const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

console.log('🚀 SFQuant 高级功能演示');
console.log('='.repeat(60));

async function demoAdvancedFeatures() {
  try {
    // 1. 演示实时WebSocket数据
    console.log('\n📊 1. 实时市场数据 (Binance WebSocket)');
    console.log('正在接收实时价格数据...');
    console.log('✅ BTC/USDT, ETH/USDT, BNB/USDT, ADA/USDT, SOL/USDT');
    console.log('💡 查看终端输出可以看到实时价格更新');

    // 2. 演示策略模板
    console.log('\n🤖 2. 策略模板系统');
    const templatesResponse = await axios.get(`${API_BASE}/advanced/strategy-templates`);
    const templates = templatesResponse.data.data;
    
    console.log(`✅ 可用策略模板: ${templates.length}个`);
    templates.forEach((template, index) => {
      console.log(`   ${index + 1}. ${template.name} (${template.type})`);
      console.log(`      风险等级: ${template.riskLevel}, 预期收益: ${template.expectedReturn}%`);
    });

    // 3. 演示技术指标计算
    console.log('\n📈 3. 技术指标计算');
    const indicatorsResponse = await axios.post(`${API_BASE}/advanced/technical-indicators`, {
      symbol: 'BTC/USDT',
      timeframe: '1h',
      limit: 50
    });
    
    if (indicatorsResponse.data.success) {
      const indicators = indicatorsResponse.data.data.indicators;
      const signals = indicatorsResponse.data.data.signals;
      
      console.log('✅ 技术指标计算完成:');
      console.log(`   SMA(20): ${indicators.sma[indicators.sma.length - 1]?.toFixed(2) || 'N/A'}`);
      console.log(`   RSI(14): ${indicators.rsi[indicators.rsi.length - 1]?.toFixed(2) || 'N/A'}`);
      console.log(`   MACD: ${indicators.macd.macd[indicators.macd.macd.length - 1]?.toFixed(4) || 'N/A'}`);
      
      console.log('\n🎯 交易信号分析:');
      console.log(`   推荐操作: ${signals.recommendation}`);
      console.log(`   信号强度: ${signals.strength.toFixed(1)}%`);
      signals.signals.forEach(signal => {
        console.log(`   - ${signal}`);
      });
    }

    // 4. 演示回测功能
    console.log('\n⏮️ 4. 策略回测');
    console.log('正在运行RSI均值回归策略回测...');
    
    const backtestResponse = await axios.post(`${API_BASE}/advanced/backtest`, {
      strategyId: 'rsi_mean_reversion',
      symbol: 'BTC/USDT',
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天前
      endDate: new Date().toISOString(),
      initialCapital: 10000,
      commission: 0.001,
      slippage: 0.0005,
      timeframe: '1h'
    });

    if (backtestResponse.data.success) {
      const result = backtestResponse.data.data;
      console.log('✅ 回测完成:');
      console.log(`   总收益率: ${result.metrics.totalReturn.toFixed(2)}%`);
      console.log(`   夏普比率: ${result.metrics.sharpeRatio.toFixed(2)}`);
      console.log(`   最大回撤: ${result.metrics.maxDrawdown.toFixed(2)}%`);
      console.log(`   胜率: ${result.metrics.winRate.toFixed(1)}%`);
      console.log(`   总交易次数: ${result.metrics.totalTrades}`);
      console.log(`   盈利交易: ${result.metrics.profitableTrades}`);
    }

    // 5. 演示价格监控
    console.log('\n🔍 5. 价格异常监控');
    
    // 添加价格提醒
    const alertResponse = await axios.post(`${API_BASE}/advanced/price-monitor/alerts`, {
      symbol: 'BTC/USDT',
      type: 'PRICE_ABOVE',
      threshold: 110000,
      isActive: true
    });
    
    if (alertResponse.data.success) {
      console.log('✅ 价格提醒已设置: BTC/USDT > $110,000');
    }

    // 获取监控统计
    const statsResponse = await axios.get(`${API_BASE}/advanced/price-monitor/stats`);
    if (statsResponse.data.success) {
      const stats = statsResponse.data.data;
      console.log('📊 监控统计:');
      console.log(`   监控交易对: ${stats.monitoredSymbols}个`);
      console.log(`   活跃提醒: ${stats.activeAlerts}个`);
      console.log(`   检测到异常: ${stats.anomaliesDetected}个`);
    }

    // 获取异常记录
    const anomaliesResponse = await axios.get(`${API_BASE}/advanced/price-monitor/anomalies?limit=5`);
    if (anomaliesResponse.data.success && anomaliesResponse.data.data.length > 0) {
      console.log('\n⚠️ 最近价格异常:');
      anomaliesResponse.data.data.slice(0, 3).forEach((anomaly, index) => {
        console.log(`   ${index + 1}. ${anomaly.description} (${anomaly.severity})`);
      });
    } else {
      console.log('✅ 暂无价格异常');
    }

    // 6. 演示性能监控
    console.log('\n⚡ 6. 系统性能监控');
    
    // 等待性能服务初始化
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    try {
      const healthResponse = await axios.get(`${API_BASE}/advanced/performance/health`);
      if (healthResponse.data.success) {
        const health = healthResponse.data.data;
        console.log('🏥 系统健康状态:');
        console.log(`   状态: ${health.status}`);
        console.log(`   健康评分: ${health.score}/100`);
        if (health.issues.length > 0) {
          console.log('   问题:');
          health.issues.forEach(issue => {
            console.log(`   - ${issue}`);
          });
        } else {
          console.log('   ✅ 系统运行正常');
        }
      }
    } catch (error) {
      console.log('⏳ 性能监控服务正在初始化...');
    }

    // 7. 演示策略性能分析
    console.log('\n📊 7. 策略性能分析');
    try {
      const performanceResponse = await axios.get(`${API_BASE}/advanced/strategy-performance/rsi_mean_reversion`);
      if (performanceResponse.data.success) {
        const performance = performanceResponse.data.data;
        console.log('✅ RSI均值回归策略性能:');
        console.log(`   总收益率: ${performance.performance.totalReturn.toFixed(2)}%`);
        console.log(`   年化收益率: ${performance.performance.annualizedReturn.toFixed(2)}%`);
        console.log(`   最大回撤: ${performance.performance.maxDrawdown.toFixed(2)}%`);
        console.log(`   最近交易: ${performance.recentTrades.length}笔`);
      }
    } catch (error) {
      console.log('⏳ 策略性能分析正在计算...');
    }

    console.log('\n🎉 高级功能演示完成!');
    console.log('\n💡 提示:');
    console.log('- 访问 http://localhost:3001/docs 查看完整API文档');
    console.log('- 访问 http://localhost:3003 使用Web界面');
    console.log('- 实时数据通过WebSocket持续更新');
    console.log('- 所有数据都来自真实的Binance API');

  } catch (error) {
    console.error('❌ 演示过程中出现错误:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行演示
demoAdvancedFeatures();

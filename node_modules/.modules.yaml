hoistPattern:
  - '*'
hoistedDependencies:
  '@adraffy/ens-normalize@1.10.1':
    '@adraffy/ens-normalize': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': private
  '@babel/core@7.27.1':
    '@babel/core': private
  '@babel/generator@7.27.1':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.1':
    '@babel/helpers': private
  '@babel/parser@7.27.2':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.1)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.1)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.1)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.1)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.1':
    '@babel/traverse': private
  '@babel/types@7.27.1':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@ethereumjs/rlp@5.0.2':
    '@ethereumjs/rlp': private
  '@ethereumjs/util@9.1.0':
    '@ethereumjs/util': private
  '@ethersproject/abi@5.8.0':
    '@ethersproject/abi': private
  '@ethersproject/abstract-provider@5.8.0':
    '@ethersproject/abstract-provider': private
  '@ethersproject/abstract-signer@5.8.0':
    '@ethersproject/abstract-signer': private
  '@ethersproject/address@5.8.0':
    '@ethersproject/address': private
  '@ethersproject/base64@5.8.0':
    '@ethersproject/base64': private
  '@ethersproject/bignumber@5.8.0':
    '@ethersproject/bignumber': private
  '@ethersproject/bytes@5.8.0':
    '@ethersproject/bytes': private
  '@ethersproject/constants@5.8.0':
    '@ethersproject/constants': private
  '@ethersproject/hash@5.8.0':
    '@ethersproject/hash': private
  '@ethersproject/keccak256@5.8.0':
    '@ethersproject/keccak256': private
  '@ethersproject/logger@5.8.0':
    '@ethersproject/logger': private
  '@ethersproject/networks@5.8.0':
    '@ethersproject/networks': private
  '@ethersproject/properties@5.8.0':
    '@ethersproject/properties': private
  '@ethersproject/rlp@5.8.0':
    '@ethersproject/rlp': private
  '@ethersproject/sha2@5.8.0':
    '@ethersproject/sha2': private
  '@ethersproject/signing-key@5.8.0':
    '@ethersproject/signing-key': private
  '@ethersproject/solidity@5.8.0':
    '@ethersproject/solidity': private
  '@ethersproject/strings@5.8.0':
    '@ethersproject/strings': private
  '@ethersproject/transactions@5.8.0':
    '@ethersproject/transactions': private
  '@ethersproject/web@5.8.0':
    '@ethersproject/web': private
  '@fastify/accept-negotiator@1.1.0':
    '@fastify/accept-negotiator': private
  '@fastify/ajv-compiler@3.6.0':
    '@fastify/ajv-compiler': private
  '@fastify/busboy@2.1.1':
    '@fastify/busboy': private
  '@fastify/cors@8.5.0':
    '@fastify/cors': private
  '@fastify/error@3.4.1':
    '@fastify/error': private
  '@fastify/fast-json-stringify-compiler@4.3.0':
    '@fastify/fast-json-stringify-compiler': private
  '@fastify/helmet@11.1.1':
    '@fastify/helmet': private
  '@fastify/jwt@7.2.4':
    '@fastify/jwt': private
  '@fastify/merge-json-schemas@0.1.1':
    '@fastify/merge-json-schemas': private
  '@fastify/rate-limit@9.1.0':
    '@fastify/rate-limit': private
  '@fastify/send@2.1.0':
    '@fastify/send': private
  '@fastify/static@6.12.0':
    '@fastify/static': private
  '@fastify/swagger-ui@2.1.0':
    '@fastify/swagger-ui': private
  '@fastify/swagger@8.15.0':
    '@fastify/swagger': private
  '@fastify/websocket@8.3.1':
    '@fastify/websocket': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3))':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@lukeed/ms@2.0.2':
    '@lukeed/ms': private
  '@noble/curves@1.2.0':
    '@noble/curves': private
  '@noble/hashes@1.3.2':
    '@noble/hashes': private
  '@noble/secp256k1@1.7.1':
    '@noble/secp256k1': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nomicfoundation/edr-darwin-arm64@0.11.0':
    '@nomicfoundation/edr-darwin-arm64': private
  '@nomicfoundation/edr-darwin-x64@0.11.0':
    '@nomicfoundation/edr-darwin-x64': private
  '@nomicfoundation/edr-linux-arm64-gnu@0.11.0':
    '@nomicfoundation/edr-linux-arm64-gnu': private
  '@nomicfoundation/edr-linux-arm64-musl@0.11.0':
    '@nomicfoundation/edr-linux-arm64-musl': private
  '@nomicfoundation/edr-linux-x64-gnu@0.11.0':
    '@nomicfoundation/edr-linux-x64-gnu': private
  '@nomicfoundation/edr-linux-x64-musl@0.11.0':
    '@nomicfoundation/edr-linux-x64-musl': private
  '@nomicfoundation/edr-win32-x64-msvc@0.11.0':
    '@nomicfoundation/edr-win32-x64-msvc': private
  '@nomicfoundation/edr@0.11.0':
    '@nomicfoundation/edr': private
  '@nomicfoundation/solidity-analyzer-darwin-arm64@0.1.2':
    '@nomicfoundation/solidity-analyzer-darwin-arm64': private
  '@nomicfoundation/solidity-analyzer-darwin-x64@0.1.2':
    '@nomicfoundation/solidity-analyzer-darwin-x64': private
  '@nomicfoundation/solidity-analyzer-linux-arm64-gnu@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-arm64-gnu': private
  '@nomicfoundation/solidity-analyzer-linux-arm64-musl@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-arm64-musl': private
  '@nomicfoundation/solidity-analyzer-linux-x64-gnu@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-x64-gnu': private
  '@nomicfoundation/solidity-analyzer-linux-x64-musl@0.1.2':
    '@nomicfoundation/solidity-analyzer-linux-x64-musl': private
  '@nomicfoundation/solidity-analyzer-win32-x64-msvc@0.1.2':
    '@nomicfoundation/solidity-analyzer-win32-x64-msvc': private
  '@nomicfoundation/solidity-analyzer@0.1.2':
    '@nomicfoundation/solidity-analyzer': private
  '@openzeppelin/contracts@3.4.2-solc-0.7':
    '@openzeppelin/contracts': private
  '@prisma/client@5.22.0(prisma@5.22.0)':
    '@prisma/client': private
  '@prisma/debug@5.22.0':
    '@prisma/debug': private
  '@prisma/engines-version@5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2':
    '@prisma/engines-version': private
  '@prisma/engines@5.22.0':
    '@prisma/engines': private
  '@prisma/fetch-engine@5.22.0':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@5.22.0':
    '@prisma/get-platform': private
  '@redis/bloom@1.2.0(@redis/client@1.6.1)':
    '@redis/bloom': private
  '@redis/client@1.6.1':
    '@redis/client': private
  '@redis/graph@1.1.1(@redis/client@1.6.1)':
    '@redis/graph': private
  '@redis/json@1.0.7(@redis/client@1.6.1)':
    '@redis/json': private
  '@redis/search@1.2.0(@redis/client@1.6.1)':
    '@redis/search': private
  '@redis/time-series@1.1.0(@redis/client@1.6.1)':
    '@redis/time-series': private
  '@scure/base@1.1.9':
    '@scure/base': private
  '@scure/bip32@1.3.2':
    '@scure/bip32': private
  '@scure/bip39@1.2.1':
    '@scure/bip39': private
  '@sentry/core@5.30.0':
    '@sentry/core': private
  '@sentry/hub@5.30.0':
    '@sentry/hub': private
  '@sentry/minimal@5.30.0':
    '@sentry/minimal': private
  '@sentry/node@5.30.0':
    '@sentry/node': private
  '@sentry/tracing@5.30.0':
    '@sentry/tracing': private
  '@sentry/types@5.30.0':
    '@sentry/types': private
  '@sentry/utils@5.30.0':
    '@sentry/utils': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/bcryptjs@2.4.6':
    '@types/bcryptjs': private
  '@types/bn.js@5.1.6':
    '@types/bn.js': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@29.5.14':
    '@types/jest': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/jsonwebtoken@9.0.9':
    '@types/jsonwebtoken': private
  '@types/lru-cache@5.1.1':
    '@types/lru-cache': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node-cron@3.0.11':
    '@types/node-cron': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@uniswap/lib@4.0.1-alpha':
    '@uniswap/lib': private
  '@uniswap/sdk-core@4.2.1':
    '@uniswap/sdk-core': private
  '@uniswap/swap-router-contracts@1.3.1(hardhat@2.24.0(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3))(typescript@5.8.3))':
    '@uniswap/swap-router-contracts': private
  '@uniswap/v2-core@1.0.1':
    '@uniswap/v2-core': private
  '@uniswap/v2-sdk@3.3.0':
    '@uniswap/v2-sdk': private
  '@uniswap/v3-core@1.0.1':
    '@uniswap/v3-core': private
  '@uniswap/v3-periphery@1.4.4':
    '@uniswap/v3-periphery': private
  '@uniswap/v3-sdk@3.25.2(hardhat@2.24.0(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3))(typescript@5.8.3))':
    '@uniswap/v3-sdk': private
  '@uniswap/v3-staker@1.0.0':
    '@uniswap/v3-staker': private
  abitype@0.9.8(typescript@5.8.3)(zod@3.25.28):
    abitype: private
  abstract-logging@2.0.1:
    abstract-logging: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  adm-zip@0.4.16:
    adm-zip: private
  aes-js@4.0.0-beta.5:
    aes-js: private
  agent-base@6.0.2:
    agent-base: private
  aggregate-error@3.1.0:
    aggregate-error: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv@6.12.6:
    ajv: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  apps/api:
    '@sfquant/api': private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-union@2.1.0:
    array-union: private
  asn1.js@5.4.1:
    asn1.js: private
  asynckit@0.4.0:
    asynckit: private
  atomic-sleep@1.0.0:
    atomic-sleep: private
  avvio@8.4.0:
    avvio: private
  axios@1.9.0:
    axios: private
  babel-jest@29.7.0(@babel/core@7.27.1):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.1):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.27.1):
    babel-preset-jest: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-sol@1.0.1:
    base64-sol: private
  bcryptjs@2.4.3:
    bcryptjs: private
  big.js@5.2.2:
    big.js: private
  bignumber.js@9.3.0:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bn.js@5.2.2:
    bn.js: private
  boxen@5.1.2:
    boxen: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  brorand@1.1.0:
    brorand: private
  browser-stdout@1.3.1:
    browser-stdout: private
  browserslist@4.24.5:
    browserslist: private
  bser@2.1.1:
    bser: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  ccxt@4.4.85:
    ccxt: private
  chalk@4.1.2:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  chokidar@3.6.0:
    chokidar: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  clean-stack@2.2.0:
    clean-stack: private
  cli-boxes@2.2.1:
    cli-boxes: private
  cliui@8.0.1:
    cliui: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  co@4.6.0:
    co: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  command-exists@1.2.9:
    command-exists: private
  commander@8.3.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@0.5.4:
    content-disposition: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@0.7.2:
    cookie: private
  create-jest@29.7.0(@types/node@20.17.50)(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3)):
    create-jest: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  debug@4.4.1(supports-color@8.1.1):
    debug: private
  decamelize@4.0.0:
    decamelize: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  dedent@1.6.0:
    dedent: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  detect-newline@3.1.0:
    detect-newline: private
  diff-sequences@29.6.3:
    diff-sequences: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dotenv@14.3.2:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  electron-to-chromium@1.5.157:
    electron-to-chromium: private
  elliptic@6.6.1:
    elliptic: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  enquirer@2.4.1:
    enquirer: private
  env-paths@2.2.1:
    env-paths: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.25.4:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  ethereum-cryptography@1.2.0:
    ethereum-cryptography: private
  ethers@6.14.1:
    ethers: private
  execa@5.1.1:
    execa: private
  exit@0.1.2:
    exit: private
  expect@29.7.0:
    expect: private
  fast-content-type-parse@1.1.0:
    fast-content-type-parse: private
  fast-decode-uri-component@1.0.1:
    fast-decode-uri-component: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-json-stringify@5.16.1:
    fast-json-stringify: private
  fast-jwt@3.3.3:
    fast-jwt: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-querystring@1.1.2:
    fast-querystring: private
  fast-redact@3.5.0:
    fast-redact: private
  fast-uri@2.4.0:
    fast-uri: private
  fastfall@1.5.1:
    fastfall: private
  fastify-plugin@4.5.1:
    fastify-plugin: private
  fastify@4.29.1:
    fastify: private
  fastparallel@2.4.1:
    fastparallel: private
  fastq@1.19.1:
    fastq: private
  fastseries@1.7.2:
    fastseries: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-my-way@8.2.2:
    find-my-way: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flat@5.0.2:
    flat: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9(debug@4.4.1):
    follow-redirects: private
  form-data@4.0.2:
    form-data: private
  forwarded@0.2.0:
    forwarded: private
  fp-ts@1.19.3:
    fp-ts: private
  fs-extra@7.0.1:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  generic-pool@3.9.0:
    generic-pool: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@6.0.1:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@8.1.0:
    glob: private
  globals@13.24.0:
    globals: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  hardhat-watcher@2.5.0(hardhat@2.24.0(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3))(typescript@5.8.3)):
    hardhat-watcher: private
  hardhat@2.24.0(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3))(typescript@5.8.3):
    hardhat: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hash.js@1.1.7:
    hash.js: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  helmet@7.2.0:
    helmet: private
  hmac-drbg@1.0.1:
    hmac-drbg: private
  html-escaper@2.0.2:
    html-escaper: private
  http-errors@2.0.0:
    http-errors: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  immutable@4.3.7:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  io-ts@1.10.4:
    io-ts: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@2.1.0:
    is-plain-obj: private
  is-stream@2.0.1:
    is-stream: private
  is-unicode-supported@0.1.0:
    is-unicode-supported: private
  isexe@2.0.0:
    isexe: private
  isows@1.0.3(ws@8.13.0):
    isows: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0:
    jest-circus: private
  jest-cli@29.7.0(@types/node@20.17.50)(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3)):
    jest-cli: private
  jest-config@29.7.0(@types/node@20.17.50)(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3)):
    jest-config: private
  jest-diff@29.7.0:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@29.7.0:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jest@29.7.0(@types/node@20.17.50)(ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3)):
    jest: private
  js-sha3@0.8.0:
    js-sha3: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbi@3.2.5:
    jsbi: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-ref-resolver@1.0.1:
    json-schema-ref-resolver: private
  json-schema-resolver@2.0.0:
    json-schema-resolver: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stream-stringify@3.1.6:
    json-stream-stringify: private
  json5@2.2.3:
    json5: private
  jsonfile@4.0.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keccak@3.0.4:
    keccak: private
  keyv@4.5.4:
    keyv: private
  kleur@3.0.3:
    kleur: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  light-my-request@5.14.0:
    light-my-request: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@6.0.0:
    locate-path: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  lru-cache@5.1.1:
    lru-cache: private
  lru_map@0.3.3:
    lru_map: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  memorystream@0.3.1:
    memorystream: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micro-eth-signer@0.14.0:
    micro-eth-signer: private
  micro-packed@0.7.3:
    micro-packed: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@3.0.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimalistic-assert@1.0.1:
    minimalistic-assert: private
  minimalistic-crypto-utils@1.0.1:
    minimalistic-crypto-utils: private
  minimatch@3.1.2:
    minimatch: private
  mnemonist@0.39.6:
    mnemonist: private
  mocha@10.8.2:
    mocha: private
  ms@2.1.3:
    ms: private
  natural-compare@1.4.0:
    natural-compare: private
  node-addon-api@2.0.2:
    node-addon-api: private
  node-cron@3.0.3:
    node-cron: private
  node-gyp-build@4.8.4:
    node-gyp-build: private
  node-int64@0.4.0:
    node-int64: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  obliterator@2.0.5:
    obliterator: private
  on-exit-leak-free@2.1.2:
    on-exit-leak-free: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  openapi-types@12.1.3:
    openapi-types: private
  optionator@0.9.4:
    optionator: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@4.0.0:
    p-map: private
  p-try@2.2.0:
    p-try: private
  packages/ccxt-adapter:
    '@sfquant/ccxt-adapter': private
  packages/core:
    '@sfquant/core': private
  packages/dex-adapter:
    '@sfquant/dex-adapter': private
  packages/strategy-runtime:
    '@sfquant/strategy-runtime': private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-type@4.0.0:
    path-type: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pino-abstract-transport@2.0.0:
    pino-abstract-transport: private
  pino-std-serializers@7.0.0:
    pino-std-serializers: private
  pino@9.7.0:
    pino: private
  pirates@4.0.7:
    pirates: private
  pkg-dir@4.2.0:
    pkg-dir: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-format@29.7.0:
    pretty-format: private
  prisma@5.22.0:
    prisma: private
  process-warning@3.0.0:
    process-warning: private
  prompts@2.4.2:
    prompts: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quick-format-unescaped@4.0.4:
    quick-format-unescaped: private
  randombytes@2.1.0:
    randombytes: private
  raw-body@2.5.2:
    raw-body: private
  react-is@18.3.1:
    react-is: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  real-require@0.2.0:
    real-require: private
  redis@4.7.1:
    redis: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  ret@0.4.3:
    ret: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  rimraf@3.0.2:
    rimraf: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex2@3.1.0:
    safe-regex2: private
  safe-stable-stringify@2.5.0:
    safe-stable-stringify: private
  safer-buffer@2.1.2:
    safer-buffer: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@7.7.2:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-cookie-parser@2.7.1:
    set-cookie-parser: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@3.0.7:
    signal-exit: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  solc@0.8.26(debug@4.4.1):
    solc: private
  sonic-boom@4.2.0:
    sonic-boom: private
  source-map-support@0.5.13:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.0.3:
    sprintf-js: private
  stack-utils@2.0.6:
    stack-utils: private
  stacktrace-parser@0.1.11:
    stacktrace-parser: private
  statuses@2.0.1:
    statuses: private
  steed@1.1.3:
    steed: private
  string-length@4.0.2:
    string-length: private
  string-width@4.2.3:
    string-width: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-bom@4.0.0:
    strip-bom: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  test-exclude@6.0.0:
    test-exclude: private
  text-table@0.2.0:
    text-table: private
  thread-stream@3.1.0:
    thread-stream: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinyglobby@0.2.14:
    tinyglobby: private
  tmp@0.0.33:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toad-cache@3.7.0:
    toad-cache: private
  toformat@2.0.0:
    toformat: private
  toidentifier@1.0.1:
    toidentifier: private
  tools/typescript-config:
    '@sfquant/typescript-config': private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  ts-node@10.9.2(@types/node@20.17.50)(typescript@5.8.3):
    ts-node: private
  tslib@2.7.0:
    tslib: private
  tsort@0.0.1:
    tsort: private
  tsx@4.19.4:
    tsx: private
  turbo-darwin-64@1.13.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@1.13.4:
    turbo-darwin-arm64: private
  turbo-linux-64@1.13.4:
    turbo-linux-64: private
  turbo-linux-arm64@1.13.4:
    turbo-linux-arm64: private
  turbo-windows-64@1.13.4:
    turbo-windows-64: private
  turbo-windows-arm64@1.13.4:
    turbo-windows-arm64: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@0.20.2:
    type-fest: private
  undici-types@6.19.8:
    undici-types: private
  undici@5.29.0:
    undici: private
  universalify@0.1.2:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@8.3.2:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  viem@1.21.4(typescript@5.8.3)(zod@3.25.28):
    viem: private
  vm2@3.9.19:
    vm2: private
  walker@1.0.8:
    walker: private
  which@2.0.2:
    which: private
  widest-line@3.1.0:
    widest-line: private
  word-wrap@1.2.5:
    word-wrap: private
  workerpool@6.5.1:
    workerpool: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.2:
    ws: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs-unparser@2.0.0:
    yargs-unparser: private
  yargs@17.7.2:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod@3.25.28:
    zod: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.3
pendingBuilds: []
prunedAt: Mon, 26 May 2025 01:36:49 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-x64@0.25.4'
  - turbo-darwin-64@1.13.4
  - turbo-linux-64@1.13.4
  - turbo-linux-arm64@1.13.4
  - turbo-windows-64@1.13.4
  - turbo-windows-arm64@1.13.4
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120

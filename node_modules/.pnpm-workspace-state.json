{"lastValidatedTimestamp": 1748224054149, "projects": {"/Users/<USER>/CursorSpace/SFQuant": {"name": "sfquant", "version": "1.0.0"}, "/Users/<USER>/CursorSpace/SFQuant/apps/api": {"name": "@sfquant/api", "version": "1.0.0"}, "/Users/<USER>/CursorSpace/SFQuant/packages/ccxt-adapter": {"name": "@sfquant/ccxt-adapter", "version": "1.0.0"}, "/Users/<USER>/CursorSpace/SFQuant/packages/core": {"name": "@sfquant/core", "version": "1.0.0"}, "/Users/<USER>/CursorSpace/SFQuant/packages/dex-adapter": {"name": "@sfquant/dex-adapter", "version": "1.0.0"}, "/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime": {"name": "@sfquant/strategy-runtime", "version": "1.0.0"}, "/Users/<USER>/CursorSpace/SFQuant/tools/typescript-config": {"name": "@sfquant/typescript-config", "version": "1.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["packages/*", "apps/*", "tools/*"]}, "filteredInstall": false}
{"version": 3, "file": "index.js", "sources": ["../../../../src/utils.ts", "../../../../src/copier.ts", "../../../../src/index.ts"], "sourcesContent": ["export interface Cache {\n  has: (value: any) => boolean;\n  set: (key: any, value: any) => void;\n  get: (key: any) => any;\n}\n\nconst { toString: toStringFunction } = Function.prototype;\nconst { create } = Object;\nconst { toString: toStringObject } = Object.prototype;\n\n/**\n * @classdesc Fallback cache for when WeakMap is not natively supported\n */\nclass LegacyCache {\n  private _keys: any[] = [];\n  private _values: any[] = [];\n\n  has(key: any) {\n    return !!~this._keys.indexOf(key);\n  }\n\n  get(key: any) {\n    return this._values[this._keys.indexOf(key)];\n  }\n\n  set(key: any, value: any) {\n    this._keys.push(key);\n    this._values.push(value);\n  }\n}\n\nfunction createCacheLegacy(): Cache {\n  return new LegacyCache();\n}\n\nfunction createCacheModern(): Cache {\n  return new WeakMap();\n}\n\n/**\n * Get a new cache object to prevent circular references.\n */\nexport const createCache =\n  typeof WeakMap !== 'undefined' ? createCacheModern : createCacheLegacy;\n\n/**\n * Get an empty version of the object with the same prototype it has.\n */\nexport function getCleanClone(prototype: any): any {\n  if (!prototype) {\n    return create(null);\n  }\n\n  const Constructor = prototype.constructor;\n\n  if (Constructor === Object) {\n    return prototype === Object.prototype ? {} : create(prototype);\n  }\n\n  if (\n    Constructor &&\n    ~toStringFunction.call(Constructor).indexOf('[native code]')\n  ) {\n    try {\n      return new Constructor();\n    } catch {}\n  }\n\n  return create(prototype);\n}\n\nfunction getRegExpFlagsLegacy(regExp: RegExp): string {\n  let flags = '';\n\n  if (regExp.global) {\n    flags += 'g';\n  }\n\n  if (regExp.ignoreCase) {\n    flags += 'i';\n  }\n\n  if (regExp.multiline) {\n    flags += 'm';\n  }\n\n  if (regExp.unicode) {\n    flags += 'u';\n  }\n\n  if (regExp.sticky) {\n    flags += 'y';\n  }\n\n  return flags;\n}\n\nfunction getRegExpFlagsModern(regExp: RegExp): string {\n  return regExp.flags;\n}\n\n/**\n * Get the flags to apply to the copied regexp.\n */\nexport const getRegExpFlags =\n  /test/g.flags === 'g' ? getRegExpFlagsModern : getRegExpFlagsLegacy;\n\nfunction getTagLegacy(value: any): string {\n  const type = toStringObject.call(value);\n\n  return type.substring(8, type.length - 1);\n}\n\nfunction getTagModern(value: any): string {\n  return value[Symbol.toStringTag] || getTagLegacy(value);\n}\n\n/**\n * Get the tag of the value passed, so that the correct copier can be used.\n */\nexport const getTag =\n  typeof Symbol !== 'undefined' ? getTagModern : getTagLegacy;\n", "import { getCleanClone, getRegExpFlags } from './utils';\n\nimport type { Cache } from './utils';\n\nexport type InternalCopier<Value> = (value: Value, state: State) => Value;\n\nexport interface State {\n  Constructor: any;\n  cache: Cache;\n  copier: InternalCopier<any>;\n  prototype: any;\n}\n\nconst {\n  defineProperty,\n  getOwnPropertyDescriptor,\n  getOwnPropertyNames,\n  getOwnPropertySymbols,\n} = Object;\nconst { hasOwnProperty, propertyIsEnumerable } = Object.prototype;\n\nconst SUPPORTS_SYMBOL = typeof getOwnPropertySymbols === 'function';\n\nfunction getStrictPropertiesModern(object: any): Array<string | symbol> {\n  return (getOwnPropertyNames(object) as Array<string | symbol>).concat(\n    getOwnPropertySymbols(object)\n  );\n}\n\n/**\n * Get the properites used when copying objects strictly. This includes both keys and symbols.\n */\nconst getStrictProperties = SUPPORTS_SYMBOL\n  ? getStrictPropertiesModern\n  : getOwnPropertyNames;\n\n/**\n * Striclty copy all properties contained on the object.\n */\nfunction copyOwnPropertiesStrict<Value>(\n  value: Value,\n  clone: Value,\n  state: State\n): Value {\n  const properties = getStrictProperties(value);\n\n  for (\n    let index = 0, length = properties.length, property, descriptor;\n    index < length;\n    ++index\n  ) {\n    property = properties[index];\n\n    if (property === 'callee' || property === 'caller') {\n      continue;\n    }\n\n    descriptor = getOwnPropertyDescriptor(value, property);\n\n    if (!descriptor) {\n      // In extra edge cases where the property descriptor cannot be retrived, fall back to\n      // the loose assignment.\n      (clone as any)[property] = state.copier((value as any)[property], state);\n      continue;\n    }\n\n    // Only clone the value if actually a value, not a getter / setter.\n    if (!descriptor.get && !descriptor.set) {\n      descriptor.value = state.copier(descriptor.value, state);\n    }\n\n    try {\n      defineProperty(clone, property, descriptor);\n    } catch (error) {\n      // Tee above can fail on node in edge cases, so fall back to the loose assignment.\n      (clone as any)[property] = descriptor.value;\n    }\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the indexed values in the array.\n */\nexport function copyArrayLoose(array: any[], state: State) {\n  const clone = new state.Constructor();\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(array, clone);\n\n  for (let index = 0, length = array.length; index < length; ++index) {\n    clone[index] = state.copier(array[index], state);\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the indexed values in the array, as well as any custom properties.\n */\nexport function copyArrayStrict<Value extends any[]>(\n  array: Value,\n  state: State\n) {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(array, clone);\n\n  return copyOwnPropertiesStrict(array, clone, state);\n}\n\n/**\n * Copy the contents of the ArrayBuffer.\n */\nexport function copyArrayBuffer<Value extends ArrayBuffer>(\n  arrayBuffer: Value,\n  _state: State\n): Value {\n  return arrayBuffer.slice(0) as Value;\n}\n\n/**\n * Create a new Blob with the contents of the original.\n */\nexport function copyBlob<Value extends Blob>(\n  blob: Value,\n  _state: State\n): Value {\n  return blob.slice(0, blob.size, blob.type) as Value;\n}\n\n/**\n * Create a new DataView with the contents of the original.\n */\nexport function copyDataView<Value extends DataView>(\n  dataView: Value,\n  state: State\n): Value {\n  return new state.Constructor(copyArrayBuffer(dataView.buffer, state));\n}\n\n/**\n * Create a new Date based on the time of the original.\n */\nexport function copyDate<Value extends Date>(date: Value, state: State): Value {\n  return new state.Constructor(date.getTime());\n}\n\n/**\n * Deeply copy the keys and values of the original.\n */\nexport function copyMapLoose<Value extends Map<any, any>>(\n  map: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(map, clone);\n\n  map.forEach((value, key) => {\n    clone.set(key, state.copier(value, state));\n  });\n\n  return clone;\n}\n\n/**\n * Deeply copy the keys and values of the original, as well as any custom properties.\n */\nexport function copyMapStrict<Value extends Map<any, any>>(\n  map: Value,\n  state: State\n) {\n  return copyOwnPropertiesStrict(map, copyMapLoose(map, state), state);\n}\n\nfunction copyObjectLooseLegacy<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone: any = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  for (const key in object) {\n    if (hasOwnProperty.call(object, key)) {\n      clone[key] = state.copier(object[key], state);\n    }\n  }\n\n  return clone;\n}\n\nfunction copyObjectLooseModern<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  for (const key in object) {\n    if (hasOwnProperty.call(object, key)) {\n      clone[key] = state.copier(object[key], state);\n    }\n  }\n\n  const symbols = getOwnPropertySymbols(object);\n\n  for (\n    let index = 0, length = symbols.length, symbol;\n    index < length;\n    ++index\n  ) {\n    symbol = symbols[index];\n\n    if (propertyIsEnumerable.call(object, symbol)) {\n      clone[symbol] = state.copier((object as any)[symbol], state);\n    }\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the properties (keys and symbols) and values of the original.\n */\nexport const copyObjectLoose = SUPPORTS_SYMBOL\n  ? copyObjectLooseModern\n  : copyObjectLooseLegacy;\n\n/**\n * Deeply copy the properties (keys and symbols) and values of the original, as well\n * as any hidden or non-enumerable properties.\n */\nexport function copyObjectStrict<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  return copyOwnPropertiesStrict(object, clone, state);\n}\n\n/**\n * Create a new primitive wrapper from the value of the original.\n */\nexport function copyPrimitiveWrapper<\n  // Specifically use the object constructor types\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  Value extends Boolean | Number | String\n>(primitiveObject: Value, state: State): Value {\n  return new state.Constructor(primitiveObject.valueOf());\n}\n\n/**\n * Create a new RegExp based on the value and flags of the original.\n */\nexport function copyRegExp<Value extends RegExp>(\n  regExp: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor(\n    regExp.source,\n    getRegExpFlags(regExp)\n  ) as Value;\n\n  clone.lastIndex = regExp.lastIndex;\n\n  return clone;\n}\n\n/**\n * Return the original value (an identity function).\n *\n * @note\n * THis is used for objects that cannot be copied, such as WeakMap.\n */\nexport function copySelf<Value>(value: Value, _state: State): Value {\n  return value;\n}\n\n/**\n * Deeply copy the values of the original.\n */\nexport function copySetLoose<Value extends Set<any>>(\n  set: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(set, clone);\n\n  set.forEach((value) => {\n    clone.add(state.copier(value, state));\n  });\n\n  return clone;\n}\n\n/**\n * Deeply copy the values of the original, as well as any custom properties.\n */\nexport function copySetStrict<Value extends Set<any>>(\n  set: Value,\n  state: State\n): Value {\n  return copyOwnPropertiesStrict(set, copySetLoose(set, state), state);\n}\n", "import {\n  copyArrayBuffer,\n  copyArrayLoose,\n  copyArrayStrict,\n  copyBlob,\n  copyDataView,\n  copyDate,\n  copyMapLoose,\n  copyMapStrict,\n  copyObjectLoose,\n  copyObjectStrict,\n  copyPrimitiveWrapper,\n  copyRegExp,\n  copySelf,\n  copySetLoose,\n  copySetStrict,\n} from './copier';\nimport { createCache, getTag } from './utils';\n\nimport type { InternalCopier, State } from './copier';\n\nexport type { State } from './copier';\n\nconst { isArray } = Array;\nconst { assign } = Object;\nconst getPrototypeOf = Object.getPrototypeOf || ((obj) => obj.__proto__)\n\nexport interface CreateCopierOptions {\n  array?: InternalCopier<any[]>;\n  arrayBuffer?: InternalCopier<ArrayBuffer>;\n  blob?: InternalCopier<Blob>;\n  dataView?: InternalCopier<DataView>;\n  date?: InternalCopier<Date>;\n  error?: InternalCopier<any>;\n  map?: InternalCopier<Map<any, any>>;\n  object?: InternalCopier<Record<string, any>>;\n  regExp?: InternalCopier<RegExp>;\n  set?: InternalCopier<Set<any>>;\n}\n\nconst DEFAULT_LOOSE_OPTIONS: Required<CreateCopierOptions> = {\n  array: copyArrayLoose,\n  arrayBuffer: copyArrayBuffer,\n  blob: copyBlob,\n  dataView: copyDataView,\n  date: copyDate,\n  error: copySelf,\n  map: copyMapLoose,\n  object: copyObjectLoose,\n  regExp: copyRegExp,\n  set: copySetLoose,\n};\nconst DEFAULT_STRICT_OPTIONS: Required<CreateCopierOptions> = assign(\n  {},\n  DEFAULT_LOOSE_OPTIONS,\n  {\n    array: copyArrayStrict,\n    map: copyMapStrict,\n    object: copyObjectStrict,\n    set: copySetStrict,\n  }\n);\n\n/**\n * Get the copiers used for each specific object tag.\n */\nfunction getTagSpecificCopiers(\n  options: Required<CreateCopierOptions>\n): Record<string, InternalCopier<any>> {\n  return {\n    Arguments: options.object,\n    Array: options.array,\n    ArrayBuffer: options.arrayBuffer,\n    Blob: options.blob,\n    Boolean: copyPrimitiveWrapper,\n    DataView: options.dataView,\n    Date: options.date,\n    Error: options.error,\n    Float32Array: options.arrayBuffer,\n    Float64Array: options.arrayBuffer,\n    Int8Array: options.arrayBuffer,\n    Int16Array: options.arrayBuffer,\n    Int32Array: options.arrayBuffer,\n    Map: options.map,\n    Number: copyPrimitiveWrapper,\n    Object: options.object,\n    Promise: copySelf,\n    RegExp: options.regExp,\n    Set: options.set,\n    String: copyPrimitiveWrapper,\n    WeakMap: copySelf,\n    WeakSet: copySelf,\n    Uint8Array: options.arrayBuffer,\n    Uint8ClampedArray: options.arrayBuffer,\n    Uint16Array: options.arrayBuffer,\n    Uint32Array: options.arrayBuffer,\n    Uint64Array: options.arrayBuffer,\n  };\n}\n\n/**\n * Create a custom copier based on the object-specific copy methods passed.\n */\nexport function createCopier(options: CreateCopierOptions) {\n  const normalizedOptions = assign({}, DEFAULT_LOOSE_OPTIONS, options);\n  const tagSpecificCopiers = getTagSpecificCopiers(normalizedOptions);\n  const { Array: array, Object: object } = tagSpecificCopiers;\n\n  function copier(value: any, state: State): any {\n    state.prototype = state.Constructor = undefined;\n\n    if (!value || typeof value !== 'object') {\n      return value;\n    }\n\n    if (state.cache.has(value)) {\n      return state.cache.get(value);\n    }\n\n    state.prototype = getPrototypeOf(value);\n    state.Constructor = state.prototype && state.prototype.constructor;\n\n    // plain objects\n    if (!state.Constructor || state.Constructor === Object) {\n      return object(value, state);\n    }\n\n    // arrays\n    if (isArray(value)) {\n      return array(value, state);\n    }\n\n    const tagSpecificCopier = tagSpecificCopiers[getTag(value)];\n\n    if (tagSpecificCopier) {\n      return tagSpecificCopier(value, state);\n    }\n\n    return typeof value.then === 'function' ? value : object(value, state);\n  }\n\n  return function copy<Value>(value: Value): Value {\n    return copier(value, {\n      Constructor: undefined,\n      cache: createCache(),\n      copier,\n      prototype: undefined,\n    });\n  };\n}\n\n/**\n * Create a custom copier based on the object-specific copy methods passed, defaulting to the\n * same internals as `copyStrict`.\n */\nexport function createStrictCopier(options: CreateCopierOptions) {\n  return createCopier(assign({}, DEFAULT_STRICT_OPTIONS, options));\n}\n\n/**\n * Copy an value deeply as much as possible, where strict recreation of object properties\n * are maintained. All properties (including non-enumerable ones) are copied with their\n * original property descriptors on both objects and arrays.\n */\nexport const copyStrict = createStrictCopier({});\n\n/**\n * Copy an value deeply as much as possible.\n */\nexport default createCopier({});\n"], "names": ["toStringFunction", "Function", "prototype", "create", "Object", "toStringObject", "LegacyCache", "this", "_keys", "_values", "has", "key", "indexOf", "get", "set", "value", "push", "createCache", "WeakMap", "getCleanClone", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "call", "_a", "getRegExpFlags", "flags", "regExp", "global", "ignoreCase", "multiline", "unicode", "sticky", "getTagLegacy", "type", "substring", "length", "getTag", "Symbol", "toStringTag", "defineProperty", "getOwnPropertyDescriptor", "getOwnPropertyNames", "getOwnPropertySymbols", "hasOwnProperty", "propertyIsEnumerable", "SUPPORTS_SYMBOL", "getStrictProperties", "object", "concat", "copyOwnPropertiesStrict", "clone", "state", "properties", "index", "length_1", "property", "descriptor", "copier", "error", "copyArrayBuffer", "arrayBuffer", "_state", "slice", "copyMapLoose", "map", "cache", "for<PERSON>ach", "copyObjectLoose", "symbols", "length_3", "symbol", "copyPrimitiveWrapper", "primitiveObject", "valueOf", "copySelf", "copySetLoose", "add", "isArray", "Array", "assign", "getPrototypeOf", "obj", "__proto__", "DEFAULT_LOOSE_OPTIONS", "array", "length_2", "blob", "size", "dataView", "buffer", "date", "getTime", "source", "lastIndex", "DEFAULT_STRICT_OPTIONS", "createCopier", "options", "tagSpecificCopiers", "Arguments", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "Boolean", "DataView", "Date", "Error", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Map", "Number", "Promise", "RegExp", "Set", "String", "WeakSet", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "Uint64Array", "getTagSpecificCopiers", "undefined", "tagSpecificCopier", "then", "createStrictCopier", "copyStrict"], "mappings": "oPAMQ,IAAUA,EAAqBC,SAASC,mBACxCC,EAAWC,OAAMD,OACPE,EAAmBD,OAAOF,mBAK5CI,EAAA,WAAA,SAAAA,IACUC,KAAKC,MAAU,GACfD,KAAOE,QAAU,EAc1B,CAAD,OAZEH,EAAGJ,UAAAQ,IAAH,SAAIC,GACF,SAAUJ,KAAKC,MAAMI,QAAQD,IAG/BL,EAAGJ,UAAAW,IAAH,SAAIF,GACF,OAAOJ,KAAKE,QAAQF,KAAKC,MAAMI,QAAQD,KAGzCL,EAAAJ,UAAAY,IAAA,SAAIH,EAAUI,GACZR,KAAKC,MAAMQ,KAAKL,GAChBJ,KAAKE,QAAQO,KAAKD,IAErBT,CAAD,IAaO,IAAMW,EACQ,oBAAZC,QART,WACE,OAAO,IAAIA,OACb,EANA,WACE,OAAO,IAAIZ,CACb,EAeM,SAAUa,EAAcjB,GAC5B,IAAKA,EACH,OAAOC,EAAO,MAGhB,IAAMiB,EAAclB,EAAUmB,YAE9B,GAAID,IAAgBhB,OAClB,OAAOF,IAAcE,OAAOF,UAAY,CAAA,EAAKC,EAAOD,GAGtD,GACEkB,IACCpB,EAAiBsB,KAAKF,GAAaR,QAAQ,iBAE5C,IACE,OAAO,IAAIQ,CACZ,CAAC,MAAAG,GAAQ,CAGZ,OAAOpB,EAAOD,EAChB,CAmCO,IAAMsB,EACO,MAAlB,QAAQC,MARV,SAA8BC,GAC5B,OAAOA,EAAOD,KAChB,EA5BA,SAA8BC,GAC5B,IAAID,EAAQ,GAsBZ,OApBIC,EAAOC,SACTF,GAAS,KAGPC,EAAOE,aACTH,GAAS,KAGPC,EAAOG,YACTJ,GAAS,KAGPC,EAAOI,UACTL,GAAS,KAGPC,EAAOK,SACTN,GAAS,KAGJA,CACT,EAYA,SAASO,EAAajB,GACpB,IAAMkB,EAAO5B,EAAeiB,KAAKP,GAEjC,OAAOkB,EAAKC,UAAU,EAAGD,EAAKE,OAAS,EACzC,CASO,IAAMC,EACO,oBAAXC,OART,SAAsBtB,GACpB,OAAOA,EAAMsB,OAAOC,cAAgBN,EAAajB,EACnD,EAMiDiB,EC3G/CO,EAIEnC,sBAHFoC,EAGEpC,OAAMoC,yBAFRC,EAEErC,OAFiBqC,oBACnBC,EACEtC,6BACEmB,EAA2CnB,OAAOF,UAAhDyC,EAAcpB,EAAAoB,eAAEC,EAAoBrB,EAAAqB,qBAEtCC,EAAmD,mBAA1BH,EAW/B,IAAMI,EAAsBD,EAT5B,SAAmCE,GACjC,OAAQN,EAAoBM,GAAmCC,OAC7DN,EAAsBK,GAE1B,EAOIN,EAKJ,SAASQ,EACPlC,EACAmC,EACAC,GAIA,IAFA,IAAMC,EAAaN,EAAoB/B,GAGjCsC,EAAQ,EAAGC,EAASF,EAAWjB,OAAQoB,OAAQ,EAAEC,OAAU,EAC/DH,EAAQC,IACND,EAIF,GAAiB,YAFjBE,EAAWH,EAAWC,KAEoB,WAAbE,EAM7B,GAFAC,EAAahB,EAAyBzB,EAAOwC,GAE7C,CAQKC,EAAW3C,KAAQ2C,EAAW1C,MACjC0C,EAAWzC,MAAQoC,EAAMM,OAAOD,EAAWzC,MAAOoC,IAGpD,IACEZ,EAAeW,EAAOK,EAAUC,EACjC,CAAC,MAAOE,GAENR,EAAcK,GAAYC,EAAWzC,KACvC,CAZA,MAFEmC,EAAcK,GAAYJ,EAAMM,OAAQ1C,EAAcwC,GAAWJ,GAiBtE,OAAOD,CACT,CAoCgB,SAAAS,EACdC,EACAC,GAEA,OAAOD,EAAYE,MAAM,EAC3B,CAgCgB,SAAAC,EACdC,EACAb,GAEA,IAAMD,EAAQ,IAAIC,EAAM/B,YASxB,OANA+B,EAAMc,MAAMnD,IAAIkD,EAAKd,GAErBc,EAAIE,SAAQ,SAACnD,EAAOJ,GAClBuC,EAAMpC,IAAIH,EAAKwC,EAAMM,OAAO1C,EAAOoC,GACrC,IAEOD,CACT,CAiEO,IAAMiB,EAAkBtB,EAnC/B,SACEE,EACAI,GAEA,IAAMD,EAAQ/B,EAAcgC,EAAMjD,WAKlC,IAAK,IAAMS,KAFXwC,EAAMc,MAAMnD,IAAIiC,EAAQG,GAENH,EACZJ,EAAerB,KAAKyB,EAAQpC,KAC9BuC,EAAMvC,GAAOwC,EAAMM,OAAOV,EAAOpC,GAAMwC,IAM3C,IAFA,IAAMiB,EAAU1B,EAAsBK,GAGhCM,EAAQ,EAAGgB,EAASD,EAAQjC,OAAQmC,OAAM,EAC9CjB,EAAQgB,IACNhB,EAEFiB,EAASF,EAAQf,GAEbT,EAAqBtB,KAAKyB,EAAQuB,KACpCpB,EAAMoB,GAAUnB,EAAMM,OAAQV,EAAeuB,GAASnB,IAI1D,OAAOD,CACT,EAhDA,SACEH,EACAI,GAEA,IAAMD,EAAa/B,EAAcgC,EAAMjD,WAKvC,IAAK,IAAMS,KAFXwC,EAAMc,MAAMnD,IAAIiC,EAAQG,GAENH,EACZJ,EAAerB,KAAKyB,EAAQpC,KAC9BuC,EAAMvC,GAAOwC,EAAMM,OAAOV,EAAOpC,GAAMwC,IAI3C,OAAOD,CACT,EA4DgB,SAAAqB,EAIdC,EAAwBrB,GACxB,OAAO,IAAIA,EAAM/B,YAAYoD,EAAgBC,UAC/C,CAyBgB,SAAAC,EAAgB3D,EAAc8C,GAC5C,OAAO9C,CACT,CAKgB,SAAA4D,EACd7D,EACAqC,GAEA,IAAMD,EAAQ,IAAIC,EAAM/B,YASxB,OANA+B,EAAMc,MAAMnD,IAAIA,EAAKoC,GAErBpC,EAAIoD,SAAQ,SAACnD,GACXmC,EAAM0B,IAAIzB,EAAMM,OAAO1C,EAAOoC,GAChC,IAEOD,CACT,CC5RQ,IAAA2B,EAAYC,MAAKD,QACjBE,EAAW3E,OAAM2E,OACnBC,EAAiB5E,OAAO4E,yBAAoBC,GAAQ,OAAAA,EAAIC,SAAS,EAejEC,EAAuD,CAC3DC,MD4Cc,SAAeA,EAAcjC,GAC3C,IAAMD,EAAQ,IAAIC,EAAM/B,YAGxB+B,EAAMc,MAAMnD,IAAIsE,EAAOlC,GAEvB,IAAK,IAAIG,EAAQ,EAAGgC,EAASD,EAAMjD,OAAQkB,EAAQgC,IAAUhC,EAC3DH,EAAMG,GAASF,EAAMM,OAAO2B,EAAM/B,GAAQF,GAG5C,OAAOD,CACT,ECtDEU,YAAaD,EACb2B,KDmFc,SACdA,EACAzB,GAEA,OAAOyB,EAAKxB,MAAM,EAAGwB,EAAKC,KAAMD,EAAKrD,KACvC,ECvFEuD,SD4Fc,SACdA,EACArC,GAEA,OAAO,IAAIA,EAAM/B,YAAYuC,EAAgB6B,EAASC,QACxD,EChGEC,KDqGc,SAA6BA,EAAavC,GACxD,OAAO,IAAIA,EAAM/B,YAAYsE,EAAKC,UACpC,ECtGEjC,MAAOgB,EACPV,IAAKD,EACLhB,OAAQoB,EACRzC,ODyNc,SACdA,EACAyB,GAEA,IAAMD,EAAQ,IAAIC,EAAM/B,YACtBM,EAAOkE,OACPpE,EAAeE,IAKjB,OAFAwB,EAAM2C,UAAYnE,EAAOmE,UAElB3C,CACT,ECpOEpC,IAAK6D,GAEDmB,EAAwDf,EAC5D,CAAE,EACFI,EACA,CACEC,MD6CY,SACdA,EACAjC,GAEA,IAAMD,EAAQ,IAAIC,EAAM/B,YAKxB,OAFA+B,EAAMc,MAAMnD,IAAIsE,EAAOlC,GAEhBD,EAAwBmC,EAAOlC,EAAOC,EAC/C,ECtDIa,IDmHY,SACdA,EACAb,GAEA,OAAOF,EAAwBe,EAAKD,EAAaC,EAAKb,GAAQA,EAChE,ECvHIJ,ODsLY,SACdA,EACAI,GAEA,IAAMD,EAAQ/B,EAAcgC,EAAMjD,WAKlC,OAFAiD,EAAMc,MAAMnD,IAAIiC,EAAQG,GAEjBD,EAAwBF,EAAQG,EAAOC,EAChD,EC/LIrC,ID6PY,SACdA,EACAqC,GAEA,OAAOF,EAAwBnC,EAAK6D,EAAa7D,EAAKqC,GAAQA,EAChE,ICtNM,SAAU4C,EAAaC,GAC3B,IACMC,EAvCR,SACED,GAEA,MAAO,CACLE,UAAWF,EAAQjD,OACnB+B,MAAOkB,EAAQZ,MACfe,YAAaH,EAAQpC,YACrBwC,KAAMJ,EAAQV,KACde,QAAS9B,EACT+B,SAAUN,EAAQR,SAClBe,KAAMP,EAAQN,KACdc,MAAOR,EAAQtC,MACf+C,aAAcT,EAAQpC,YACtB8C,aAAcV,EAAQpC,YACtB+C,UAAWX,EAAQpC,YACnBgD,WAAYZ,EAAQpC,YACpBiD,WAAYb,EAAQpC,YACpBkD,IAAKd,EAAQhC,IACb+C,OAAQxC,EACRnE,OAAQ4F,EAAQjD,OAChBiE,QAAStC,EACTuC,OAAQjB,EAAQtE,OAChBwF,IAAKlB,EAAQlF,IACbqG,OAAQ5C,EACRrD,QAASwD,EACT0C,QAAS1C,EACT2C,WAAYrB,EAAQpC,YACpB0D,kBAAmBtB,EAAQpC,YAC3B2D,YAAavB,EAAQpC,YACrB4D,YAAaxB,EAAQpC,YACrB6D,YAAazB,EAAQpC,YAEzB,CAO6B8D,CADD3C,EAAO,CAAE,EAAEI,EAAuBa,IAE7CZ,EAA0Ba,EAAkBnB,MAA7B/B,EAAWkD,EAAkB7F,OAE3D,SAASqD,EAAO1C,EAAYoC,GAG1B,GAFAA,EAAMjD,UAAYiD,EAAM/B,iBAAcuG,GAEjC5G,GAA0B,iBAAVA,EACnB,OAAOA,EAGT,GAAIoC,EAAMc,MAAMvD,IAAIK,GAClB,OAAOoC,EAAMc,MAAMpD,IAAIE,GAOzB,GAJAoC,EAAMjD,UAAY8E,EAAejE,GACjCoC,EAAM/B,YAAc+B,EAAMjD,WAAaiD,EAAMjD,UAAUmB,aAGlD8B,EAAM/B,aAAe+B,EAAM/B,cAAgBhB,OAC9C,OAAO2C,EAAOhC,EAAOoC,GAIvB,GAAI0B,EAAQ9D,GACV,OAAOqE,EAAMrE,EAAOoC,GAGtB,IAAMyE,EAAoB3B,EAAmB7D,EAAOrB,IAEpD,OAAI6G,EACKA,EAAkB7G,EAAOoC,GAGL,mBAAfpC,EAAM8G,KAAsB9G,EAAQgC,EAAOhC,EAAOoC,EACjE,CAED,OAAO,SAAqBpC,GAC1B,OAAO0C,EAAO1C,EAAO,CACnBK,iBAAauG,EACb1D,MAAOhD,IACPwC,OAAMA,EACNvD,eAAWyH,GAEf,CACF,CAMM,SAAUG,EAAmB9B,GACjC,OAAOD,EAAahB,EAAO,CAAA,EAAIe,EAAwBE,GACzD,KAOa+B,EAAaD,EAAmB,IAK9BzE,EAAA0C,EAAa,CAAA"}
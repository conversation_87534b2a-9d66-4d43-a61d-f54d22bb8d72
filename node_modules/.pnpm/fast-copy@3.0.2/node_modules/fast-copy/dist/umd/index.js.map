{"version": 3, "file": "index.js", "sources": ["../../../../src/utils.ts", "../../../../src/copier.ts", "../../../../src/index.ts"], "sourcesContent": ["export interface Cache {\n  has: (value: any) => boolean;\n  set: (key: any, value: any) => void;\n  get: (key: any) => any;\n}\n\nconst { toString: toStringFunction } = Function.prototype;\nconst { create } = Object;\nconst { toString: toStringObject } = Object.prototype;\n\n/**\n * @classdesc Fallback cache for when WeakMap is not natively supported\n */\nclass LegacyCache {\n  private _keys: any[] = [];\n  private _values: any[] = [];\n\n  has(key: any) {\n    return !!~this._keys.indexOf(key);\n  }\n\n  get(key: any) {\n    return this._values[this._keys.indexOf(key)];\n  }\n\n  set(key: any, value: any) {\n    this._keys.push(key);\n    this._values.push(value);\n  }\n}\n\nfunction createCacheLegacy(): Cache {\n  return new LegacyCache();\n}\n\nfunction createCacheModern(): Cache {\n  return new WeakMap();\n}\n\n/**\n * Get a new cache object to prevent circular references.\n */\nexport const createCache =\n  typeof WeakMap !== 'undefined' ? createCacheModern : createCacheLegacy;\n\n/**\n * Get an empty version of the object with the same prototype it has.\n */\nexport function getCleanClone(prototype: any): any {\n  if (!prototype) {\n    return create(null);\n  }\n\n  const Constructor = prototype.constructor;\n\n  if (Constructor === Object) {\n    return prototype === Object.prototype ? {} : create(prototype);\n  }\n\n  if (\n    Constructor &&\n    ~toStringFunction.call(Constructor).indexOf('[native code]')\n  ) {\n    try {\n      return new Constructor();\n    } catch {}\n  }\n\n  return create(prototype);\n}\n\nfunction getRegExpFlagsLegacy(regExp: RegExp): string {\n  let flags = '';\n\n  if (regExp.global) {\n    flags += 'g';\n  }\n\n  if (regExp.ignoreCase) {\n    flags += 'i';\n  }\n\n  if (regExp.multiline) {\n    flags += 'm';\n  }\n\n  if (regExp.unicode) {\n    flags += 'u';\n  }\n\n  if (regExp.sticky) {\n    flags += 'y';\n  }\n\n  return flags;\n}\n\nfunction getRegExpFlagsModern(regExp: RegExp): string {\n  return regExp.flags;\n}\n\n/**\n * Get the flags to apply to the copied regexp.\n */\nexport const getRegExpFlags =\n  /test/g.flags === 'g' ? getRegExpFlagsModern : getRegExpFlagsLegacy;\n\nfunction getTagLegacy(value: any): string {\n  const type = toStringObject.call(value);\n\n  return type.substring(8, type.length - 1);\n}\n\nfunction getTagModern(value: any): string {\n  return value[Symbol.toStringTag] || getTagLegacy(value);\n}\n\n/**\n * Get the tag of the value passed, so that the correct copier can be used.\n */\nexport const getTag =\n  typeof Symbol !== 'undefined' ? getTagModern : getTagLegacy;\n", "import { getCleanClone, getRegExpFlags } from './utils';\n\nimport type { Cache } from './utils';\n\nexport type InternalCopier<Value> = (value: Value, state: State) => Value;\n\nexport interface State {\n  Constructor: any;\n  cache: Cache;\n  copier: InternalCopier<any>;\n  prototype: any;\n}\n\nconst {\n  defineProperty,\n  getOwnPropertyDescriptor,\n  getOwnPropertyNames,\n  getOwnPropertySymbols,\n} = Object;\nconst { hasOwnProperty, propertyIsEnumerable } = Object.prototype;\n\nconst SUPPORTS_SYMBOL = typeof getOwnPropertySymbols === 'function';\n\nfunction getStrictPropertiesModern(object: any): Array<string | symbol> {\n  return (getOwnPropertyNames(object) as Array<string | symbol>).concat(\n    getOwnPropertySymbols(object)\n  );\n}\n\n/**\n * Get the properites used when copying objects strictly. This includes both keys and symbols.\n */\nconst getStrictProperties = SUPPORTS_SYMBOL\n  ? getStrictPropertiesModern\n  : getOwnPropertyNames;\n\n/**\n * Striclty copy all properties contained on the object.\n */\nfunction copyOwnPropertiesStrict<Value>(\n  value: Value,\n  clone: Value,\n  state: State\n): Value {\n  const properties = getStrictProperties(value);\n\n  for (\n    let index = 0, length = properties.length, property, descriptor;\n    index < length;\n    ++index\n  ) {\n    property = properties[index];\n\n    if (property === 'callee' || property === 'caller') {\n      continue;\n    }\n\n    descriptor = getOwnPropertyDescriptor(value, property);\n\n    if (!descriptor) {\n      // In extra edge cases where the property descriptor cannot be retrived, fall back to\n      // the loose assignment.\n      (clone as any)[property] = state.copier((value as any)[property], state);\n      continue;\n    }\n\n    // Only clone the value if actually a value, not a getter / setter.\n    if (!descriptor.get && !descriptor.set) {\n      descriptor.value = state.copier(descriptor.value, state);\n    }\n\n    try {\n      defineProperty(clone, property, descriptor);\n    } catch (error) {\n      // Tee above can fail on node in edge cases, so fall back to the loose assignment.\n      (clone as any)[property] = descriptor.value;\n    }\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the indexed values in the array.\n */\nexport function copyArrayLoose(array: any[], state: State) {\n  const clone = new state.Constructor();\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(array, clone);\n\n  for (let index = 0, length = array.length; index < length; ++index) {\n    clone[index] = state.copier(array[index], state);\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the indexed values in the array, as well as any custom properties.\n */\nexport function copyArrayStrict<Value extends any[]>(\n  array: Value,\n  state: State\n) {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(array, clone);\n\n  return copyOwnPropertiesStrict(array, clone, state);\n}\n\n/**\n * Copy the contents of the ArrayBuffer.\n */\nexport function copyArrayBuffer<Value extends ArrayBuffer>(\n  arrayBuffer: Value,\n  _state: State\n): Value {\n  return arrayBuffer.slice(0) as Value;\n}\n\n/**\n * Create a new Blob with the contents of the original.\n */\nexport function copyBlob<Value extends Blob>(\n  blob: Value,\n  _state: State\n): Value {\n  return blob.slice(0, blob.size, blob.type) as Value;\n}\n\n/**\n * Create a new DataView with the contents of the original.\n */\nexport function copyDataView<Value extends DataView>(\n  dataView: Value,\n  state: State\n): Value {\n  return new state.Constructor(copyArrayBuffer(dataView.buffer, state));\n}\n\n/**\n * Create a new Date based on the time of the original.\n */\nexport function copyDate<Value extends Date>(date: Value, state: State): Value {\n  return new state.Constructor(date.getTime());\n}\n\n/**\n * Deeply copy the keys and values of the original.\n */\nexport function copyMapLoose<Value extends Map<any, any>>(\n  map: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(map, clone);\n\n  map.forEach((value, key) => {\n    clone.set(key, state.copier(value, state));\n  });\n\n  return clone;\n}\n\n/**\n * Deeply copy the keys and values of the original, as well as any custom properties.\n */\nexport function copyMapStrict<Value extends Map<any, any>>(\n  map: Value,\n  state: State\n) {\n  return copyOwnPropertiesStrict(map, copyMapLoose(map, state), state);\n}\n\nfunction copyObjectLooseLegacy<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone: any = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  for (const key in object) {\n    if (hasOwnProperty.call(object, key)) {\n      clone[key] = state.copier(object[key], state);\n    }\n  }\n\n  return clone;\n}\n\nfunction copyObjectLooseModern<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  for (const key in object) {\n    if (hasOwnProperty.call(object, key)) {\n      clone[key] = state.copier(object[key], state);\n    }\n  }\n\n  const symbols = getOwnPropertySymbols(object);\n\n  for (\n    let index = 0, length = symbols.length, symbol;\n    index < length;\n    ++index\n  ) {\n    symbol = symbols[index];\n\n    if (propertyIsEnumerable.call(object, symbol)) {\n      clone[symbol] = state.copier((object as any)[symbol], state);\n    }\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the properties (keys and symbols) and values of the original.\n */\nexport const copyObjectLoose = SUPPORTS_SYMBOL\n  ? copyObjectLooseModern\n  : copyObjectLooseLegacy;\n\n/**\n * Deeply copy the properties (keys and symbols) and values of the original, as well\n * as any hidden or non-enumerable properties.\n */\nexport function copyObjectStrict<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  return copyOwnPropertiesStrict(object, clone, state);\n}\n\n/**\n * Create a new primitive wrapper from the value of the original.\n */\nexport function copyPrimitiveWrapper<\n  // Specifically use the object constructor types\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  Value extends Boolean | Number | String\n>(primitiveObject: Value, state: State): Value {\n  return new state.Constructor(primitiveObject.valueOf());\n}\n\n/**\n * Create a new RegExp based on the value and flags of the original.\n */\nexport function copyRegExp<Value extends RegExp>(\n  regExp: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor(\n    regExp.source,\n    getRegExpFlags(regExp)\n  ) as Value;\n\n  clone.lastIndex = regExp.lastIndex;\n\n  return clone;\n}\n\n/**\n * Return the original value (an identity function).\n *\n * @note\n * THis is used for objects that cannot be copied, such as WeakMap.\n */\nexport function copySelf<Value>(value: Value, _state: State): Value {\n  return value;\n}\n\n/**\n * Deeply copy the values of the original.\n */\nexport function copySetLoose<Value extends Set<any>>(\n  set: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(set, clone);\n\n  set.forEach((value) => {\n    clone.add(state.copier(value, state));\n  });\n\n  return clone;\n}\n\n/**\n * Deeply copy the values of the original, as well as any custom properties.\n */\nexport function copySetStrict<Value extends Set<any>>(\n  set: Value,\n  state: State\n): Value {\n  return copyOwnPropertiesStrict(set, copySetLoose(set, state), state);\n}\n", "import {\n  copyArrayBuffer,\n  copyArrayLoose,\n  copyArrayStrict,\n  copyBlob,\n  copyDataView,\n  copyDate,\n  copyMapLoose,\n  copyMapStrict,\n  copyObjectLoose,\n  copyObjectStrict,\n  copyPrimitiveWrapper,\n  copyRegExp,\n  copySelf,\n  copySetLoose,\n  copySetStrict,\n} from './copier';\nimport { createCache, getTag } from './utils';\n\nimport type { InternalCopier, State } from './copier';\n\nexport type { State } from './copier';\n\nconst { isArray } = Array;\nconst { assign } = Object;\nconst getPrototypeOf = Object.getPrototypeOf || ((obj) => obj.__proto__)\n\nexport interface CreateCopierOptions {\n  array?: InternalCopier<any[]>;\n  arrayBuffer?: InternalCopier<ArrayBuffer>;\n  blob?: InternalCopier<Blob>;\n  dataView?: InternalCopier<DataView>;\n  date?: InternalCopier<Date>;\n  error?: InternalCopier<any>;\n  map?: InternalCopier<Map<any, any>>;\n  object?: InternalCopier<Record<string, any>>;\n  regExp?: InternalCopier<RegExp>;\n  set?: InternalCopier<Set<any>>;\n}\n\nconst DEFAULT_LOOSE_OPTIONS: Required<CreateCopierOptions> = {\n  array: copyArrayLoose,\n  arrayBuffer: copyArrayBuffer,\n  blob: copyBlob,\n  dataView: copyDataView,\n  date: copyDate,\n  error: copySelf,\n  map: copyMapLoose,\n  object: copyObjectLoose,\n  regExp: copyRegExp,\n  set: copySetLoose,\n};\nconst DEFAULT_STRICT_OPTIONS: Required<CreateCopierOptions> = assign(\n  {},\n  DEFAULT_LOOSE_OPTIONS,\n  {\n    array: copyArrayStrict,\n    map: copyMapStrict,\n    object: copyObjectStrict,\n    set: copySetStrict,\n  }\n);\n\n/**\n * Get the copiers used for each specific object tag.\n */\nfunction getTagSpecificCopiers(\n  options: Required<CreateCopierOptions>\n): Record<string, InternalCopier<any>> {\n  return {\n    Arguments: options.object,\n    Array: options.array,\n    ArrayBuffer: options.arrayBuffer,\n    Blob: options.blob,\n    Boolean: copyPrimitiveWrapper,\n    DataView: options.dataView,\n    Date: options.date,\n    Error: options.error,\n    Float32Array: options.arrayBuffer,\n    Float64Array: options.arrayBuffer,\n    Int8Array: options.arrayBuffer,\n    Int16Array: options.arrayBuffer,\n    Int32Array: options.arrayBuffer,\n    Map: options.map,\n    Number: copyPrimitiveWrapper,\n    Object: options.object,\n    Promise: copySelf,\n    RegExp: options.regExp,\n    Set: options.set,\n    String: copyPrimitiveWrapper,\n    WeakMap: copySelf,\n    WeakSet: copySelf,\n    Uint8Array: options.arrayBuffer,\n    Uint8ClampedArray: options.arrayBuffer,\n    Uint16Array: options.arrayBuffer,\n    Uint32Array: options.arrayBuffer,\n    Uint64Array: options.arrayBuffer,\n  };\n}\n\n/**\n * Create a custom copier based on the object-specific copy methods passed.\n */\nexport function createCopier(options: CreateCopierOptions) {\n  const normalizedOptions = assign({}, DEFAULT_LOOSE_OPTIONS, options);\n  const tagSpecificCopiers = getTagSpecificCopiers(normalizedOptions);\n  const { Array: array, Object: object } = tagSpecificCopiers;\n\n  function copier(value: any, state: State): any {\n    state.prototype = state.Constructor = undefined;\n\n    if (!value || typeof value !== 'object') {\n      return value;\n    }\n\n    if (state.cache.has(value)) {\n      return state.cache.get(value);\n    }\n\n    state.prototype = getPrototypeOf(value);\n    state.Constructor = state.prototype && state.prototype.constructor;\n\n    // plain objects\n    if (!state.Constructor || state.Constructor === Object) {\n      return object(value, state);\n    }\n\n    // arrays\n    if (isArray(value)) {\n      return array(value, state);\n    }\n\n    const tagSpecificCopier = tagSpecificCopiers[getTag(value)];\n\n    if (tagSpecificCopier) {\n      return tagSpecificCopier(value, state);\n    }\n\n    return typeof value.then === 'function' ? value : object(value, state);\n  }\n\n  return function copy<Value>(value: Value): Value {\n    return copier(value, {\n      Constructor: undefined,\n      cache: createCache(),\n      copier,\n      prototype: undefined,\n    });\n  };\n}\n\n/**\n * Create a custom copier based on the object-specific copy methods passed, defaulting to the\n * same internals as `copyStrict`.\n */\nexport function createStrictCopier(options: CreateCopierOptions) {\n  return createCopier(assign({}, DEFAULT_STRICT_OPTIONS, options));\n}\n\n/**\n * Copy an value deeply as much as possible, where strict recreation of object properties\n * are maintained. All properties (including non-enumerable ones) are copied with their\n * original property descriptors on both objects and arrays.\n */\nexport const copyStrict = createStrictCopier({});\n\n/**\n * Copy an value deeply as much as possible.\n */\nexport default createCopier({});\n"], "names": [], "mappings": ";;;;;;IAMQ,IAAU,gBAAgB,GAAK,QAAQ,CAAC,SAAS,SAAvB,CAAwB;IAClD,IAAA,MAAM,GAAK,MAAM,CAAA,MAAX,CAAY;IAClB,IAAU,cAAc,GAAK,MAAM,CAAC,SAAS,SAArB,CAAsB;IAEtD;;IAEG;IACH,IAAA,WAAA,kBAAA,YAAA;IAAA,IAAA,SAAA,WAAA,GAAA;YACU,IAAK,CAAA,KAAA,GAAU,EAAE,CAAC;YAClB,IAAO,CAAA,OAAA,GAAU,EAAE,CAAC;SAc7B;QAZC,WAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,GAAQ,EAAA;YACV,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SACnC,CAAA;QAED,WAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,GAAQ,EAAA;IACV,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;SAC9C,CAAA;IAED,IAAA,WAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,GAAQ,EAAE,KAAU,EAAA;IACtB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACrB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B,CAAA;QACH,OAAC,WAAA,CAAA;IAAD,CAAC,EAAA,CAAA,CAAA;IAED,SAAS,iBAAiB,GAAA;QACxB,OAAO,IAAI,WAAW,EAAE,CAAC;IAC3B,CAAC;IAED,SAAS,iBAAiB,GAAA;QACxB,OAAO,IAAI,OAAO,EAAE,CAAC;IACvB,CAAC;IAED;;IAEG;IACI,IAAM,WAAW,GACtB,OAAO,OAAO,KAAK,WAAW,GAAG,iBAAiB,GAAG,iBAAiB,CAAC;IAEzE;;IAEG;IACG,SAAU,aAAa,CAAC,SAAc,EAAA;QAC1C,IAAI,CAAC,SAAS,EAAE;IACd,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;IACrB,KAAA;IAED,IAAA,IAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;QAE1C,IAAI,WAAW,KAAK,MAAM,EAAE;IAC1B,QAAA,OAAO,SAAS,KAAK,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IAChE,KAAA;IAED,IAAA,IACE,WAAW;YACX,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,EAC5D;YACA,IAAI;gBACF,OAAO,IAAI,WAAW,EAAE,CAAC;IAC1B,SAAA;IAAC,QAAA,OAAA,EAAA,EAAM,GAAE;IACX,KAAA;IAED,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC;IAC3B,CAAC;IAED,SAAS,oBAAoB,CAAC,MAAc,EAAA;QAC1C,IAAI,KAAK,GAAG,EAAE,CAAC;QAEf,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,KAAK,IAAI,GAAG,CAAC;IACd,KAAA;QAED,IAAI,MAAM,CAAC,UAAU,EAAE;YACrB,KAAK,IAAI,GAAG,CAAC;IACd,KAAA;QAED,IAAI,MAAM,CAAC,SAAS,EAAE;YACpB,KAAK,IAAI,GAAG,CAAC;IACd,KAAA;QAED,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,KAAK,IAAI,GAAG,CAAC;IACd,KAAA;QAED,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,KAAK,IAAI,GAAG,CAAC;IACd,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,oBAAoB,CAAC,MAAc,EAAA;QAC1C,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;IAEG;IACI,IAAM,cAAc,GACzB,OAAO,CAAC,KAAK,KAAK,GAAG,GAAG,oBAAoB,GAAG,oBAAoB,CAAC;IAEtE,SAAS,YAAY,CAAC,KAAU,EAAA;QAC9B,IAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAExC,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED,SAAS,YAAY,CAAC,KAAU,EAAA;QAC9B,OAAO,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;IAC1D,CAAC;IAED;;IAEG;IACI,IAAM,MAAM,GACjB,OAAO,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG,YAAY;;IC3G3D,IAAA,cAAc,GAIZ,MAAM,eAJM,EACd,wBAAwB,GAGtB,MAAM,CAAA,wBAHgB,EACxB,mBAAmB,GAEjB,MAAM,CAFW,mBAAA,EACnB,qBAAqB,GACnB,MAAM,sBADa,CACZ;IACL,IAAA,EAAA,GAA2C,MAAM,CAAC,SAAS,EAAzD,cAAc,GAAA,EAAA,CAAA,cAAA,EAAE,oBAAoB,GAAA,EAAA,CAAA,oBAAqB,CAAC;IAElE,IAAM,eAAe,GAAG,OAAO,qBAAqB,KAAK,UAAU,CAAC;IAEpE,SAAS,yBAAyB,CAAC,MAAW,EAAA;IAC5C,IAAA,OAAQ,mBAAmB,CAAC,MAAM,CAA4B,CAAC,MAAM,CACnE,qBAAqB,CAAC,MAAM,CAAC,CAC9B,CAAC;IACJ,CAAC;IAED;;IAEG;IACH,IAAM,mBAAmB,GAAG,eAAe;IACzC,MAAE,yBAAyB;UACzB,mBAAmB,CAAC;IAExB;;IAEG;IACH,SAAS,uBAAuB,CAC9B,KAAY,EACZ,KAAY,EACZ,KAAY,EAAA;IAEZ,IAAA,IAAM,UAAU,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAE9C,KACE,IAAI,KAAK,GAAG,CAAC,EAAE,QAAM,GAAG,UAAU,CAAC,MAAM,EAAE,QAAQ,GAAA,KAAA,CAAA,EAAE,UAAU,GAAA,KAAA,CAAA,EAC/D,KAAK,GAAG,QAAM,EACd,EAAE,KAAK,EACP;IACA,QAAA,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;IAE7B,QAAA,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBAClD,SAAS;IACV,SAAA;IAED,QAAA,UAAU,GAAG,wBAAwB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAEvD,IAAI,CAAC,UAAU,EAAE;;;IAGd,YAAA,KAAa,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,MAAM,CAAE,KAAa,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;gBACzE,SAAS;IACV,SAAA;;YAGD,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;IACtC,YAAA,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC1D,SAAA;YAED,IAAI;IACF,YAAA,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC7C,SAAA;IAAC,QAAA,OAAO,KAAK,EAAE;;IAEb,YAAA,KAAa,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;IAC7C,SAAA;IACF,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;IAEG;IACa,SAAA,cAAc,CAAC,KAAY,EAAE,KAAY,EAAA;IACvD,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;;QAGtC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAE9B,IAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,QAAM,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,QAAM,EAAE,EAAE,KAAK,EAAE;IAClE,QAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;IAClD,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;IAEG;IACa,SAAA,eAAe,CAC7B,KAAY,EACZ,KAAY,EAAA;IAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAW,CAAC;;QAG/C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAE9B,OAAO,uBAAuB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED;;IAEG;IACa,SAAA,eAAe,CAC7B,WAAkB,EAClB,MAAa,EAAA;IAEb,IAAA,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,CAAU,CAAC;IACvC,CAAC;IAED;;IAEG;IACa,SAAA,QAAQ,CACtB,IAAW,EACX,MAAa,EAAA;IAEb,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAU,CAAC;IACtD,CAAC;IAED;;IAEG;IACa,SAAA,YAAY,CAC1B,QAAe,EACf,KAAY,EAAA;IAEZ,IAAA,OAAO,IAAI,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAa,CAAC,CAAC,CAAC;IACxE,CAAC;IAED;;IAEG;IACa,SAAA,QAAQ,CAAqB,IAAW,EAAE,KAAY,EAAA;QACpE,OAAO,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;IAEG;IACa,SAAA,YAAY,CAC1B,GAAU,EACV,KAAY,EAAA;IAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAW,CAAC;;QAG/C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAE5B,IAAA,GAAG,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG,EAAA;IACrB,QAAA,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C,KAAC,CAAC,CAAC;IAEH,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;IAEG;IACa,SAAA,aAAa,CAC3B,GAAU,EACV,KAAY,EAAA;IAEZ,IAAA,OAAO,uBAAuB,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;IACvE,CAAC;IAED,SAAS,qBAAqB,CAC5B,MAAa,EACb,KAAY,EAAA;QAEZ,IAAM,KAAK,GAAQ,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;QAGlD,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE/B,IAAA,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;YACxB,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;IACpC,YAAA,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IAC/C,SAAA;IACF,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED,SAAS,qBAAqB,CAC5B,MAAa,EACb,KAAY,EAAA;QAEZ,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;QAG7C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE/B,IAAA,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;YACxB,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;IACpC,YAAA,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IAC/C,SAAA;IACF,KAAA;IAED,IAAA,IAAM,OAAO,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAE9C,KACE,IAAI,KAAK,GAAG,CAAC,EAAE,QAAM,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,GAAA,KAAA,CAAA,EAC9C,KAAK,GAAG,QAAM,EACd,EAAE,KAAK,EACP;IACA,QAAA,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;YAExB,IAAI,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;IAC7C,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAE,MAAc,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;IAC9D,SAAA;IACF,KAAA;IAED,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;IAEG;IACI,IAAM,eAAe,GAAG,eAAe;IAC5C,MAAE,qBAAqB;UACrB,qBAAqB,CAAC;IAE1B;;;IAGG;IACa,SAAA,gBAAgB,CAC9B,MAAa,EACb,KAAY,EAAA;QAEZ,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;QAG7C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAE/B,OAAO,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACvD,CAAC;IAED;;IAEG;IACa,SAAA,oBAAoB,CAIlC,eAAsB,EAAE,KAAY,EAAA;QACpC,OAAO,IAAI,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1D,CAAC;IAED;;IAEG;IACa,SAAA,UAAU,CACxB,MAAa,EACb,KAAY,EAAA;IAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,CACjC,MAAM,CAAC,MAAM,EACb,cAAc,CAAC,MAAM,CAAC,CACd,CAAC;IAEX,IAAA,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAEnC,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;IAKG;IACa,SAAA,QAAQ,CAAQ,KAAY,EAAE,MAAa,EAAA;IACzD,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;IAEG;IACa,SAAA,YAAY,CAC1B,GAAU,EACV,KAAY,EAAA;IAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAW,CAAC;;QAG/C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IAE5B,IAAA,GAAG,CAAC,OAAO,CAAC,UAAC,KAAK,EAAA;IAChB,QAAA,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;IACxC,KAAC,CAAC,CAAC;IAEH,IAAA,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;IAEG;IACa,SAAA,aAAa,CAC3B,GAAU,EACV,KAAY,EAAA;IAEZ,IAAA,OAAO,uBAAuB,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;IACvE;;ICtSQ,IAAA,OAAO,GAAK,KAAK,CAAA,OAAV,CAAW;IAClB,IAAA,MAAM,GAAK,MAAM,CAAA,MAAX,CAAY;IAC1B,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,KAAK,UAAC,GAAG,EAAA,EAAK,OAAA,GAAG,CAAC,SAAS,CAAb,EAAa,CAAC,CAAA;IAexE,IAAM,qBAAqB,GAAkC;IAC3D,IAAA,KAAK,EAAE,cAAc;IACrB,IAAA,WAAW,EAAE,eAAe;IAC5B,IAAA,IAAI,EAAE,QAAQ;IACd,IAAA,QAAQ,EAAE,YAAY;IACtB,IAAA,IAAI,EAAE,QAAQ;IACd,IAAA,KAAK,EAAE,QAAQ;IACf,IAAA,GAAG,EAAE,YAAY;IACjB,IAAA,MAAM,EAAE,eAAe;IACvB,IAAA,MAAM,EAAE,UAAU;IAClB,IAAA,GAAG,EAAE,YAAY;KAClB,CAAC;IACF,IAAM,sBAAsB,GAAkC,MAAM,CAClE,EAAE,EACF,qBAAqB,EACrB;IACE,IAAA,KAAK,EAAE,eAAe;IACtB,IAAA,GAAG,EAAE,aAAa;IAClB,IAAA,MAAM,EAAE,gBAAgB;IACxB,IAAA,GAAG,EAAE,aAAa;IACnB,CAAA,CACF,CAAC;IAEF;;IAEG;IACH,SAAS,qBAAqB,CAC5B,OAAsC,EAAA;QAEtC,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,MAAM;YACzB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,IAAI,EAAE,OAAO,CAAC,IAAI;IAClB,QAAA,OAAO,EAAE,oBAAoB;YAC7B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,YAAY,EAAE,OAAO,CAAC,WAAW;YACjC,YAAY,EAAE,OAAO,CAAC,WAAW;YACjC,SAAS,EAAE,OAAO,CAAC,WAAW;YAC9B,UAAU,EAAE,OAAO,CAAC,WAAW;YAC/B,UAAU,EAAE,OAAO,CAAC,WAAW;YAC/B,GAAG,EAAE,OAAO,CAAC,GAAG;IAChB,QAAA,MAAM,EAAE,oBAAoB;YAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;IACtB,QAAA,OAAO,EAAE,QAAQ;YACjB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,GAAG,EAAE,OAAO,CAAC,GAAG;IAChB,QAAA,MAAM,EAAE,oBAAoB;IAC5B,QAAA,OAAO,EAAE,QAAQ;IACjB,QAAA,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,OAAO,CAAC,WAAW;YAC/B,iBAAiB,EAAE,OAAO,CAAC,WAAW;YACtC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,WAAW,EAAE,OAAO,CAAC,WAAW;SACjC,CAAC;IACJ,CAAC;IAED;;IAEG;IACG,SAAU,YAAY,CAAC,OAA4B,EAAA;QACvD,IAAM,iBAAiB,GAAG,MAAM,CAAC,EAAE,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;IACrE,IAAA,IAAM,kBAAkB,GAAG,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAO,KAAK,GAAqB,kBAAkB,CAAA,KAAvC,EAAU,MAAM,GAAK,kBAAkB,CAAA,MAAvB,CAAwB;IAE5D,IAAA,SAAS,MAAM,CAAC,KAAU,EAAE,KAAY,EAAA;YACtC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC;IAEhD,QAAA,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;IACvC,YAAA,OAAO,KAAK,CAAC;IACd,SAAA;YAED,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC1B,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,SAAA;IAED,QAAA,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;IACxC,QAAA,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC;;YAGnE,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM,EAAE;IACtD,YAAA,OAAO,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC7B,SAAA;;IAGD,QAAA,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;IAClB,YAAA,OAAO,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC5B,SAAA;YAED,IAAM,iBAAiB,GAAG,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;IAE5D,QAAA,IAAI,iBAAiB,EAAE;IACrB,YAAA,OAAO,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IACxC,SAAA;IAED,QAAA,OAAO,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACxE;QAED,OAAO,SAAS,IAAI,CAAQ,KAAY,EAAA;YACtC,OAAO,MAAM,CAAC,KAAK,EAAE;IACnB,YAAA,WAAW,EAAE,SAAS;gBACtB,KAAK,EAAE,WAAW,EAAE;IACpB,YAAA,MAAM,EAAA,MAAA;IACN,YAAA,SAAS,EAAE,SAAS;IACrB,SAAA,CAAC,CAAC;IACL,KAAC,CAAC;IACJ,CAAC;IAED;;;IAGG;IACG,SAAU,kBAAkB,CAAC,OAA4B,EAAA;QAC7D,OAAO,YAAY,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;;;IAIG;QACU,UAAU,GAAG,kBAAkB,CAAC,EAAE,EAAE;IAEjD;;IAEG;AACH,gBAAe,YAAY,CAAC,EAAE,CAAC;;;;;;;;;;;;;"}
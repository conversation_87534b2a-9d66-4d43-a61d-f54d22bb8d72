{"version": 3, "file": "index.cjs", "sources": ["../../../../src/utils.ts", "../../../../src/copier.ts", "../../../../src/index.ts"], "sourcesContent": ["export interface Cache {\n  has: (value: any) => boolean;\n  set: (key: any, value: any) => void;\n  get: (key: any) => any;\n}\n\nconst { toString: toStringFunction } = Function.prototype;\nconst { create } = Object;\nconst { toString: toStringObject } = Object.prototype;\n\n/**\n * @classdesc Fallback cache for when WeakMap is not natively supported\n */\nclass LegacyCache {\n  private _keys: any[] = [];\n  private _values: any[] = [];\n\n  has(key: any) {\n    return !!~this._keys.indexOf(key);\n  }\n\n  get(key: any) {\n    return this._values[this._keys.indexOf(key)];\n  }\n\n  set(key: any, value: any) {\n    this._keys.push(key);\n    this._values.push(value);\n  }\n}\n\nfunction createCacheLegacy(): Cache {\n  return new LegacyCache();\n}\n\nfunction createCacheModern(): Cache {\n  return new WeakMap();\n}\n\n/**\n * Get a new cache object to prevent circular references.\n */\nexport const createCache =\n  typeof WeakMap !== 'undefined' ? createCacheModern : createCacheLegacy;\n\n/**\n * Get an empty version of the object with the same prototype it has.\n */\nexport function getCleanClone(prototype: any): any {\n  if (!prototype) {\n    return create(null);\n  }\n\n  const Constructor = prototype.constructor;\n\n  if (Constructor === Object) {\n    return prototype === Object.prototype ? {} : create(prototype);\n  }\n\n  if (\n    Constructor &&\n    ~toStringFunction.call(Constructor).indexOf('[native code]')\n  ) {\n    try {\n      return new Constructor();\n    } catch {}\n  }\n\n  return create(prototype);\n}\n\nfunction getRegExpFlagsLegacy(regExp: RegExp): string {\n  let flags = '';\n\n  if (regExp.global) {\n    flags += 'g';\n  }\n\n  if (regExp.ignoreCase) {\n    flags += 'i';\n  }\n\n  if (regExp.multiline) {\n    flags += 'm';\n  }\n\n  if (regExp.unicode) {\n    flags += 'u';\n  }\n\n  if (regExp.sticky) {\n    flags += 'y';\n  }\n\n  return flags;\n}\n\nfunction getRegExpFlagsModern(regExp: RegExp): string {\n  return regExp.flags;\n}\n\n/**\n * Get the flags to apply to the copied regexp.\n */\nexport const getRegExpFlags =\n  /test/g.flags === 'g' ? getRegExpFlagsModern : getRegExpFlagsLegacy;\n\nfunction getTagLegacy(value: any): string {\n  const type = toStringObject.call(value);\n\n  return type.substring(8, type.length - 1);\n}\n\nfunction getTagModern(value: any): string {\n  return value[Symbol.toStringTag] || getTagLegacy(value);\n}\n\n/**\n * Get the tag of the value passed, so that the correct copier can be used.\n */\nexport const getTag =\n  typeof Symbol !== 'undefined' ? getTagModern : getTagLegacy;\n", "import { getCleanClone, getRegExpFlags } from './utils';\n\nimport type { Cache } from './utils';\n\nexport type InternalCopier<Value> = (value: Value, state: State) => Value;\n\nexport interface State {\n  Constructor: any;\n  cache: Cache;\n  copier: InternalCopier<any>;\n  prototype: any;\n}\n\nconst {\n  defineProperty,\n  getOwnPropertyDescriptor,\n  getOwnPropertyNames,\n  getOwnPropertySymbols,\n} = Object;\nconst { hasOwnProperty, propertyIsEnumerable } = Object.prototype;\n\nconst SUPPORTS_SYMBOL = typeof getOwnPropertySymbols === 'function';\n\nfunction getStrictPropertiesModern(object: any): Array<string | symbol> {\n  return (getOwnPropertyNames(object) as Array<string | symbol>).concat(\n    getOwnPropertySymbols(object)\n  );\n}\n\n/**\n * Get the properites used when copying objects strictly. This includes both keys and symbols.\n */\nconst getStrictProperties = SUPPORTS_SYMBOL\n  ? getStrictPropertiesModern\n  : getOwnPropertyNames;\n\n/**\n * Striclty copy all properties contained on the object.\n */\nfunction copyOwnPropertiesStrict<Value>(\n  value: Value,\n  clone: Value,\n  state: State\n): Value {\n  const properties = getStrictProperties(value);\n\n  for (\n    let index = 0, length = properties.length, property, descriptor;\n    index < length;\n    ++index\n  ) {\n    property = properties[index];\n\n    if (property === 'callee' || property === 'caller') {\n      continue;\n    }\n\n    descriptor = getOwnPropertyDescriptor(value, property);\n\n    if (!descriptor) {\n      // In extra edge cases where the property descriptor cannot be retrived, fall back to\n      // the loose assignment.\n      (clone as any)[property] = state.copier((value as any)[property], state);\n      continue;\n    }\n\n    // Only clone the value if actually a value, not a getter / setter.\n    if (!descriptor.get && !descriptor.set) {\n      descriptor.value = state.copier(descriptor.value, state);\n    }\n\n    try {\n      defineProperty(clone, property, descriptor);\n    } catch (error) {\n      // Tee above can fail on node in edge cases, so fall back to the loose assignment.\n      (clone as any)[property] = descriptor.value;\n    }\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the indexed values in the array.\n */\nexport function copyArrayLoose(array: any[], state: State) {\n  const clone = new state.Constructor();\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(array, clone);\n\n  for (let index = 0, length = array.length; index < length; ++index) {\n    clone[index] = state.copier(array[index], state);\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the indexed values in the array, as well as any custom properties.\n */\nexport function copyArrayStrict<Value extends any[]>(\n  array: Value,\n  state: State\n) {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(array, clone);\n\n  return copyOwnPropertiesStrict(array, clone, state);\n}\n\n/**\n * Copy the contents of the ArrayBuffer.\n */\nexport function copyArrayBuffer<Value extends ArrayBuffer>(\n  arrayBuffer: Value,\n  _state: State\n): Value {\n  return arrayBuffer.slice(0) as Value;\n}\n\n/**\n * Create a new Blob with the contents of the original.\n */\nexport function copyBlob<Value extends Blob>(\n  blob: Value,\n  _state: State\n): Value {\n  return blob.slice(0, blob.size, blob.type) as Value;\n}\n\n/**\n * Create a new DataView with the contents of the original.\n */\nexport function copyDataView<Value extends DataView>(\n  dataView: Value,\n  state: State\n): Value {\n  return new state.Constructor(copyArrayBuffer(dataView.buffer, state));\n}\n\n/**\n * Create a new Date based on the time of the original.\n */\nexport function copyDate<Value extends Date>(date: Value, state: State): Value {\n  return new state.Constructor(date.getTime());\n}\n\n/**\n * Deeply copy the keys and values of the original.\n */\nexport function copyMapLoose<Value extends Map<any, any>>(\n  map: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(map, clone);\n\n  map.forEach((value, key) => {\n    clone.set(key, state.copier(value, state));\n  });\n\n  return clone;\n}\n\n/**\n * Deeply copy the keys and values of the original, as well as any custom properties.\n */\nexport function copyMapStrict<Value extends Map<any, any>>(\n  map: Value,\n  state: State\n) {\n  return copyOwnPropertiesStrict(map, copyMapLoose(map, state), state);\n}\n\nfunction copyObjectLooseLegacy<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone: any = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  for (const key in object) {\n    if (hasOwnProperty.call(object, key)) {\n      clone[key] = state.copier(object[key], state);\n    }\n  }\n\n  return clone;\n}\n\nfunction copyObjectLooseModern<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  for (const key in object) {\n    if (hasOwnProperty.call(object, key)) {\n      clone[key] = state.copier(object[key], state);\n    }\n  }\n\n  const symbols = getOwnPropertySymbols(object);\n\n  for (\n    let index = 0, length = symbols.length, symbol;\n    index < length;\n    ++index\n  ) {\n    symbol = symbols[index];\n\n    if (propertyIsEnumerable.call(object, symbol)) {\n      clone[symbol] = state.copier((object as any)[symbol], state);\n    }\n  }\n\n  return clone;\n}\n\n/**\n * Deeply copy the properties (keys and symbols) and values of the original.\n */\nexport const copyObjectLoose = SUPPORTS_SYMBOL\n  ? copyObjectLooseModern\n  : copyObjectLooseLegacy;\n\n/**\n * Deeply copy the properties (keys and symbols) and values of the original, as well\n * as any hidden or non-enumerable properties.\n */\nexport function copyObjectStrict<Value extends Record<string, any>>(\n  object: Value,\n  state: State\n): Value {\n  const clone = getCleanClone(state.prototype);\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(object, clone);\n\n  return copyOwnPropertiesStrict(object, clone, state);\n}\n\n/**\n * Create a new primitive wrapper from the value of the original.\n */\nexport function copyPrimitiveWrapper<\n  // Specifically use the object constructor types\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  Value extends Boolean | Number | String\n>(primitiveObject: Value, state: State): Value {\n  return new state.Constructor(primitiveObject.valueOf());\n}\n\n/**\n * Create a new RegExp based on the value and flags of the original.\n */\nexport function copyRegExp<Value extends RegExp>(\n  regExp: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor(\n    regExp.source,\n    getRegExpFlags(regExp)\n  ) as Value;\n\n  clone.lastIndex = regExp.lastIndex;\n\n  return clone;\n}\n\n/**\n * Return the original value (an identity function).\n *\n * @note\n * THis is used for objects that cannot be copied, such as WeakMap.\n */\nexport function copySelf<Value>(value: Value, _state: State): Value {\n  return value;\n}\n\n/**\n * Deeply copy the values of the original.\n */\nexport function copySetLoose<Value extends Set<any>>(\n  set: Value,\n  state: State\n): Value {\n  const clone = new state.Constructor() as Value;\n\n  // set in the cache immediately to be able to reuse the object recursively\n  state.cache.set(set, clone);\n\n  set.forEach((value) => {\n    clone.add(state.copier(value, state));\n  });\n\n  return clone;\n}\n\n/**\n * Deeply copy the values of the original, as well as any custom properties.\n */\nexport function copySetStrict<Value extends Set<any>>(\n  set: Value,\n  state: State\n): Value {\n  return copyOwnPropertiesStrict(set, copySetLoose(set, state), state);\n}\n", "import {\n  copyArrayBuffer,\n  copyArrayLoose,\n  copyArrayStrict,\n  copyBlob,\n  copyDataView,\n  copyDate,\n  copyMapLoose,\n  copyMapStrict,\n  copyObjectLoose,\n  copyObjectStrict,\n  copyPrimitiveWrapper,\n  copyRegExp,\n  copySelf,\n  copySetLoose,\n  copySetStrict,\n} from './copier';\nimport { createCache, getTag } from './utils';\n\nimport type { InternalCopier, State } from './copier';\n\nexport type { State } from './copier';\n\nconst { isArray } = Array;\nconst { assign } = Object;\nconst getPrototypeOf = Object.getPrototypeOf || ((obj) => obj.__proto__)\n\nexport interface CreateCopierOptions {\n  array?: InternalCopier<any[]>;\n  arrayBuffer?: InternalCopier<ArrayBuffer>;\n  blob?: InternalCopier<Blob>;\n  dataView?: InternalCopier<DataView>;\n  date?: InternalCopier<Date>;\n  error?: InternalCopier<any>;\n  map?: InternalCopier<Map<any, any>>;\n  object?: InternalCopier<Record<string, any>>;\n  regExp?: InternalCopier<RegExp>;\n  set?: InternalCopier<Set<any>>;\n}\n\nconst DEFAULT_LOOSE_OPTIONS: Required<CreateCopierOptions> = {\n  array: copyArrayLoose,\n  arrayBuffer: copyArrayBuffer,\n  blob: copyBlob,\n  dataView: copyDataView,\n  date: copyDate,\n  error: copySelf,\n  map: copyMapLoose,\n  object: copyObjectLoose,\n  regExp: copyRegExp,\n  set: copySetLoose,\n};\nconst DEFAULT_STRICT_OPTIONS: Required<CreateCopierOptions> = assign(\n  {},\n  DEFAULT_LOOSE_OPTIONS,\n  {\n    array: copyArrayStrict,\n    map: copyMapStrict,\n    object: copyObjectStrict,\n    set: copySetStrict,\n  }\n);\n\n/**\n * Get the copiers used for each specific object tag.\n */\nfunction getTagSpecificCopiers(\n  options: Required<CreateCopierOptions>\n): Record<string, InternalCopier<any>> {\n  return {\n    Arguments: options.object,\n    Array: options.array,\n    ArrayBuffer: options.arrayBuffer,\n    Blob: options.blob,\n    Boolean: copyPrimitiveWrapper,\n    DataView: options.dataView,\n    Date: options.date,\n    Error: options.error,\n    Float32Array: options.arrayBuffer,\n    Float64Array: options.arrayBuffer,\n    Int8Array: options.arrayBuffer,\n    Int16Array: options.arrayBuffer,\n    Int32Array: options.arrayBuffer,\n    Map: options.map,\n    Number: copyPrimitiveWrapper,\n    Object: options.object,\n    Promise: copySelf,\n    RegExp: options.regExp,\n    Set: options.set,\n    String: copyPrimitiveWrapper,\n    WeakMap: copySelf,\n    WeakSet: copySelf,\n    Uint8Array: options.arrayBuffer,\n    Uint8ClampedArray: options.arrayBuffer,\n    Uint16Array: options.arrayBuffer,\n    Uint32Array: options.arrayBuffer,\n    Uint64Array: options.arrayBuffer,\n  };\n}\n\n/**\n * Create a custom copier based on the object-specific copy methods passed.\n */\nexport function createCopier(options: CreateCopierOptions) {\n  const normalizedOptions = assign({}, DEFAULT_LOOSE_OPTIONS, options);\n  const tagSpecificCopiers = getTagSpecificCopiers(normalizedOptions);\n  const { Array: array, Object: object } = tagSpecificCopiers;\n\n  function copier(value: any, state: State): any {\n    state.prototype = state.Constructor = undefined;\n\n    if (!value || typeof value !== 'object') {\n      return value;\n    }\n\n    if (state.cache.has(value)) {\n      return state.cache.get(value);\n    }\n\n    state.prototype = getPrototypeOf(value);\n    state.Constructor = state.prototype && state.prototype.constructor;\n\n    // plain objects\n    if (!state.Constructor || state.Constructor === Object) {\n      return object(value, state);\n    }\n\n    // arrays\n    if (isArray(value)) {\n      return array(value, state);\n    }\n\n    const tagSpecificCopier = tagSpecificCopiers[getTag(value)];\n\n    if (tagSpecificCopier) {\n      return tagSpecificCopier(value, state);\n    }\n\n    return typeof value.then === 'function' ? value : object(value, state);\n  }\n\n  return function copy<Value>(value: Value): Value {\n    return copier(value, {\n      Constructor: undefined,\n      cache: createCache(),\n      copier,\n      prototype: undefined,\n    });\n  };\n}\n\n/**\n * Create a custom copier based on the object-specific copy methods passed, defaulting to the\n * same internals as `copyStrict`.\n */\nexport function createStrictCopier(options: CreateCopierOptions) {\n  return createCopier(assign({}, DEFAULT_STRICT_OPTIONS, options));\n}\n\n/**\n * Copy an value deeply as much as possible, where strict recreation of object properties\n * are maintained. All properties (including non-enumerable ones) are copied with their\n * original property descriptors on both objects and arrays.\n */\nexport const copyStrict = createStrictCopier({});\n\n/**\n * Copy an value deeply as much as possible.\n */\nexport default createCopier({});\n"], "names": [], "mappings": ";;;;AAMQ,IAAU,gBAAgB,GAAK,QAAQ,CAAC,SAAS,SAAvB,CAAwB;AAClD,IAAA,MAAM,GAAK,MAAM,CAAA,MAAX,CAAY;AAClB,IAAU,cAAc,GAAK,MAAM,CAAC,SAAS,SAArB,CAAsB;AAEtD;;AAEG;AACH,IAAA,WAAA,kBAAA,YAAA;AAAA,IAAA,SAAA,WAAA,GAAA;QACU,IAAK,CAAA,KAAA,GAAU,EAAE,CAAC;QAClB,IAAO,CAAA,OAAA,GAAU,EAAE,CAAC;KAc7B;IAZC,WAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,GAAQ,EAAA;QACV,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KACnC,CAAA;IAED,WAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,GAAQ,EAAA;AACV,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;KAC9C,CAAA;AAED,IAAA,WAAA,CAAA,SAAA,CAAA,GAAG,GAAH,UAAI,GAAQ,EAAE,KAAU,EAAA;AACtB,QAAA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACrB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC1B,CAAA;IACH,OAAC,WAAA,CAAA;AAAD,CAAC,EAAA,CAAA,CAAA;AAED,SAAS,iBAAiB,GAAA;IACxB,OAAO,IAAI,WAAW,EAAE,CAAC;AAC3B,CAAC;AAED,SAAS,iBAAiB,GAAA;IACxB,OAAO,IAAI,OAAO,EAAE,CAAC;AACvB,CAAC;AAED;;AAEG;AACI,IAAM,WAAW,GACtB,OAAO,OAAO,KAAK,WAAW,GAAG,iBAAiB,GAAG,iBAAiB,CAAC;AAEzE;;AAEG;AACG,SAAU,aAAa,CAAC,SAAc,EAAA;IAC1C,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,KAAA;AAED,IAAA,IAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC;IAE1C,IAAI,WAAW,KAAK,MAAM,EAAE;AAC1B,QAAA,OAAO,SAAS,KAAK,MAAM,CAAC,SAAS,GAAG,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAChE,KAAA;AAED,IAAA,IACE,WAAW;QACX,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,EAC5D;QACA,IAAI;YACF,OAAO,IAAI,WAAW,EAAE,CAAC;AAC1B,SAAA;AAAC,QAAA,OAAA,EAAA,EAAM,GAAE;AACX,KAAA;AAED,IAAA,OAAO,MAAM,CAAC,SAAS,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,oBAAoB,CAAC,MAAc,EAAA;IAC1C,IAAI,KAAK,GAAG,EAAE,CAAC;IAEf,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,KAAK,IAAI,GAAG,CAAC;AACd,KAAA;IAED,IAAI,MAAM,CAAC,UAAU,EAAE;QACrB,KAAK,IAAI,GAAG,CAAC;AACd,KAAA;IAED,IAAI,MAAM,CAAC,SAAS,EAAE;QACpB,KAAK,IAAI,GAAG,CAAC;AACd,KAAA;IAED,IAAI,MAAM,CAAC,OAAO,EAAE;QAClB,KAAK,IAAI,GAAG,CAAC;AACd,KAAA;IAED,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,KAAK,IAAI,GAAG,CAAC;AACd,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,oBAAoB,CAAC,MAAc,EAAA;IAC1C,OAAO,MAAM,CAAC,KAAK,CAAC;AACtB,CAAC;AAED;;AAEG;AACI,IAAM,cAAc,GACzB,OAAO,CAAC,KAAK,KAAK,GAAG,GAAG,oBAAoB,GAAG,oBAAoB,CAAC;AAEtE,SAAS,YAAY,CAAC,KAAU,EAAA;IAC9B,IAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAExC,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,CAAC;AAED,SAAS,YAAY,CAAC,KAAU,EAAA;IAC9B,OAAO,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1D,CAAC;AAED;;AAEG;AACI,IAAM,MAAM,GACjB,OAAO,MAAM,KAAK,WAAW,GAAG,YAAY,GAAG,YAAY;;AC3G3D,IAAA,cAAc,GAIZ,MAAM,eAJM,EACd,wBAAwB,GAGtB,MAAM,CAAA,wBAHgB,EACxB,mBAAmB,GAEjB,MAAM,CAFW,mBAAA,EACnB,qBAAqB,GACnB,MAAM,sBADa,CACZ;AACL,IAAA,EAAA,GAA2C,MAAM,CAAC,SAAS,EAAzD,cAAc,GAAA,EAAA,CAAA,cAAA,EAAE,oBAAoB,GAAA,EAAA,CAAA,oBAAqB,CAAC;AAElE,IAAM,eAAe,GAAG,OAAO,qBAAqB,KAAK,UAAU,CAAC;AAEpE,SAAS,yBAAyB,CAAC,MAAW,EAAA;AAC5C,IAAA,OAAQ,mBAAmB,CAAC,MAAM,CAA4B,CAAC,MAAM,CACnE,qBAAqB,CAAC,MAAM,CAAC,CAC9B,CAAC;AACJ,CAAC;AAED;;AAEG;AACH,IAAM,mBAAmB,GAAG,eAAe;AACzC,MAAE,yBAAyB;MACzB,mBAAmB,CAAC;AAExB;;AAEG;AACH,SAAS,uBAAuB,CAC9B,KAAY,EACZ,KAAY,EACZ,KAAY,EAAA;AAEZ,IAAA,IAAM,UAAU,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAE9C,KACE,IAAI,KAAK,GAAG,CAAC,EAAE,QAAM,GAAG,UAAU,CAAC,MAAM,EAAE,QAAQ,GAAA,KAAA,CAAA,EAAE,UAAU,GAAA,KAAA,CAAA,EAC/D,KAAK,GAAG,QAAM,EACd,EAAE,KAAK,EACP;AACA,QAAA,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAE7B,QAAA,IAAI,QAAQ,KAAK,QAAQ,IAAI,QAAQ,KAAK,QAAQ,EAAE;YAClD,SAAS;AACV,SAAA;AAED,QAAA,UAAU,GAAG,wBAAwB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,UAAU,EAAE;;;AAGd,YAAA,KAAa,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,MAAM,CAAE,KAAa,CAAC,QAAQ,CAAC,EAAE,KAAK,CAAC,CAAC;YACzE,SAAS;AACV,SAAA;;QAGD,IAAI,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE;AACtC,YAAA,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1D,SAAA;QAED,IAAI;AACF,YAAA,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC7C,SAAA;AAAC,QAAA,OAAO,KAAK,EAAE;;AAEb,YAAA,KAAa,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC;AAC7C,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;AACa,SAAA,cAAc,CAAC,KAAY,EAAE,KAAY,EAAA;AACvD,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;;IAGtC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAE9B,IAAA,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,QAAM,GAAG,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,QAAM,EAAE,EAAE,KAAK,EAAE;AAClE,QAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;AAClD,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;AACa,SAAA,eAAe,CAC7B,KAAY,EACZ,KAAY,EAAA;AAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAW,CAAC;;IAG/C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAE9B,OAAO,uBAAuB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACtD,CAAC;AAED;;AAEG;AACa,SAAA,eAAe,CAC7B,WAAkB,EAClB,MAAa,EAAA;AAEb,IAAA,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,CAAU,CAAC;AACvC,CAAC;AAED;;AAEG;AACa,SAAA,QAAQ,CACtB,IAAW,EACX,MAAa,EAAA;AAEb,IAAA,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAU,CAAC;AACtD,CAAC;AAED;;AAEG;AACa,SAAA,YAAY,CAC1B,QAAe,EACf,KAAY,EAAA;AAEZ,IAAA,OAAO,IAAI,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAa,CAAC,CAAC,CAAC;AACxE,CAAC;AAED;;AAEG;AACa,SAAA,QAAQ,CAAqB,IAAW,EAAE,KAAY,EAAA;IACpE,OAAO,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AAC/C,CAAC;AAED;;AAEG;AACa,SAAA,YAAY,CAC1B,GAAU,EACV,KAAY,EAAA;AAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAW,CAAC;;IAG/C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAE5B,IAAA,GAAG,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,GAAG,EAAA;AACrB,QAAA,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7C,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;AACa,SAAA,aAAa,CAC3B,GAAU,EACV,KAAY,EAAA;AAEZ,IAAA,OAAO,uBAAuB,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;AACvE,CAAC;AAED,SAAS,qBAAqB,CAC5B,MAAa,EACb,KAAY,EAAA;IAEZ,IAAM,KAAK,GAAQ,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;IAGlD,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAE/B,IAAA,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;QACxB,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AACpC,YAAA,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/C,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,qBAAqB,CAC5B,MAAa,EACb,KAAY,EAAA;IAEZ,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;IAG7C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAE/B,IAAA,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;QACxB,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;AACpC,YAAA,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/C,SAAA;AACF,KAAA;AAED,IAAA,IAAM,OAAO,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;IAE9C,KACE,IAAI,KAAK,GAAG,CAAC,EAAE,QAAM,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,GAAA,KAAA,CAAA,EAC9C,KAAK,GAAG,QAAM,EACd,EAAE,KAAK,EACP;AACA,QAAA,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;QAExB,IAAI,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE;AAC7C,YAAA,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAE,MAAc,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;AAC9D,SAAA;AACF,KAAA;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;AACI,IAAM,eAAe,GAAG,eAAe;AAC5C,MAAE,qBAAqB;MACrB,qBAAqB,CAAC;AAE1B;;;AAGG;AACa,SAAA,gBAAgB,CAC9B,MAAa,EACb,KAAY,EAAA;IAEZ,IAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;;IAG7C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAE/B,OAAO,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACvD,CAAC;AAED;;AAEG;AACa,SAAA,oBAAoB,CAIlC,eAAsB,EAAE,KAAY,EAAA;IACpC,OAAO,IAAI,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED;;AAEG;AACa,SAAA,UAAU,CACxB,MAAa,EACb,KAAY,EAAA;AAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,CACjC,MAAM,CAAC,MAAM,EACb,cAAc,CAAC,MAAM,CAAC,CACd,CAAC;AAEX,IAAA,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAEnC,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;AAKG;AACa,SAAA,QAAQ,CAAQ,KAAY,EAAE,MAAa,EAAA;AACzD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;AACa,SAAA,YAAY,CAC1B,GAAU,EACV,KAAY,EAAA;AAEZ,IAAA,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,WAAW,EAAW,CAAC;;IAG/C,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAE5B,IAAA,GAAG,CAAC,OAAO,CAAC,UAAC,KAAK,EAAA;AAChB,QAAA,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AACxC,KAAC,CAAC,CAAC;AAEH,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;AACa,SAAA,aAAa,CAC3B,GAAU,EACV,KAAY,EAAA;AAEZ,IAAA,OAAO,uBAAuB,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;AACvE;;ACtSQ,IAAA,OAAO,GAAK,KAAK,CAAA,OAAV,CAAW;AAClB,IAAA,MAAM,GAAK,MAAM,CAAA,MAAX,CAAY;AAC1B,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,KAAK,UAAC,GAAG,EAAA,EAAK,OAAA,GAAG,CAAC,SAAS,CAAb,EAAa,CAAC,CAAA;AAexE,IAAM,qBAAqB,GAAkC;AAC3D,IAAA,KAAK,EAAE,cAAc;AACrB,IAAA,WAAW,EAAE,eAAe;AAC5B,IAAA,IAAI,EAAE,QAAQ;AACd,IAAA,QAAQ,EAAE,YAAY;AACtB,IAAA,IAAI,EAAE,QAAQ;AACd,IAAA,KAAK,EAAE,QAAQ;AACf,IAAA,GAAG,EAAE,YAAY;AACjB,IAAA,MAAM,EAAE,eAAe;AACvB,IAAA,MAAM,EAAE,UAAU;AAClB,IAAA,GAAG,EAAE,YAAY;CAClB,CAAC;AACF,IAAM,sBAAsB,GAAkC,MAAM,CAClE,EAAE,EACF,qBAAqB,EACrB;AACE,IAAA,KAAK,EAAE,eAAe;AACtB,IAAA,GAAG,EAAE,aAAa;AAClB,IAAA,MAAM,EAAE,gBAAgB;AACxB,IAAA,GAAG,EAAE,aAAa;AACnB,CAAA,CACF,CAAC;AAEF;;AAEG;AACH,SAAS,qBAAqB,CAC5B,OAAsC,EAAA;IAEtC,OAAO;QACL,SAAS,EAAE,OAAO,CAAC,MAAM;QACzB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,IAAI,EAAE,OAAO,CAAC,IAAI;AAClB,QAAA,OAAO,EAAE,oBAAoB;QAC7B,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;QAClB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,YAAY,EAAE,OAAO,CAAC,WAAW;QACjC,YAAY,EAAE,OAAO,CAAC,WAAW;QACjC,SAAS,EAAE,OAAO,CAAC,WAAW;QAC9B,UAAU,EAAE,OAAO,CAAC,WAAW;QAC/B,UAAU,EAAE,OAAO,CAAC,WAAW;QAC/B,GAAG,EAAE,OAAO,CAAC,GAAG;AAChB,QAAA,MAAM,EAAE,oBAAoB;QAC5B,MAAM,EAAE,OAAO,CAAC,MAAM;AACtB,QAAA,OAAO,EAAE,QAAQ;QACjB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,GAAG,EAAE,OAAO,CAAC,GAAG;AAChB,QAAA,MAAM,EAAE,oBAAoB;AAC5B,QAAA,OAAO,EAAE,QAAQ;AACjB,QAAA,OAAO,EAAE,QAAQ;QACjB,UAAU,EAAE,OAAO,CAAC,WAAW;QAC/B,iBAAiB,EAAE,OAAO,CAAC,WAAW;QACtC,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,WAAW,EAAE,OAAO,CAAC,WAAW;KACjC,CAAC;AACJ,CAAC;AAED;;AAEG;AACG,SAAU,YAAY,CAAC,OAA4B,EAAA;IACvD,IAAM,iBAAiB,GAAG,MAAM,CAAC,EAAE,EAAE,qBAAqB,EAAE,OAAO,CAAC,CAAC;AACrE,IAAA,IAAM,kBAAkB,GAAG,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;IAC5D,IAAO,KAAK,GAAqB,kBAAkB,CAAA,KAAvC,EAAU,MAAM,GAAK,kBAAkB,CAAA,MAAvB,CAAwB;AAE5D,IAAA,SAAS,MAAM,CAAC,KAAU,EAAE,KAAY,EAAA;QACtC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC;AAEhD,QAAA,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACvC,YAAA,OAAO,KAAK,CAAC;AACd,SAAA;QAED,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC1B,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,SAAA;AAED,QAAA,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AACxC,QAAA,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC;;QAGnE,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,KAAK,MAAM,EAAE;AACtD,YAAA,OAAO,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC7B,SAAA;;AAGD,QAAA,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;AAClB,YAAA,OAAO,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5B,SAAA;QAED,IAAM,iBAAiB,GAAG,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAE5D,QAAA,IAAI,iBAAiB,EAAE;AACrB,YAAA,OAAO,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxC,SAAA;AAED,QAAA,OAAO,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;KACxE;IAED,OAAO,SAAS,IAAI,CAAQ,KAAY,EAAA;QACtC,OAAO,MAAM,CAAC,KAAK,EAAE;AACnB,YAAA,WAAW,EAAE,SAAS;YACtB,KAAK,EAAE,WAAW,EAAE;AACpB,YAAA,MAAM,EAAA,MAAA;AACN,YAAA,SAAS,EAAE,SAAS;AACrB,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AACJ,CAAC;AAED;;;AAGG;AACG,SAAU,kBAAkB,CAAC,OAA4B,EAAA;IAC7D,OAAO,YAAY,CAAC,MAAM,CAAC,EAAE,EAAE,sBAAsB,EAAE,OAAO,CAAC,CAAC,CAAC;AACnE,CAAC;AAED;;;;AAIG;IACU,UAAU,GAAG,kBAAkB,CAAC,EAAE,EAAE;AAEjD;;AAEG;AACH,YAAe,YAAY,CAAC,EAAE,CAAC;;;;;;;"}
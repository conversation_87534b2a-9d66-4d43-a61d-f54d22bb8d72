import { create } from 'zustand'
import { authService, User, LoginCredentials, RegisterData } from '@/services/authService'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  register: (data: RegisterData) => Promise<void>
  logout: () => Promise<void>
  getCurrentUser: () => Promise<void>
  changePassword: (oldPassword: string, newPassword: string) => Promise<void>
  clearError: () => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  login: async (credentials: LoginCredentials) => {
    set({ isLoading: true, error: null })
    
    try {
      const authData = await authService.login(credentials)
      set({
        user: authData.user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message,
        isAuthenticated: false,
        user: null
      })
      throw error
    }
  },

  register: async (data: RegisterData) => {
    set({ isLoading: true, error: null })
    
    try {
      const authData = await authService.register(data)
      set({
        user: authData.user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message,
        isAuthenticated: false,
        user: null
      })
      throw error
    }
  },

  logout: async () => {
    set({ isLoading: true })
    
    try {
      await authService.logout()
      set({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null
      })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message
      })
    }
  },

  getCurrentUser: async () => {
    if (!authService.isAuthenticated()) {
      set({ isAuthenticated: false, user: null })
      return
    }

    set({ isLoading: true, error: null })
    
    try {
      const user = await authService.getCurrentUser()
      set({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null
      })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message,
        isAuthenticated: false,
        user: null
      })
    }
  },

  changePassword: async (oldPassword: string, newPassword: string) => {
    set({ isLoading: true, error: null })
    
    try {
      await authService.changePassword(oldPassword, newPassword)
      set({ isLoading: false, error: null })
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message
      })
      throw error
    }
  },

  clearError: () => {
    set({ error: null })
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading })
  }
}))

// 选择器函数
export const selectUser = () => useAuthStore(state => state.user)
export const selectIsAuthenticated = () => useAuthStore(state => state.isAuthenticated)
export const selectIsLoading = () => useAuthStore(state => state.isLoading)
export const selectError = () => useAuthStore(state => state.error)

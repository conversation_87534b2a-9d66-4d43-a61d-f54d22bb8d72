import { useState, useEffect } from 'react'

interface MarketData {
  symbol: string
  timestamp: number
  bid: number
  ask: number
  last: number
  volume: number
  high: number
  low: number
  open: number
  close: number
  change?: number
  changePercent?: number
}

interface WebSocketMessage {
  type: string
  clientId?: string
  symbol?: string
  data?: any
  timestamp: number
}

function App() {
  const [isConnected, setIsConnected] = useState(false)
  const [marketData, setMarketData] = useState<Record<string, MarketData>>({})
  const [messages, setMessages] = useState<WebSocketMessage[]>([])
  const [ws, setWs] = useState<WebSocket | null>(null)

  useEffect(() => {
    // 连接WebSocket
    const websocket = new WebSocket('ws://localhost:3001/ws')
    
    websocket.onopen = () => {
      console.log('WebSocket connected')
      setIsConnected(true)
      setWs(websocket)
      
      // 订阅一些交易对
      const symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
      symbols.forEach(symbol => {
        websocket.send(JSON.stringify({
          type: 'subscribe',
          payload: { channel: 'price', symbol }
        }))
      })
    }

    websocket.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        setMessages(prev => [message, ...prev.slice(0, 49)]) // 保留最近50条消息
        
        if (message.type === 'price_update' && message.symbol && message.data) {
          setMarketData(prev => ({
            ...prev,
            [message.symbol!]: message.data
          }))
        }
      } catch (error) {
        console.error('Failed to parse message:', error)
      }
    }

    websocket.onclose = () => {
      console.log('WebSocket disconnected')
      setIsConnected(false)
    }

    websocket.onerror = (error) => {
      console.error('WebSocket error:', error)
    }

    return () => {
      websocket.close()
    }
  }, [])

  const formatPrice = (price: number) => {
    if (price >= 1000) return price.toFixed(2)
    if (price >= 1) return price.toFixed(4)
    return price.toFixed(6)
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`
    return volume.toFixed(0)
  }

  return (
    <div style={{ fontFamily: 'Arial, sans-serif', padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* 标题 */}
      <div style={{ marginBottom: '30px', textAlign: 'center' }}>
        <h1 style={{ color: '#333', marginBottom: '10px' }}>🚀 SFQuant 量化交易系统</h1>
        <div style={{ 
          display: 'inline-flex', 
          alignItems: 'center', 
          padding: '8px 16px', 
          borderRadius: '20px',
          backgroundColor: isConnected ? '#d4edda' : '#f8d7da',
          color: isConnected ? '#155724' : '#721c24',
          border: `1px solid ${isConnected ? '#c3e6cb' : '#f5c6cb'}`
        }}>
          <div style={{ 
            width: '8px', 
            height: '8px', 
            borderRadius: '50%', 
            backgroundColor: isConnected ? '#28a745' : '#dc3545',
            marginRight: '8px',
            animation: isConnected ? 'pulse 2s infinite' : 'none'
          }} />
          {isConnected ? '实时连接' : '连接断开'}
        </div>
      </div>

      {/* 市场数据表格 */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>📊 实时市场数据</h2>
        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          overflow: 'hidden'
        }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>交易对</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>价格</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>24h变化</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>成交量</th>
                <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>更新时间</th>
              </tr>
            </thead>
            <tbody>
              {Object.values(marketData).map((data) => (
                <tr key={data.symbol} style={{ borderBottom: '1px solid #dee2e6' }}>
                  <td style={{ padding: '12px', fontWeight: 'bold' }}>{data.symbol}</td>
                  <td style={{ padding: '12px', textAlign: 'right', fontFamily: 'monospace' }}>
                    ${formatPrice(data.last)}
                  </td>
                  <td style={{ 
                    padding: '12px', 
                    textAlign: 'right',
                    color: (data.changePercent || 0) >= 0 ? '#28a745' : '#dc3545',
                    fontWeight: 'bold'
                  }}>
                    {(data.changePercent || 0) >= 0 ? '+' : ''}{(data.changePercent || 0).toFixed(2)}%
                  </td>
                  <td style={{ padding: '12px', textAlign: 'right', fontFamily: 'monospace' }}>
                    {formatVolume(data.volume)}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'right', fontSize: '12px', color: '#666' }}>
                    {new Date(data.timestamp).toLocaleTimeString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {Object.keys(marketData).length === 0 && (
            <div style={{ padding: '40px', textAlign: 'center', color: '#666' }}>
              <div style={{ fontSize: '24px', marginBottom: '10px' }}>⏳</div>
              <div>正在加载市场数据...</div>
            </div>
          )}
        </div>
      </div>

      {/* WebSocket消息日志 */}
      <div>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>📡 实时消息日志</h2>
        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          padding: '15px',
          height: '300px',
          overflow: 'auto'
        }}>
          {messages.map((message, index) => (
            <div key={index} style={{ 
              marginBottom: '8px', 
              padding: '8px', 
              backgroundColor: '#f8f9fa', 
              borderRadius: '4px',
              fontSize: '12px',
              fontFamily: 'monospace'
            }}>
              <div style={{ color: '#666', marginBottom: '4px' }}>
                {new Date(message.timestamp).toLocaleTimeString()} - {message.type}
              </div>
              <div style={{ color: '#333' }}>
                {JSON.stringify(message, null, 2)}
              </div>
            </div>
          ))}
          
          {messages.length === 0 && (
            <div style={{ textAlign: 'center', color: '#666', paddingTop: '50px' }}>
              <div style={{ fontSize: '24px', marginBottom: '10px' }}>📝</div>
              <div>等待WebSocket消息...</div>
            </div>
          )}
        </div>
      </div>

      {/* CSS动画 */}
      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  )
}

export default App

import axios from 'axios'

const API_BASE_URL = 'http://localhost:3001/api/v1'

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  name: string
  username?: string
}

export interface User {
  id: string
  email: string
  username?: string
  name: string
  role: string
  isActive: boolean
  isVerified: boolean
}

export interface AuthTokens {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export interface AuthResponse {
  user: User
  tokens: AuthTokens
}

class AuthService {
  private accessToken: string | null = null
  private refreshToken: string | null = null

  constructor() {
    // 从localStorage恢复token
    this.accessToken = localStorage.getItem('accessToken')
    this.refreshToken = localStorage.getItem('refreshToken')
    
    // 设置axios默认headers
    if (this.accessToken) {
      this.setAuthHeader(this.accessToken)
    }

    // 设置响应拦截器处理token过期
    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401 && this.refreshToken) {
          try {
            await this.refreshAccessToken()
            // 重试原请求
            return axios.request(error.config)
          } catch (refreshError) {
            this.logout()
            window.location.href = '/login'
          }
        }
        return Promise.reject(error)
      }
    )
  }

  // 用户注册
  async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/register`, data)
      const authData = response.data.data
      
      this.setTokens(authData.tokens)
      return authData
    } catch (error: any) {
      throw new Error(error.response?.data?.error || '注册失败')
    }
  }

  // 用户登录
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, credentials)
      const authData = response.data.data
      
      this.setTokens(authData.tokens)
      return authData
    } catch (error: any) {
      throw new Error(error.response?.data?.error || '登录失败')
    }
  }

  // 用户登出
  async logout(): Promise<void> {
    try {
      if (this.accessToken) {
        await axios.post(`${API_BASE_URL}/auth/logout`)
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.clearTokens()
    }
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    try {
      const response = await axios.get(`${API_BASE_URL}/auth/me`)
      return response.data.data.user
    } catch (error: any) {
      throw new Error(error.response?.data?.error || '获取用户信息失败')
    }
  }

  // 修改密码
  async changePassword(oldPassword: string, newPassword: string): Promise<void> {
    try {
      await axios.post(`${API_BASE_URL}/auth/change-password`, {
        oldPassword,
        newPassword
      })
    } catch (error: any) {
      throw new Error(error.response?.data?.error || '修改密码失败')
    }
  }

  // 获取用户设置
  async getUserSettings(): Promise<any> {
    try {
      const response = await axios.get(`${API_BASE_URL}/auth/settings`)
      return response.data.data.settings
    } catch (error: any) {
      throw new Error(error.response?.data?.error || '获取用户设置失败')
    }
  }

  // 更新用户设置
  async updateUserSettings(settings: any): Promise<any> {
    try {
      const response = await axios.put(`${API_BASE_URL}/auth/settings`, settings)
      return response.data.data.settings
    } catch (error: any) {
      throw new Error(error.response?.data?.error || '更新用户设置失败')
    }
  }

  // 刷新访问令牌
  private async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
        refreshToken: this.refreshToken
      })
      
      const tokens = response.data.data.tokens
      this.setTokens(tokens)
    } catch (error) {
      this.clearTokens()
      throw error
    }
  }

  // 设置tokens
  private setTokens(tokens: AuthTokens): void {
    this.accessToken = tokens.accessToken
    this.refreshToken = tokens.refreshToken
    
    localStorage.setItem('accessToken', tokens.accessToken)
    localStorage.setItem('refreshToken', tokens.refreshToken)
    
    this.setAuthHeader(tokens.accessToken)
  }

  // 清除tokens
  private clearTokens(): void {
    this.accessToken = null
    this.refreshToken = null
    
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    
    delete axios.defaults.headers.common['Authorization']
  }

  // 设置认证头
  private setAuthHeader(token: string): void {
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!this.accessToken
  }

  // 获取访问令牌
  getAccessToken(): string | null {
    return this.accessToken
  }
}

export const authService = new AuthService()

describe('Basic Tests', () => {
  it('should pass basic math test', () => {
    expect(1 + 1).toBe(2)
  })

  it('should test async functionality', async () => {
    const promise = Promise.resolve('test')
    await expect(promise).resolves.toBe('test')
  })

  it('should test mock functionality', () => {
    const mockFn = jest.fn()
    mockFn('test')
    expect(mockFn).toHaveBeenCalledWith('test')
  })

  it('should test object properties', () => {
    const obj = {
      name: 'SFQuant',
      version: '1.0.0',
      features: ['DEX', 'CEX', 'WebSocket']
    }

    expect(obj).toHaveProperty('name', 'SFQuant')
    expect(obj.features).toContain('DEX')
    expect(obj.features).toHaveLength(3)
  })
})

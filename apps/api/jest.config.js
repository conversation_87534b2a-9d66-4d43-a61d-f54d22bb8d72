module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/tests'],
  testMatch: [
    '**/__tests__/**/*.ts',
    '**/?(*.)+(spec|test).ts'
  ],
  transform: {
    '^.+\\.ts$': ['ts-jest', {
      useESM: false
    }]
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/index.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html'],
  moduleNameMapper: {
    '^@sfquant/core$': '<rootDir>/../../packages/core/src',
    '^@sfquant/ccxt-adapter$': '<rootDir>/../../packages/ccxt-adapter/src',
    '^@sfquant/dex-adapter$': '<rootDir>/../../packages/dex-adapter/src',
    '^@sfquant/strategy-runtime$': '<rootDir>/../../packages/strategy-runtime/src'
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testTimeout: 30000
}

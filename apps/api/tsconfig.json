{"extends": "../../tools/typescript-config/base.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "baseUrl": ".", "skipLibCheck": true, "paths": {"@sfquant/core": ["../../packages/core/dist"], "@sfquant/ccxt-adapter": ["../../packages/ccxt-adapter/dist"], "@sfquant/dex-adapter": ["../../packages/dex-adapter/dist"], "@sfquant/strategy-runtime": ["../../packages/strategy-runtime/dist"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}
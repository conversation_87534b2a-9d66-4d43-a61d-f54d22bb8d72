{"name": "@sfquant/api", "version": "1.0.0", "description": "SFQuant API Service", "main": "dist/index.js", "scripts": {"dev": "npm run build && node dist/index.js", "dev:watch": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest"}, "dependencies": {"@sfquant/core": "workspace:*", "@sfquant/ccxt-adapter": "workspace:*", "@sfquant/dex-adapter": "workspace:*", "@sfquant/strategy-runtime": "workspace:*", "fastify": "^4.24.0", "@fastify/cors": "^8.4.0", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^2.1.0", "@fastify/rate-limit": "^9.0.0", "@fastify/helmet": "^11.1.0", "@fastify/jwt": "^7.2.0", "@fastify/websocket": "^8.3.0", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "redis": "^4.6.0", "zod": "^3.22.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.0", "ws": "^8.14.0", "node-cron": "^3.0.0", "axios": "^1.6.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/bcryptjs": "^2.4.0", "@types/jsonwebtoken": "^9.0.0", "@types/ws": "^8.5.0", "@types/node-cron": "^3.0.0", "tsx": "^4.6.0", "typescript": "^5.3.0", "jest": "^29.7.0", "@types/jest": "^29.5.0"}}
{"name": "@sfquant/api", "version": "1.0.0", "description": "SFQuant API Service", "main": "dist/index.js", "scripts": {"dev": "tsx src/index.ts", "dev:watch": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest"}, "dependencies": {"@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.0", "@fastify/jwt": "^7.2.0", "@fastify/rate-limit": "^9.0.0", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^2.1.0", "@fastify/websocket": "^8.3.0", "@prisma/client": "^5.6.0", "@sfquant/ccxt-adapter": "workspace:*", "@sfquant/core": "workspace:*", "@sfquant/dex-adapter": "workspace:*", "@sfquant/strategy-runtime": "workspace:*", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "fastify": "^4.24.0", "jsonwebtoken": "^9.0.0", "node-cron": "^3.0.0", "prisma": "^5.6.0", "redis": "^4.6.0", "ws": "^8.14.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.0", "@types/jest": "^29.5.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.0", "@types/ws": "^8.5.0", "jest": "^29.7.0", "pino-pretty": "^13.0.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}}
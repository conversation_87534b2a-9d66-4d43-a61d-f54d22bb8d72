{"version": 3, "file": "StrategyRuntime.js", "sourceRoot": "", "sources": ["../../../../../../packages/strategy-runtime/src/StrategyRuntime.ts"], "names": [], "mappings": "AAsBA,UAAU;AACV,MAAM,OAAgB,mBAAmB;IAO7B,iBAAiB,CAAC,OAAwB;QAClD,OAAO;YACL,OAAO;YACP,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,SAAS,EAAE,OAAO,CAAC,SAAS;YAE5B,OAAO;YACP,QAAQ,EAAE;gBACR,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,EAAE;gBACvB,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;gBAC3B,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;gBAC3B,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACvC,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,SAAS;gBACrC,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,OAAO;aAClC;YAED,OAAO;YACP,KAAK,EAAE;gBACL,GAAG,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC9E,IAAI,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAChF,KAAK,EAAE,CAAC,OAAe,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;aACnF;YAED,cAAc;YACd,UAAU,EAAE;gBACV,GAAG,EAAE,CAAC,MAAgB,EAAE,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC5E,GAAG,EAAE,CAAC,MAAgB,EAAE,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC5E,GAAG,EAAE,CAAC,MAAgB,EAAE,MAAc,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC5E,IAAI,EAAE,CAAC,MAAgB,EAAE,IAAY,EAAE,IAAY,EAAE,MAAc,EAAE,EAAE,CACrE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC;aACjD;SACF,CAAA;IACH,CAAC;IAES,YAAY,CACpB,IAA6B,EAC7B,MAAc,EACd,MAAe,EACf,KAAc,EACd,UAAmB,EACnB,QAA8B;QAE9B,OAAO;YACL,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,UAAU;YACV,QAAQ;SACT,CAAA;IACH,CAAC;IAED,YAAY;IACJ,YAAY,CAAC,MAAgB,EAAE,MAAc;QACnD,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChD,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;YAC1E,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,CAAA;QAC3B,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,YAAY,CAAC,MAAgB,EAAE,MAAc;QACnD,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAEnC,YAAY;QACZ,IAAI,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;QACrE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAEhB,aAAa;QACb,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5C,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,GAAG,CAAA;YAC1C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAClB,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,YAAY,CAAC,MAAgB,EAAE,MAAc;QACnD,MAAM,MAAM,GAAa,EAAE,CAAA;QAC3B,MAAM,KAAK,GAAa,EAAE,CAAA;QAC1B,MAAM,MAAM,GAAa,EAAE,CAAA;QAE3B,SAAS;QACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YACxC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACnC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACvC,CAAC;QAED,QAAQ;QACR,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;YACtF,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,CAAA;YAEvF,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAClB,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,GAAG,OAAO,GAAG,OAAO,CAAA;gBAC5B,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;gBAClC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAClB,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAA;IACf,CAAC;IAEO,aAAa,CAAC,MAAgB,EAAE,IAAY,EAAE,IAAY,EAAE,MAAc;QAKhF,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAE/C,UAAU;QACV,MAAM,IAAI,GAAa,EAAE,CAAA;QACzB,MAAM,UAAU,GAAG,IAAI,GAAG,IAAI,CAAA;QAC9B,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACjD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAA;QACjD,CAAC;QAED,QAAQ;QACR,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QAElD,QAAQ;QACR,MAAM,SAAS,GAAa,EAAE,CAAA;QAC9B,MAAM,gBAAgB,GAAG,MAAM,GAAG,CAAC,CAAA;QACnC,KAAK,IAAI,CAAC,GAAG,gBAAgB,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAA;QAC5D,CAAC;QAED,OAAO;YACL,IAAI;YACJ,MAAM,EAAE,UAAU;YAClB,SAAS;SACV,CAAA;IACH,CAAC;CACF;AAED,WAAW;AACX,MAAM,OAAO,sBAAsB;IAAnC;QACU,aAAQ,GAA2C,IAAI,GAAG,EAAE,CAAA;IAgDtE,CAAC;IA9CC,eAAe,CAAC,OAAwB;QACtC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAC9C,CAAC;IAED,UAAU,CAAC,QAA0B;QACnC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IACpC,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,QAA0B,EAC1B,IAAY,EACZ,OAAwB;QAExB,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,YAAY,CAAC,CAAA;QAC/D,CAAC;QAED,IAAI,CAAC;YACH,OAAO,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAA;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAA0B,EAAE,IAAY;QAC7D,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAA;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;gBACL,KAAK,EAAE,KAAK;gBACZ,MAAM,EAAE,CAAC,wBAAwB,QAAQ,YAAY,CAAC;gBACtD,QAAQ,EAAE,EAAE;aACb,CAAA;QACH,CAAC;QAED,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAA;QACrF,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC3B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;IACvB,CAAC;CACF"}
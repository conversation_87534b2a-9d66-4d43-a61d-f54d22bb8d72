import { StrategyLanguage, StrategyContext, StrategyResult, Signal } from '@sfquant/core';
export interface StrategyRuntime {
    readonly language: StrategyLanguage;
    execute(code: string, context: StrategyContext): Promise<StrategyResult>;
    validate(code: string): Promise<ValidationResult>;
    dispose(): Promise<void>;
}
export interface ValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}
export declare abstract class BaseStrategyRuntime implements StrategyRuntime {
    abstract readonly language: StrategyLanguage;
    abstract execute(code: string, context: StrategyContext): Promise<StrategyResult>;
    abstract validate(code: string): Promise<ValidationResult>;
    abstract dispose(): Promise<void>;
    protected createSafeContext(context: StrategyContext): {
        marketData: Record<string, {
            symbol: string;
            open: number;
            timestamp: number;
            bid: number;
            ask: number;
            last: number;
            volume: number;
            high: number;
            low: number;
            close: number;
        }>;
        orderBooks: Record<string, {
            symbol: string;
            timestamp: number;
            bids: {
                price: number;
                amount: number;
            }[];
            asks: {
                price: number;
                amount: number;
            }[];
        }>;
        balances: Record<string, {
            currency: string;
            free: number;
            used: number;
            total: number;
        }>;
        openOrders: {
            symbol: string;
            timestamp: number;
            id: string;
            type: "limit" | "market" | "stop" | "stop_limit";
            status: "open" | "pending" | "closed" | "canceled" | "rejected";
            amount: number;
            side: "buy" | "sell";
            filled: number;
            remaining: number;
            clientOrderId?: string | undefined;
            price?: number | undefined;
            fee?: {
                currency: string;
                cost: number;
            } | undefined;
        }[];
        timestamp: number;
        strategy: {
            id: string;
            name: string;
            type: "arbitrage" | "trend" | "high_frequency" | "market_maker" | "ai_ml";
            parameters: Record<string, any>;
            exchanges: string[];
            symbols: string[];
        };
        utils: {
            log: (message: string) => void;
            warn: (message: string) => void;
            error: (message: string) => void;
        };
        indicators: {
            sma: (prices: number[], period: number) => number[];
            ema: (prices: number[], period: number) => number[];
            rsi: (prices: number[], period: number) => number[];
            macd: (prices: number[], fast: number, slow: number, signal: number) => {
                macd: number[];
                signal: number[];
                histogram: number[];
            };
        };
    };
    protected createSignal(type: 'buy' | 'sell' | 'hold', symbol: string, amount?: number, price?: number, confidence?: number, metadata?: Record<string, any>): Signal;
    private calculateSMA;
    private calculateEMA;
    private calculateRSI;
    private calculateMACD;
}
export declare class StrategyRuntimeManager {
    private runtimes;
    registerRuntime(runtime: StrategyRuntime): void;
    getRuntime(language: StrategyLanguage): StrategyRuntime | undefined;
    executeStrategy(language: StrategyLanguage, code: string, context: StrategyContext): Promise<StrategyResult>;
    validateStrategy(language: StrategyLanguage, code: string): Promise<ValidationResult>;
    dispose(): Promise<void>;
}
//# sourceMappingURL=StrategyRuntime.d.ts.map
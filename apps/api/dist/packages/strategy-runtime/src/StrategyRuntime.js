// 基础策略运行时
export class BaseStrategyRuntime {
    createSafeContext(context) {
        return {
            // 市场数据
            marketData: context.marketData,
            orderBooks: context.orderBooks,
            balances: context.balances,
            openOrders: context.openOrders,
            timestamp: context.timestamp,
            // 策略配置
            strategy: {
                id: context.strategy.id,
                name: context.strategy.name,
                type: context.strategy.type,
                parameters: context.strategy.parameters,
                exchanges: context.strategy.exchanges,
                symbols: context.strategy.symbols
            },
            // 工具函数
            utils: {
                log: (message) => console.log(`[${context.strategy.name}] ${message}`),
                warn: (message) => console.warn(`[${context.strategy.name}] ${message}`),
                error: (message) => console.error(`[${context.strategy.name}] ${message}`)
            },
            // 技术指标库 (简化版)
            indicators: {
                sma: (prices, period) => this.calculateSMA(prices, period),
                ema: (prices, period) => this.calculateEMA(prices, period),
                rsi: (prices, period) => this.calculateRSI(prices, period),
                macd: (prices, fast, slow, signal) => this.calculateMACD(prices, fast, slow, signal)
            }
        };
    }
    createSignal(type, symbol, amount, price, confidence, metadata) {
        return {
            type,
            symbol,
            amount,
            price,
            confidence,
            metadata
        };
    }
    // 简化的技术指标实现
    calculateSMA(prices, period) {
        const result = [];
        for (let i = period - 1; i < prices.length; i++) {
            const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
            result.push(sum / period);
        }
        return result;
    }
    calculateEMA(prices, period) {
        const result = [];
        const multiplier = 2 / (period + 1);
        // 第一个值使用SMA
        let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period;
        result.push(ema);
        // 后续值使用EMA公式
        for (let i = period; i < prices.length; i++) {
            ema = (prices[i] - ema) * multiplier + ema;
            result.push(ema);
        }
        return result;
    }
    calculateRSI(prices, period) {
        const result = [];
        const gains = [];
        const losses = [];
        // 计算价格变化
        for (let i = 1; i < prices.length; i++) {
            const change = prices[i] - prices[i - 1];
            gains.push(change > 0 ? change : 0);
            losses.push(change < 0 ? -change : 0);
        }
        // 计算RSI
        for (let i = period - 1; i < gains.length; i++) {
            const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
            const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period;
            if (avgLoss === 0) {
                result.push(100);
            }
            else {
                const rs = avgGain / avgLoss;
                const rsi = 100 - (100 / (1 + rs));
                result.push(rsi);
            }
        }
        return result;
    }
    calculateMACD(prices, fast, slow, signal) {
        const emaFast = this.calculateEMA(prices, fast);
        const emaSlow = this.calculateEMA(prices, slow);
        // 计算MACD线
        const macd = [];
        const startIndex = slow - fast;
        for (let i = startIndex; i < emaFast.length; i++) {
            macd.push(emaFast[i] - emaSlow[i - startIndex]);
        }
        // 计算信号线
        const signalLine = this.calculateEMA(macd, signal);
        // 计算柱状图
        const histogram = [];
        const signalStartIndex = signal - 1;
        for (let i = signalStartIndex; i < macd.length; i++) {
            histogram.push(macd[i] - signalLine[i - signalStartIndex]);
        }
        return {
            macd,
            signal: signalLine,
            histogram
        };
    }
}
// 策略运行时管理器
export class StrategyRuntimeManager {
    constructor() {
        this.runtimes = new Map();
    }
    registerRuntime(runtime) {
        this.runtimes.set(runtime.language, runtime);
    }
    getRuntime(language) {
        return this.runtimes.get(language);
    }
    async executeStrategy(language, code, context) {
        const runtime = this.getRuntime(language);
        if (!runtime) {
            throw new Error(`Runtime for language ${language} not found`);
        }
        try {
            return await runtime.execute(code, context);
        }
        catch (error) {
            return {
                signals: [],
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    async validateStrategy(language, code) {
        const runtime = this.getRuntime(language);
        if (!runtime) {
            return {
                valid: false,
                errors: [`Runtime for language ${language} not found`],
                warnings: []
            };
        }
        return await runtime.validate(code);
    }
    async dispose() {
        const promises = Array.from(this.runtimes.values()).map(runtime => runtime.dispose());
        await Promise.all(promises);
        this.runtimes.clear();
    }
}
//# sourceMappingURL=StrategyRuntime.js.map
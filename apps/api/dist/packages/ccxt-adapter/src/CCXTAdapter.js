import * as ccxt from 'ccxt';
import { ExchangeError, NetworkError, AuthenticationError, InsufficientFunds, InvalidOrder, RateLimitExceeded } from '../../core/dist/index.js';
export class CCXTAdapter {
    constructor() {
        this.exchange = null;
        this._id = '';
        this._name = '';
        this._type = 'cex';
        this.subscriptions = new Map();
    }
    get id() {
        return this._id;
    }
    get name() {
        return this._name;
    }
    get type() {
        return this._type;
    }
    async initialize(config) {
        try {
            this._id = config.id;
            this._name = config.name;
            this._type = config.type;
            // 检查CCXT是否支持该交易所
            if (!(config.id in ccxt)) {
                throw new ExchangeError(`Exchange ${config.id} not supported by CCXT`, config.id);
            }
            // 创建交易所实例
            const ExchangeClass = ccxt[config.id];
            this.exchange = new ExchangeClass({
                apiKey: config.apiKey,
                secret: config.secret,
                password: config.passphrase,
                sandbox: config.sandbox,
                rateLimit: config.rateLimit,
                enableRateLimit: true,
                ...config.options
            });
            // 加载市场信息
            await this.exchange.loadMarkets();
        }
        catch (error) {
            throw this.handleError(error, config.id);
        }
    }
    isInitialized() {
        return this.exchange !== null && this.exchange.markets !== undefined;
    }
    async fetchMarkets() {
        this.ensureInitialized();
        try {
            const markets = await this.exchange.fetchMarkets();
            return markets.map((market) => market.symbol);
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async fetchTicker(symbol) {
        this.ensureInitialized();
        try {
            const ticker = await this.exchange.fetchTicker(symbol);
            return {
                symbol,
                timestamp: ticker.timestamp || Date.now(),
                bid: ticker.bid || 0,
                ask: ticker.ask || 0,
                last: ticker.last || 0,
                volume: ticker.baseVolume || 0,
                high: ticker.high || 0,
                low: ticker.low || 0,
                open: ticker.open || 0,
                close: ticker.close || ticker.last || 0
            };
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async fetchOrderBook(symbol, limit) {
        this.ensureInitialized();
        try {
            const orderbook = await this.exchange.fetchOrderBook(symbol, limit);
            return {
                symbol,
                timestamp: orderbook.timestamp || Date.now(),
                bids: orderbook.bids.map((entry) => ({ price: Number(entry[0]), amount: Number(entry[1]) })),
                asks: orderbook.asks.map((entry) => ({ price: Number(entry[0]), amount: Number(entry[1]) }))
            };
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async fetchTrades(symbol, limit) {
        this.ensureInitialized();
        try {
            return await this.exchange.fetchTrades(symbol, undefined, limit);
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async fetchOHLCV(symbol, timeframe, since, limit) {
        this.ensureInitialized();
        try {
            return await this.exchange.fetchOHLCV(symbol, timeframe, since, limit);
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async fetchBalance() {
        this.ensureInitialized();
        try {
            const balance = await this.exchange.fetchBalance();
            const result = {};
            for (const [currency, info] of Object.entries(balance)) {
                if (currency !== 'info' && currency !== 'free' && currency !== 'used' && currency !== 'total') {
                    const balanceInfo = info;
                    result[currency] = {
                        currency,
                        free: balanceInfo.free || 0,
                        used: balanceInfo.used || 0,
                        total: balanceInfo.total || 0
                    };
                }
            }
            return result;
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async fetchOpenOrders(symbol) {
        this.ensureInitialized();
        try {
            const orders = await this.exchange.fetchOpenOrders(symbol);
            return orders.map((order) => this.transformOrder(order));
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async fetchClosedOrders(symbol, since, limit) {
        this.ensureInitialized();
        try {
            const orders = await this.exchange.fetchClosedOrders(symbol, since, limit);
            return orders.map((order) => this.transformOrder(order));
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async createOrder(order) {
        this.ensureInitialized();
        try {
            const result = await this.exchange.createOrder(order.symbol, order.type, order.side, order.amount, order.price, {
                stopPrice: order.stopPrice,
                timeInForce: order.timeInForce,
                clientOrderId: order.clientOrderId
            });
            return this.transformOrder(result);
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async cancelOrder(id, symbol) {
        this.ensureInitialized();
        try {
            const result = await this.exchange.cancelOrder(id, symbol);
            return this.transformOrder(result);
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    async cancelAllOrders(symbol) {
        this.ensureInitialized();
        try {
            const orders = await this.exchange.cancelAllOrders(symbol);
            return Array.isArray(orders) ? orders.map((order) => this.transformOrder(order)) : [];
        }
        catch (error) {
            throw this.handleError(error, this.id);
        }
    }
    // WebSocket订阅方法
    async subscribeToTicker(symbol, callback) {
        // 实现WebSocket ticker订阅
        // 这里需要根据具体交易所的WebSocket API实现
    }
    async subscribeToOrderBook(symbol, callback) {
        // 实现WebSocket orderbook订阅
    }
    async subscribeToTrades(symbol, callback) {
        // 实现WebSocket trades订阅
    }
    async unsubscribe(symbol, type) {
        // 实现取消订阅
    }
    ensureInitialized() {
        if (!this.isInitialized()) {
            throw new ExchangeError('Exchange not initialized', this.id);
        }
    }
    transformOrder(order) {
        return {
            id: order.id,
            clientOrderId: order.clientOrderId,
            symbol: order.symbol,
            side: order.side,
            type: order.type,
            amount: order.amount,
            price: order.price,
            filled: order.filled || 0,
            remaining: order.remaining || order.amount,
            status: order.status,
            timestamp: order.timestamp || Date.now(),
            fee: order.fee ? {
                currency: order.fee.currency,
                cost: order.fee.cost
            } : undefined
        };
    }
    handleError(error, exchangeId) {
        if (error instanceof ccxt.NetworkError) {
            return new NetworkError(error.message, exchangeId, error);
        }
        else if (error instanceof ccxt.AuthenticationError) {
            return new AuthenticationError(error.message, exchangeId, error);
        }
        else if (error instanceof ccxt.InsufficientFunds) {
            return new InsufficientFunds(error.message, exchangeId, error);
        }
        else if (error instanceof ccxt.InvalidOrder) {
            return new InvalidOrder(error.message, exchangeId, error);
        }
        else if (error instanceof ccxt.RateLimitExceeded) {
            return new RateLimitExceeded(error.message, exchangeId, error);
        }
        else if (error instanceof ccxt.BaseError) {
            return new ExchangeError(error.message, exchangeId, error.constructor.name, error);
        }
        else {
            return new ExchangeError(error.message || 'Unknown error', exchangeId, 'UNKNOWN_ERROR', error);
        }
    }
}
//# sourceMappingURL=CCXTAdapter.js.map
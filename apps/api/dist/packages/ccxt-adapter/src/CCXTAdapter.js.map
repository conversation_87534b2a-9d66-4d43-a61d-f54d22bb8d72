{"version": 3, "file": "CCXTAdapter.js", "sourceRoot": "", "sources": ["../../../../../../packages/ccxt-adapter/src/CCXTAdapter.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,MAAM,CAAA;AAC5B,OAAO,EAQL,aAAa,EACb,YAAY,EACZ,mBAAmB,EACnB,iBAAiB,EACjB,YAAY,EACZ,iBAAiB,EAClB,MAAM,0BAA0B,CAAA;AAEjC,MAAM,OAAO,WAAW;IAAxB;QACU,aAAQ,GAAyB,IAAI,CAAA;QACrC,QAAG,GAAW,EAAE,CAAA;QAChB,UAAK,GAAW,EAAE,CAAA;QAClB,UAAK,GAAkB,KAAK,CAAA;QAC5B,kBAAa,GAAqB,IAAI,GAAG,EAAE,CAAA;IAkQrD,CAAC;IAhQC,IAAI,EAAE;QACJ,OAAO,IAAI,CAAC,GAAG,CAAA;IACjB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAsB;QACrC,IAAI,CAAC;YACH,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,EAAE,CAAA;YACpB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAA;YACxB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAA;YAExB,iBAAiB;YACjB,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,aAAa,CAAC,YAAY,MAAM,CAAC,EAAE,wBAAwB,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;YACnF,CAAC;YAED,UAAU;YACV,MAAM,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,EAAuB,CAAQ,CAAA;YACjE,IAAI,CAAC,QAAQ,GAAG,IAAI,aAAa,CAAC;gBAChC,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,QAAQ,EAAE,MAAM,CAAC,UAAU;gBAC3B,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,eAAe,EAAE,IAAI;gBACrB,GAAG,MAAM,CAAC,OAAO;aAClB,CAAC,CAAA;YAEF,SAAS;YACT,MAAM,IAAI,CAAC,QAAS,CAAC,WAAW,EAAE,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;QAC1C,CAAC;IACH,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,SAAS,CAAA;IACtE,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,YAAY,EAAE,CAAA;YACnD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAc;QAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;YACvD,OAAO;gBACL,MAAM;gBACN,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;gBACzC,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;gBACpB,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;gBACpB,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC;gBACtB,MAAM,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC;gBAC9B,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC;gBACtB,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;gBACpB,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC;gBACtB,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,IAAI,CAAC;aACxC,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,KAAc;QACjD,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YACpE,OAAO;gBACL,MAAM;gBACN,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;gBAC5C,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACjG,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;aAClG,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,KAAc;QAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,QAAS,CAAC,WAAW,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAc,EAAE,SAAiB,EAAE,KAAc,EAAE,KAAc;QAChF,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,QAAS,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,YAAY,EAAE,CAAA;YACnD,MAAM,MAAM,GAA4B,EAAE,CAAA;YAE1C,KAAK,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvD,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;oBAC9F,MAAM,WAAW,GAAG,IAAW,CAAA;oBAC/B,MAAM,CAAC,QAAQ,CAAC,GAAG;wBACjB,QAAQ;wBACR,IAAI,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC;wBAC3B,IAAI,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC;wBAC3B,KAAK,EAAE,WAAW,CAAC,KAAK,IAAI,CAAC;qBAC9B,CAAA;gBACH,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAA;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAe;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;YAC3D,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAA;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAe,EAAE,KAAc,EAAE,KAAc;QACrE,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;YAC3E,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAA;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAmB;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,WAAW,CAC7C,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,MAAM,EACZ,KAAK,CAAC,KAAK,EACX;gBACE,SAAS,EAAE,KAAK,CAAC,SAAS;gBAC1B,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,aAAa,EAAE,KAAK,CAAC,aAAa;aACnC,CACF,CAAA;YACD,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAU,EAAE,MAAe;QAC3C,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;YAC3D,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAe;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACxB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;YAC3D,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAC5F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAED,gBAAgB;IAChB,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,QAAoC;QAC1E,uBAAuB;QACvB,8BAA8B;IAChC,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAc,EAAE,QAAmC;QAC5E,0BAA0B;IAC5B,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,MAAc,EAAE,QAA6B;QACnE,uBAAuB;IACzB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAc,EAAE,IAAuC;QACvE,SAAS;IACX,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YAC1B,MAAM,IAAI,aAAa,CAAC,0BAA0B,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;QAC9D,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,KAAU;QAC/B,OAAO;YACL,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,aAAa,EAAE,KAAK,CAAC,aAAa;YAClC,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;YACzB,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM;YAC1C,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC,GAAG,EAAE;YACxC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBACf,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ;gBAC5B,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI;aACrB,CAAC,CAAC,CAAC,SAAS;SACd,CAAA;IACH,CAAC;IAEO,WAAW,CAAC,KAAU,EAAE,UAAkB;QAChD,IAAI,KAAK,YAAY,IAAI,CAAC,YAAY,EAAE,CAAC;YACvC,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;aAAM,IAAI,KAAK,YAAY,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACrD,OAAO,IAAI,mBAAmB,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;QAClE,CAAC;aAAM,IAAI,KAAK,YAAY,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACnD,OAAO,IAAI,iBAAiB,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;QAChE,CAAC;aAAM,IAAI,KAAK,YAAY,IAAI,CAAC,YAAY,EAAE,CAAC;YAC9C,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;QAC3D,CAAC;aAAM,IAAI,KAAK,YAAY,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACnD,OAAO,IAAI,iBAAiB,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;QAChE,CAAC;aAAM,IAAI,KAAK,YAAY,IAAI,CAAC,SAAS,EAAE,CAAC;YAC3C,OAAO,IAAI,aAAa,CAAC,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QACpF,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,aAAa,CAAC,KAAK,CAAC,OAAO,IAAI,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,KAAK,CAAC,CAAA;QAChG,CAAC;IACH,CAAC;CACF"}
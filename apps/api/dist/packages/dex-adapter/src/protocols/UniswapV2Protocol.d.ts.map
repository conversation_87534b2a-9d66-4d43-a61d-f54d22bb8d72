{"version": 3, "file": "UniswapV2Protocol.d.ts", "sourceRoot": "", "sources": ["../../../../../../../packages/dex-adapter/src/protocols/UniswapV2Protocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAA;AAC/B,OAAO,EACL,WAAW,EAEX,QAAQ,EACR,SAAS,EACT,cAAc,EACd,WAAW,EACX,eAAe,EACf,WAAW,EAGZ,MAAM,cAAc,CAAA;AAErB,qBAAa,iBAAkB,YAAW,WAAW;IACnD,QAAQ,CAAC,IAAI,gBAAe;IAC5B,QAAQ,CAAC,OAAO,WAAU;IAC1B,QAAQ,CAAC,aAAa,EAAE,MAAM,CAAA;IAC9B,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAA;IAC/B,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAA;IAExB,OAAO,CAAC,QAAQ,CAAiB;IACjC,OAAO,CAAC,cAAc,CAA+B;IACrD,OAAO,CAAC,eAAe,CAA+B;IAGtD,OAAO,CAAC,QAAQ,CAAC,UAAU,CAO1B;IAED,OAAO,CAAC,QAAQ,CAAC,WAAW,CAI3B;IAED,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAKxB;gBAEW,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ;IAY/D,OAAO,CAAC,oBAAoB;IA+B5B,OAAO,CAAC,mBAAmB;IAKrB,aAAa,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAcxE,YAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAUlF,WAAW,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IAUlF,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC;IAgDrF,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;IAgBpE,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC;IAgC/E,WAAW,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;IAalE,oBAAoB,CAAC,MAAM,EAAE,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC;IAyB1D,eAAe,CAAC,MAAM,EAAE,cAAc,GAAG,OAAO,CAAC,WAAW,CAAC;IAsB7D,4BAA4B,CAAC,MAAM,EAAE,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC;IAwBnE,+BAA+B,CAAC,MAAM,EAAE,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC;YAwB9D,YAAY;IAW1B,OAAO,CAAC,cAAc;IAWtB,OAAO,CAAC,cAAc;YAWR,oBAAoB;CAInC"}
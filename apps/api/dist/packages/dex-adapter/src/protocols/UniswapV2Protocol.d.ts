import { ethers } from 'ethers';
import { ChainConfig, PoolInfo, TradePath, DEXTradeParams, GasEstimate, LiquidityParams, DEXProtocol } from '../types/dex';
export declare class UniswapV2Protocol implements DEXProtocol {
    readonly name = "Uniswap V2";
    readonly version = "2.0.0";
    readonly routerAddress: string;
    readonly factoryAddress: string;
    readonly chainId: number;
    private provider;
    private routerContract;
    private factoryContract;
    private readonly ROUTER_ABI;
    private readonly FACTORY_ABI;
    private readonly PAIR_ABI;
    constructor(chainConfig: ChainConfig, provider: ethers.Provider);
    private getContractAddresses;
    private initializeContracts;
    getTokenPrice(tokenAddress: string, baseToken?: string): Promise<number>;
    getAmountOut(amountIn: string, tokenIn: string, tokenOut: string): Promise<string>;
    getAmountIn(amountOut: string, tokenIn: string, tokenOut: string): Promise<string>;
    findBestPath(tokenIn: string, tokenOut: string, amountIn: string): Promise<TradePath>;
    getAllPaths(tokenIn: string, tokenOut: string): Promise<TradePath[]>;
    getPool(token0: string, token1: string, fee?: number): Promise<PoolInfo | null>;
    getAllPools(token0?: string, token1?: string): Promise<PoolInfo[]>;
    buildSwapTransaction(params: DEXTradeParams): Promise<any>;
    estimateSwapGas(params: DEXTradeParams): Promise<GasEstimate>;
    buildAddLiquidityTransaction(params: LiquidityParams): Promise<any>;
    buildRemoveLiquidityTransaction(params: LiquidityParams): Promise<any>;
    private getTokenInfo;
    private getUSDCAddress;
    private getWETHAddress;
    private calculatePriceImpact;
}
//# sourceMappingURL=UniswapV2Protocol.d.ts.map
import { TokenInfo } from '../types/dex';
export declare const COMMON_TOKENS: Record<number, Record<string, TokenInfo>>;
export declare function getTokenBySymbol(symbol: string, chainId: number): TokenInfo | null;
export declare function getTokenByAddress(address: string, chainId: number): TokenInfo | null;
export declare function isNativeToken(address: string): boolean;
export declare function getWrappedToken(chainId: number): TokenInfo | null;
export declare function formatTokenAmount(amount: string, decimals: number): string;
export declare function parseTokenAmount(amount: string, decimals: number): string;
//# sourceMappingURL=tokenUtils.d.ts.map
import { UnifiedExchange, ExchangeConfig, UnifiedOrder, OrderResult, OrderBook, Balance, MarketData } from '../../core/dist/index.js';
import { PoolInfo } from './types/dex';
export declare class DEXAdapter implements UnifiedExchange {
    private provider;
    private wallet;
    private protocols;
    private chainConfig;
    private _id;
    private _name;
    private _type;
    private subscriptions;
    private eventListeners;
    get id(): string;
    get name(): string;
    get type(): 'cex' | 'dex';
    initialize(config: ExchangeConfig): Promise<void>;
    isInitialized(): boolean;
    fetchMarkets(): Promise<string[]>;
    fetchTicker(symbol: string): Promise<MarketData>;
    fetchOrderBook(symbol: string, limit?: number): Promise<OrderBook>;
    fetchTrades(symbol: string, limit?: number): Promise<any[]>;
    fetchOHLCV(symbol: string, timeframe: string, since?: number, limit?: number): Promise<any[]>;
    fetchBalance(): Promise<Record<string, Balance>>;
    fetchOpenOrders(symbol?: string): Promise<OrderResult[]>;
    fetchClosedOrders(symbol?: string, since?: number, limit?: number): Promise<OrderResult[]>;
    createOrder(order: UnifiedOrder): Promise<OrderResult>;
    cancelOrder(id: string, symbol?: string): Promise<OrderResult>;
    cancelAllOrders(symbol?: string): Promise<OrderResult[]>;
    estimateGas(transaction: any): Promise<bigint>;
    getTokenPrice(tokenAddress: string, baseToken?: string): Promise<number>;
    getLiquidityPools(tokenA: string, tokenB: string): Promise<PoolInfo[]>;
    subscribeToTicker(symbol: string, callback: (data: MarketData) => void): Promise<void>;
    subscribeToOrderBook(symbol: string, callback: (data: OrderBook) => void): Promise<void>;
    subscribeToTrades(symbol: string, callback: (data: any) => void): Promise<void>;
    unsubscribe(symbol: string, type: 'ticker' | 'orderbook' | 'trades'): Promise<void>;
    private initializeProtocols;
    private ensureInitialized;
    private getTokenInfo;
    private getTokenPriceFromMultipleProtocols;
    private getAllPoolsForPair;
    private findBestTradePath;
    private executeSwap;
    private getTokenBalance;
    private getUSDCAddress;
    private getTokenAddress;
    private subscribeToPoolSwapEvents;
    private subscribeToPoolEvents;
    private unsubscribeFromPoolEvents;
    private calculatePriceFromSwap;
    private simulateOrderBookFromPools;
    private getPoolPrice;
    private estimateSwapAmount;
    private handleError;
}
//# sourceMappingURL=DEXAdapter.d.ts.map
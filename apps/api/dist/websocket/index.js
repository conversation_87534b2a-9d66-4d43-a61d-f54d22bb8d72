import { config } from '../config';
export async function setupWebSocket(fastify) {
    const clients = new Map();
    let clientIdCounter = 0;
    // WebSocket路由
    fastify.register(async function (fastify) {
        fastify.get('/ws', { websocket: true }, (connection, request) => {
            const clientId = `client_${++clientIdCounter}_${Date.now()}`;
            const client = {
                id: clientId,
                socket: connection.socket,
                subscriptions: new Set(),
                lastPing: Date.now()
            };
            clients.set(clientId, client);
            console.log(`WebSocket client connected: ${clientId}`);
            // 发送欢迎消息
            connection.socket.send(JSON.stringify({
                type: 'welcome',
                clientId,
                timestamp: Date.now()
            }));
            // 处理消息
            connection.socket.on('message', async (message) => {
                try {
                    const data = JSON.parse(message.toString());
                    await handleWebSocketMessage(client, data, fastify);
                }
                catch (error) {
                    console.error('WebSocket message error:', error);
                    connection.socket.send(JSON.stringify({
                        type: 'error',
                        message: 'Invalid message format',
                        timestamp: Date.now()
                    }));
                }
            });
            // 处理连接关闭
            connection.socket.on('close', () => {
                console.log(`WebSocket client disconnected: ${clientId}`);
                clients.delete(clientId);
            });
            // 处理错误
            connection.socket.on('error', (error) => {
                console.error(`WebSocket error for client ${clientId}:`, error);
                clients.delete(clientId);
            });
        });
    });
    // 心跳检查
    const heartbeatInterval = setInterval(() => {
        const now = Date.now();
        for (const [clientId, client] of clients) {
            // 检查客户端是否超时
            if (now - client.lastPing > config.WS_HEARTBEAT_INTERVAL * 2) {
                console.log(`WebSocket client timeout: ${clientId}`);
                client.socket.close();
                clients.delete(clientId);
                continue;
            }
            // 发送心跳
            try {
                client.socket.send(JSON.stringify({
                    type: 'ping',
                    timestamp: now
                }));
            }
            catch (error) {
                console.error(`Failed to send ping to client ${clientId}:`, error);
                clients.delete(clientId);
            }
        }
    }, config.WS_HEARTBEAT_INTERVAL);
    // 订阅Redis消息
    await setupRedisSubscriptions(fastify, clients);
    // 清理函数
    fastify.addHook('onClose', async () => {
        clearInterval(heartbeatInterval);
        // 关闭所有WebSocket连接
        for (const client of clients.values()) {
            try {
                client.socket.close();
            }
            catch (error) {
                console.error('Error closing WebSocket:', error);
            }
        }
        clients.clear();
    });
    console.log('WebSocket server setup completed');
}
// 处理WebSocket消息
async function handleWebSocketMessage(client, data, fastify) {
    const { type, payload } = data;
    switch (type) {
        case 'pong':
            client.lastPing = Date.now();
            break;
        case 'auth':
            await handleAuth(client, payload, fastify);
            break;
        case 'subscribe':
            await handleSubscribe(client, payload, fastify);
            break;
        case 'unsubscribe':
            await handleUnsubscribe(client, payload);
            break;
        case 'get_market_data':
            await handleGetMarketData(client, payload, fastify);
            break;
        case 'get_strategy_status':
            await handleGetStrategyStatus(client, payload, fastify);
            break;
        default:
            client.socket.send(JSON.stringify({
                type: 'error',
                message: `Unknown message type: ${type}`,
                timestamp: Date.now()
            }));
    }
}
// 处理认证
async function handleAuth(client, payload, fastify) {
    try {
        const { token } = payload;
        if (!token) {
            client.socket.send(JSON.stringify({
                type: 'auth_error',
                message: 'Token required',
                timestamp: Date.now()
            }));
            return;
        }
        // 验证JWT token
        const decoded = fastify.jwt.verify(token);
        client.userId = decoded.userId;
        client.socket.send(JSON.stringify({
            type: 'auth_success',
            userId: client.userId,
            timestamp: Date.now()
        }));
        console.log(`Client ${client.id} authenticated as user ${client.userId}`);
    }
    catch (error) {
        client.socket.send(JSON.stringify({
            type: 'auth_error',
            message: 'Invalid token',
            timestamp: Date.now()
        }));
    }
}
// 处理订阅
async function handleSubscribe(client, payload, fastify) {
    const { channel, symbol } = payload;
    if (!channel) {
        client.socket.send(JSON.stringify({
            type: 'error',
            message: 'Channel required',
            timestamp: Date.now()
        }));
        return;
    }
    const subscriptionKey = symbol ? `${channel}:${symbol}` : channel;
    client.subscriptions.add(subscriptionKey);
    // 如果是价格订阅，确保市场数据服务订阅了该交易对
    if (channel === 'price' && symbol) {
        await fastify.marketDataService.subscribeToSymbol(symbol);
    }
    client.socket.send(JSON.stringify({
        type: 'subscribed',
        channel,
        symbol,
        timestamp: Date.now()
    }));
    console.log(`Client ${client.id} subscribed to ${subscriptionKey}`);
}
// 处理取消订阅
async function handleUnsubscribe(client, payload) {
    const { channel, symbol } = payload;
    const subscriptionKey = symbol ? `${channel}:${symbol}` : channel;
    client.subscriptions.delete(subscriptionKey);
    client.socket.send(JSON.stringify({
        type: 'unsubscribed',
        channel,
        symbol,
        timestamp: Date.now()
    }));
    console.log(`Client ${client.id} unsubscribed from ${subscriptionKey}`);
}
// 处理获取市场数据
async function handleGetMarketData(client, payload, fastify) {
    try {
        const { symbol } = payload;
        const data = await fastify.marketDataService.getMarketData(symbol);
        client.socket.send(JSON.stringify({
            type: 'market_data',
            symbol,
            data,
            timestamp: Date.now()
        }));
    }
    catch (error) {
        client.socket.send(JSON.stringify({
            type: 'error',
            message: `Failed to get market data: ${error}`,
            timestamp: Date.now()
        }));
    }
}
// 处理获取策略状态
async function handleGetStrategyStatus(client, payload, fastify) {
    try {
        const { strategyId } = payload;
        const execution = fastify.strategyService.getStrategyExecution(strategyId);
        const stats = await fastify.strategyService.getStrategyStats(strategyId);
        client.socket.send(JSON.stringify({
            type: 'strategy_status',
            strategyId,
            execution,
            stats,
            timestamp: Date.now()
        }));
    }
    catch (error) {
        client.socket.send(JSON.stringify({
            type: 'error',
            message: `Failed to get strategy status: ${error}`,
            timestamp: Date.now()
        }));
    }
}
// 设置Redis订阅
async function setupRedisSubscriptions(fastify, clients) {
    // 订阅价格更新
    await fastify.redis.subscribe('price_updates', (message) => {
        try {
            const data = JSON.parse(message);
            broadcastToSubscribers(clients, `price:${data.symbol}`, {
                type: 'price_update',
                symbol: data.symbol,
                data: data.data,
                timestamp: data.timestamp
            });
        }
        catch (error) {
            console.error('Error processing price update:', error);
        }
    });
    // 订阅订单簿更新
    await fastify.redis.subscribe('orderbook_updates', (message) => {
        try {
            const data = JSON.parse(message);
            broadcastToSubscribers(clients, `orderbook:${data.symbol}`, {
                type: 'orderbook_update',
                symbol: data.symbol,
                data: data.data,
                timestamp: data.timestamp
            });
        }
        catch (error) {
            console.error('Error processing orderbook update:', error);
        }
    });
    // 订阅通知
    await fastify.redis.subscribe('notifications', (message) => {
        try {
            const notification = JSON.parse(message);
            broadcastToUser(clients, notification.userId, {
                type: 'notification',
                ...notification
            });
        }
        catch (error) {
            console.error('Error processing notification:', error);
        }
    });
    console.log('Redis subscriptions setup completed');
}
// 广播给订阅了特定频道的客户端
function broadcastToSubscribers(clients, channel, message) {
    for (const client of clients.values()) {
        if (client.subscriptions.has(channel)) {
            try {
                client.socket.send(JSON.stringify(message));
            }
            catch (error) {
                console.error(`Failed to send message to client ${client.id}:`, error);
            }
        }
    }
}
// 广播给特定用户
function broadcastToUser(clients, userId, message) {
    for (const client of clients.values()) {
        if (client.userId === userId) {
            try {
                client.socket.send(JSON.stringify(message));
            }
            catch (error) {
                console.error(`Failed to send message to user ${userId}:`, error);
            }
        }
    }
}
//# sourceMappingURL=index.js.map
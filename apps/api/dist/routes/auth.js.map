{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/routes/auth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAA;AACvB,OAAO,MAAM,MAAM,UAAU,CAAA;AAE7B,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAA;AAElC,cAAc;AACd,MAAM,cAAc,GAAG,CAAC,CAAC,MAAM,CAAC;IAC9B,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;IACzB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IAC3B,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;CACjC,CAAC,CAAA;AAEF,MAAM,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC;IAC3B,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE;IACzB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CAC5B,CAAC,CAAA;AAEF,MAAM,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC;IAClC,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE;CACzB,CAAC,CAAA;AAEF,WAAW;AACX,KAAK,UAAU,YAAY,CAAC,OAAuB,EAAE,KAAmB;IACtE,IAAI,CAAC;QACH,MAAM,OAAO,CAAC,SAAS,EAAE,CAAA;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACrB,KAAK,EAAE,cAAc;YACrB,OAAO,EAAE,0BAA0B;SACpC,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,UAAU,CAAC,OAAwB;IACvD,OAAO;IACP,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;QACxB,MAAM,EAAE;YACN,WAAW,EAAE,qBAAqB;YAClC,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC;gBACvC,UAAU,EAAE;oBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;oBAC1C,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE;oBAC1C,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE;iBACvD;aACF;YACD,QAAQ,EAAE;gBACR,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,IAAI,EAAE;4BACJ,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACtB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACzB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACxB,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;6BAC9B;yBACF;wBACD,MAAM,EAAE;4BACN,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCAC/B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;6BACjC;yBACF;qBACF;iBACF;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAEpE,YAAY;YACZ,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAC3D,IAAI,YAAY,EAAE,CAAC;gBACjB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,UAAU;oBACjB,OAAO,EAAE,qCAAqC;iBAC/C,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,OAAO;YACP,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAA;YAEtD,OAAO;YACP,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC;gBACvC,KAAK;gBACL,QAAQ,EAAE,cAAc;gBACxB,IAAI;gBACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAA;YAEF,eAAe;YACf,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAClC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EACtC,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB,CAAA;YAED,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CACnC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EACpC,EAAE,SAAS,EAAE,MAAM,CAAC,cAAc,EAAE,CACrC,CAAA;YAED,wBAAwB;YACxB,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,YAAY,CAAC,CAAA;YAErF,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,SAAS,EAAE,IAAI,CAAC,SAAS;iBAC1B;gBACD,MAAM,EAAE;oBACN,WAAW;oBACX,YAAY;iBACb;aACF,CAAC,CAAA;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAChC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,sBAAsB;oBAC/B,OAAO,EAAE,KAAK,CAAC,MAAM;iBACtB,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,uBAAuB;oBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAClE,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,OAAO;IACP,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE;QACrB,MAAM,EAAE;YACN,WAAW,EAAE,YAAY;YACzB,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;gBAC/B,UAAU,EAAE;oBACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;oBAC1C,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC7B;aACF;YACD,QAAQ,EAAE;gBACR,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,IAAI,EAAE;4BACJ,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACtB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACzB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;6BACzB;yBACF;wBACD,MAAM,EAAE;4BACN,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCAC/B,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;6BACjC;yBACF;qBACF;iBACF;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAE3D,OAAO;YACP,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YACnD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,2BAA2B;iBACrC,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,OAAO;YACP,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;YACrE,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,2BAA2B;iBACrC,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,eAAe;YACf,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAClC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EACtC,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB,CAAA;YAED,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CACnC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EACpC,EAAE,SAAS,EAAE,MAAM,CAAC,cAAc,EAAE,CACrC,CAAA;YAED,wBAAwB;YACxB,MAAM,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,iBAAiB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,YAAY,CAAC,CAAA;YAErF,OAAO;gBACL,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB;gBACD,MAAM,EAAE;oBACN,WAAW;oBACX,YAAY;iBACb;aACF,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAChC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,sBAAsB;oBAC/B,OAAO,EAAE,KAAK,CAAC,MAAM;iBACtB,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,uBAAuB;oBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAClE,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,UAAU;IACV,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE;QACvB,MAAM,EAAE;YACN,WAAW,EAAE,sBAAsB;YACnC,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,CAAC,cAAc,CAAC;gBAC1B,UAAU,EAAE;oBACV,YAAY,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBACjC;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,MAAM,EAAE,YAAY,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAE/D,kBAAkB;YAClB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,CAAQ,CAAA;YACvD,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC/B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,uBAAuB;iBACjC,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,iBAAiB;YACjB,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAA;YAC9E,IAAI,WAAW,KAAK,YAAY,EAAE,CAAC;gBACjC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,oCAAoC;iBAC9C,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,SAAS;YACT,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YACzD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,gBAAgB;iBAC1B,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,mBAAmB;YACnB,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CACrC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EACtC,EAAE,SAAS,EAAE,KAAK,EAAE,CACrB,CAAA;YAED,OAAO;gBACL,WAAW,EAAE,cAAc;aAC5B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAChC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,sBAAsB;oBAC/B,OAAO,EAAE,KAAK,CAAC,MAAM;iBACtB,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,cAAc;oBACrB,OAAO,EAAE,kCAAkC;iBAC5C,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,OAAO;IACP,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE;QACtB,UAAU,EAAE,YAAY;QACxB,MAAM,EAAE;YACN,WAAW,EAAE,aAAa;YAC1B,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;SAC3B;KACF,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,OAAO,CAAC,IAAW,CAAA;YAEhC,yBAAyB;YACzB,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;YAEvD,OAAO,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAA;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,WAAW;IACX,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE;QACjB,UAAU,EAAE,YAAY;QACxB,MAAM,EAAE;YACN,WAAW,EAAE,8BAA8B;YAC3C,IAAI,EAAE,CAAC,MAAM,CAAC;YACd,QAAQ,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;YAC1B,QAAQ,EAAE;gBACR,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,IAAI,EAAE;4BACJ,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACtB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACzB,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;gCACxB,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;6BAC9B;yBACF;qBACF;iBACF;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,OAAO,CAAC,IAAW,CAAA;YACrC,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;YAE3D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE,gBAAgB;iBAC1B,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,OAAO;gBACL,IAAI,EAAE;oBACJ,EAAE,EAAE,IAAI,CAAC,EAAE;oBACX,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,SAAS,EAAE,IAAI,CAAC,SAAS;iBAC1B;aACF,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC"}
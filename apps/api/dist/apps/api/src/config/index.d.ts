export declare const config: {
    readonly NODE_ENV: "development" | "production" | "test";
    readonly PORT: number;
    readonly HOST: string;
    readonly DATABASE_URL: string;
    readonly REDIS_URL: string;
    readonly JWT_SECRET: string;
    readonly JWT_EXPIRES_IN: string;
    readonly CORS_ORIGINS: string[];
    readonly RATE_LIMIT_MAX: number;
    readonly RATE_LIMIT_WINDOW: string;
    readonly LOG_LEVEL: "fatal" | "error" | "warn" | "info" | "debug" | "trace";
    readonly ENCRYPTION_KEY: string;
    readonly COINGECKO_API_KEY: string | undefined;
    readonly COINMARKETCAP_API_KEY: string | undefined;
    readonly WS_HEARTBEAT_INTERVAL: number;
    readonly WS_HEARTBEAT_TIMEOUT: number;
    readonly WS_MAX_CONNECTIONS: number;
    readonly STRATEGY_TIMEOUT: number;
    readonly MAX_CONCURRENT_STRATEGIES: number;
    readonly MARKET_DATA_CACHE_TTL: number;
    readonly PRICE_UPDATE_INTERVAL: number;
    readonly SUPPORTED_CEX: readonly ["binance", "okx", "bybit", "coinbase", "kraken", "huobi", "kucoin"];
    readonly SUPPORTED_DEX: readonly ["uniswap-v2", "uniswap-v3", "sushiswap", "pancakeswap", "curve", "balancer"];
    readonly SUPPORTED_CHAINS: readonly [{
        readonly id: 1;
        readonly name: "Ethereum";
        readonly rpc: "https://eth.llamarpc.com";
    }, {
        readonly id: 56;
        readonly name: "BSC";
        readonly rpc: "https://bsc-dataseed.binance.org";
    }, {
        readonly id: 137;
        readonly name: "Polygon";
        readonly rpc: "https://polygon-rpc.com";
    }, {
        readonly id: 42161;
        readonly name: "Arbitrum";
        readonly rpc: "https://arb1.arbitrum.io/rpc";
    }, {
        readonly id: 10;
        readonly name: "Optimism";
        readonly rpc: "https://mainnet.optimism.io";
    }];
    readonly DEFAULT_STRATEGY_PARAMS: {
        readonly maxPositionSize: 1000;
        readonly maxDailyLoss: 100;
        readonly maxDrawdown: 0.1;
        readonly slippageTolerance: 0.005;
        readonly gasLimit: 200000;
        readonly timeout: 30000;
    };
};
export type Config = typeof config;
//# sourceMappingURL=index.d.ts.map
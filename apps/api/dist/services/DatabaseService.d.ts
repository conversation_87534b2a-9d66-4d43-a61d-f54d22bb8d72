export declare class DatabaseService {
    private connected;
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    isConnected(): boolean;
    createStrategy(strategy: any): Promise<any>;
    getStrategy(id: string): Promise<any>;
    updateStrategy(id: string, updates: any): Promise<any>;
    deleteStrategy(id: string): Promise<void>;
    getAllStrategies(): Promise<any[]>;
    createUser(user: any): Promise<any>;
    getUserByEmail(email: string): Promise<any>;
    getUserById(id: string): Promise<any>;
    createTradeRecord(trade: any): Promise<any>;
    getTradeRecords(filters: any): Promise<any[]>;
    createBacktestResult(result: any): Promise<any>;
    getBacktestResults(strategyId: string): Promise<any[]>;
    healthCheck(): Promise<{
        status: 'ok' | 'error';
        message?: string;
    }>;
}
//# sourceMappingURL=DatabaseService.d.ts.map
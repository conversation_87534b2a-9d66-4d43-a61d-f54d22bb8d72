{"version": 3, "file": "MarketDataService.js", "sourceRoot": "", "sources": ["../../src/services/MarketDataService.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAA;AAClC,OAAO,KAAK,MAAM,OAAO,CAAA;AAYzB,MAAM,OAAO,iBAAiB;IAK5B,YAAoB,KAAmB;QAAnB,UAAK,GAAL,KAAK,CAAc;QAJ/B,yBAAoB,GAAgC,IAAI,GAAG,EAAE,CAAA;QAC7D,sBAAiB,GAAgB,IAAI,GAAG,EAAE,CAAA;QAC1C,gBAAW,GAA4B,IAAI,GAAG,EAAE,CAAA;IAEd,CAAC;IAE3C,KAAK,CAAC,UAAU;QACd,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;QAElD,eAAe;QACf,MAAM,aAAa,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,CAAC,CAAA;QAElF,KAAK,MAAM,MAAM,IAAI,aAAa,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QACtC,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAA;IAC7D,CAAC;IAED,YAAY;IACZ,KAAK,CAAC,iBAAiB,CAAC,MAAc;QACpC,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACvC,OAAM;QACR,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAElC,WAAW;QACX,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACtC,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;QACrC,CAAC,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAA;QAEhC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QAE/C,WAAW;QACX,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;QAEnC,OAAO,CAAC,GAAG,CAAC,iBAAiB,MAAM,gBAAgB,CAAC,CAAA;IACtD,CAAC;IAED,UAAU;IACV,KAAK,CAAC,qBAAqB,CAAC,MAAc;QACxC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACxC,OAAM;QACR,CAAC;QAED,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAErC,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACtD,IAAI,QAAQ,EAAE,CAAC;YACb,aAAa,CAAC,QAAQ,CAAC,CAAA;YACvB,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAC1C,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,gBAAgB,CAAC,CAAA;IAC1D,CAAC;IAED,SAAS;IACT,KAAK,CAAC,aAAa,CAAC,MAAc;QAChC,SAAS;QACT,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;QAC3D,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAA;QACf,CAAC;QAED,kBAAkB;QAClB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;QACnC,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAA;IACrD,CAAC;IAED,UAAU;IACV,KAAK,CAAC,YAAY,CAAC,MAAc;QAC/B,SAAS;QACT,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAC1D,IAAI,MAAM,EAAE,CAAC;YACX,OAAO,MAAM,CAAA;QACf,CAAC;QAED,kBAAkB;QAClB,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;QAClC,OAAO,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;IACpD,CAAC;IAED,aAAa;IACb,KAAK,CAAC,qBAAqB,CAAC,OAAiB;QAC3C,MAAM,OAAO,GAA+B,EAAE,CAAA;QAE9C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YAC5C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAC7C,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,CAAC,MAAM,CAAC,GAAG,IAAI,CAAA;YACxB,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC3B,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,SAAS;IACT,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,SAAiB,EAAE,QAAgB,GAAG;QAC1E,IAAI,CAAC;YACH,uBAAuB;YACvB,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,6DAA6D,EAAE;gBAC9F,MAAM,EAAE;oBACN,WAAW,EAAE,KAAK;oBAClB,IAAI,EAAE,EAAE;oBACR,QAAQ,EAAE,OAAO;iBAClB;aACF,CAAC,CAAA;YAEF,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC5D,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;gBAClB,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;aACf,CAAC,CAAC,CAAA;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAA;YAClE,OAAO,EAAE,CAAA;QACX,CAAC;IACH,CAAC;IAED,SAAS;IACT,KAAK,CAAC,gBAAgB,CAAC,KAA2C;QAChE,MAAM,UAAU,GAAe;YAC7B,GAAG,KAAK;YACR,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE;YAC1B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QAED,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,CAAA;QAE/C,YAAY;QACZ,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAE1C,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC,CAAA;QACxF,OAAO,UAAU,CAAA;IACnB,CAAC;IAED,SAAS;IACT,KAAK,CAAC,gBAAgB,CAAC,OAAe;QACpC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAChC,OAAO,CAAC,GAAG,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAA;IAC/C,CAAC;IAED,YAAY;IACZ,kBAAkB,CAAC,MAAc;QAC/B,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC,CAAA;IACvF,CAAC;IAED,SAAS;IACD,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,YAAoB;QACjE,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CACzD,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,CACjD,CAAA;QAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,SAAS,GAAG,KAAK,CAAA;YAErB,IAAI,KAAK,CAAC,SAAS,KAAK,OAAO,IAAI,YAAY,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAC/D,SAAS,GAAG,IAAI,CAAA;YAClB,CAAC;iBAAM,IAAI,KAAK,CAAC,SAAS,KAAK,OAAO,IAAI,YAAY,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBACtE,SAAS,GAAG,IAAI,CAAA;YAClB,CAAC;YAED,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;YACnD,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS;IACD,KAAK,CAAC,iBAAiB,CAAC,KAAiB,EAAE,YAAoB;QACrE,OAAO,CAAC,GAAG,CAAC,0BAA0B,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,KAAK,cAAc,YAAY,EAAE,CAAC,CAAA;QAEpH,eAAe;QACf,KAAK,CAAC,MAAM,GAAG,KAAK,CAAA;QAEpB,cAAc;QACd,yBAAyB;QACzB,MAAM,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;IACvD,CAAC;IAED,SAAS;IACD,KAAK,CAAC,qBAAqB,CAAC,KAAiB,EAAE,YAAoB;QACzE,qCAAqC;QACrC,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,aAAa;YACnB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,OAAO,EAAE,GAAG,KAAK,CAAC,MAAM,aAAa,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,KAAK,oBAAoB,YAAY,EAAE;YACrG,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAA;QAED,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAA;IACzE,CAAC;IAED,SAAS;IACD,KAAK,CAAC,gBAAgB,CAAC,MAAc;QAC3C,IAAI,CAAC;YACH,oBAAoB;YACpB,cAAc;YACd,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAA;YAE5D,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO;gBACP,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,qBAAqB,CAAC,CAAA;gBAElF,SAAS;gBACT,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAA;gBAEpD,WAAW;gBACX,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC;oBACvD,MAAM;oBACN,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC,CAAA;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;IAED,UAAU;IACF,KAAK,CAAC,eAAe,CAAC,MAAc;QAC1C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAA;YAE1D,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,QAAQ;gBAE/D,YAAY;gBACZ,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,CAAC;oBAC3D,MAAM;oBACN,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC,CAAA;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAA;QACpE,CAAC;IACH,CAAC;IAED,mBAAmB;IACX,KAAK,CAAC,sBAAsB,CAAC,MAAc;QACjD,IAAI,CAAC;YACH,kBAAkB;YAClB,cAAc;YACd,MAAM,SAAS,GAAG,KAAK,CAAA,CAAC,OAAO;YAC/B,MAAM,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAA,CAAC,WAAW;YAC3D,MAAM,KAAK,GAAG,SAAS,GAAG,YAAY,CAAA;YAEtC,OAAO;gBACL,MAAM;gBACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,GAAG,EAAE,KAAK,GAAG,KAAK;gBAClB,GAAG,EAAE,KAAK,GAAG,KAAK;gBAClB,IAAI,EAAE,KAAK;gBACX,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO;gBAC/B,IAAI,EAAE,KAAK,GAAG,IAAI;gBAClB,GAAG,EAAE,KAAK,GAAG,IAAI;gBACjB,IAAI,EAAE,KAAK,GAAG,IAAI;gBAClB,KAAK,EAAE,KAAK;aACb,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAA;YAClE,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,oBAAoB;IACZ,KAAK,CAAC,qBAAqB,CAAC,MAAc;QAChD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,KAAK,CAAA;YACvB,MAAM,IAAI,GAAG,EAAE,CAAA;YACf,MAAM,IAAI,GAAG,EAAE,CAAA;YAEf,UAAU;YACV,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC;oBACR,KAAK,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;oBAC/B,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;iBAC3B,CAAC,CAAA;gBACF,IAAI,CAAC,IAAI,CAAC;oBACR,KAAK,EAAE,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;oBAC/B,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;iBAC3B,CAAC,CAAA;YACJ,CAAC;YAED,OAAO;gBACL,MAAM;gBACN,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,IAAI;gBACJ,IAAI;aACL,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAA;YACjE,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,WAAW;IACX,KAAK,CAAC,cAAc;QAKlB,OAAO;YACL,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;YAC9C,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM;YACxF,YAAY,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAA;IACH,CAAC;IAED,OAAO;IACP,KAAK,CAAC,OAAO;QACX,UAAU;QACV,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,EAAE,CAAC;YAC1D,aAAa,CAAC,QAAQ,CAAC,CAAA;QACzB,CAAC;QAED,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,CAAA;QACjC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;QAC9B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;QAExB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;IAC/C,CAAC;IAEO,eAAe;QACrB,OAAO,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAA;IACzE,CAAC;CACF"}
import { DatabaseService } from './services/DatabaseService';
import { RedisService } from './services/RedisService';
import { ExchangeService } from './services/ExchangeService';
import { StrategyService } from './services/StrategyService';
import { MarketDataService } from './services/MarketDataService';
declare module 'fastify' {
    interface FastifyInstance {
        db: DatabaseService;
        redis: RedisService;
        exchangeService: ExchangeService;
        strategyService: StrategyService;
        marketDataService: MarketDataService;
    }
}
//# sourceMappingURL=index.d.ts.map
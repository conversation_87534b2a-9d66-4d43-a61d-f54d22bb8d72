
> @sfquant/api@1.0.0 build /Users/<USER>/CursorSpace/SFQuant/apps/api
> tsc

[96msrc/services/ExchangeService.ts[0m:[93m11[0m:[93m29[0m - [91merror[0m[90m TS2307: [0mCannot find module '@sfquant/ccxt-adapter' or its corresponding type declarations.

[7m11[0m import { CCXTAdapter } from '@sfquant/ccxt-adapter'
[7m  [0m [91m                            ~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/services/StrategyService.ts[0m:[93m12[0m:[93m8[0m - [91merror[0m[90m TS2307: [0mCannot find module '@sfquant/strategy-runtime' or its corresponding type declarations.

[7m12[0m } from '@sfquant/strategy-runtime'
[7m  [0m [91m       ~~~~~~~~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/websocket/index.ts[0m:[93m24[0m:[93m9[0m - [91merror[0m[90m TS2740: [0mType 'WebSocket' is missing the following properties from type 'SocketStream': socket, allowHalfOpen, pipe, compose, and 51 more.

[7m24[0m         socket: connection.socket,
[7m  [0m [91m        ~~~~~~[0m

  [96msrc/websocket/index.ts[0m:[93m7[0m:[93m3[0m
    [7m7[0m   socket: SocketStream
    [7m [0m [96m  ~~~~~~[0m
    The expected type comes from property 'socket' which is declared here on type 'WebSocketClient'

[96msrc/websocket/index.ts[0m:[93m76[0m:[93m23[0m - [91merror[0m[90m TS2551: [0mProperty 'close' does not exist on type 'SocketStream'. Did you mean 'closed'?

[7m76[0m         client.socket.close()
[7m  [0m [91m                      ~~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m161[0m:[93m22[0m
    [7m161[0m             readonly closed: boolean;
    [7m   [0m [96m                     ~~~~~~[0m
    'closed' is declared here.

[96msrc/websocket/index.ts[0m:[93m83[0m:[93m23[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m83[0m         client.socket.send(JSON.stringify({
[7m  [0m [91m                      ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m104[0m:[93m23[0m - [91merror[0m[90m TS2551: [0mProperty 'close' does not exist on type 'SocketStream'. Did you mean 'closed'?

[7m104[0m         client.socket.close()
[7m   [0m [91m                      ~~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m161[0m:[93m22[0m
    [7m161[0m             readonly closed: boolean;
    [7m   [0m [96m                     ~~~~~~[0m
    'closed' is declared here.

[96msrc/websocket/index.ts[0m:[93m150[0m:[93m21[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m150[0m       client.socket.send(JSON.stringify({
[7m   [0m [91m                    ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m168[0m:[93m21[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m168[0m       client.socket.send(JSON.stringify({
[7m   [0m [91m                    ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m180[0m:[93m19[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m180[0m     client.socket.send(JSON.stringify({
[7m   [0m [91m                  ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m188[0m:[93m19[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m188[0m     client.socket.send(JSON.stringify({
[7m   [0m [91m                  ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m205[0m:[93m19[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m205[0m     client.socket.send(JSON.stringify({
[7m   [0m [91m                  ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m221[0m:[93m17[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m221[0m   client.socket.send(JSON.stringify({
[7m   [0m [91m                ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m238[0m:[93m17[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m238[0m   client.socket.send(JSON.stringify({
[7m   [0m [91m                ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m258[0m:[93m19[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m258[0m     client.socket.send(JSON.stringify({
[7m   [0m [91m                  ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m265[0m:[93m19[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m265[0m     client.socket.send(JSON.stringify({
[7m   [0m [91m                  ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m284[0m:[93m19[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m284[0m     client.socket.send(JSON.stringify({
[7m   [0m [91m                  ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m292[0m:[93m19[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m292[0m     client.socket.send(JSON.stringify({
[7m   [0m [91m                  ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m360[0m:[93m23[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m360[0m         client.socket.send(JSON.stringify(message))
[7m   [0m [91m                      ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.

[96msrc/websocket/index.ts[0m:[93m377[0m:[93m23[0m - [91merror[0m[90m TS2551: [0mProperty 'send' does not exist on type 'SocketStream'. Did you mean 'end'?

[7m377[0m         client.socket.send(JSON.stringify(message))
[7m   [0m [91m                      ~~~~[0m

  [96m../../node_modules/.pnpm/@types+node@20.17.50/node_modules/@types/node/stream.d.ts[0m:[93m892[0m:[93m13[0m
    [7m892[0m             end(cb?: () => void): this;
    [7m   [0m [96m            ~~~[0m
    'end' is declared here.


Found 19 errors in 3 files.

Errors  Files
     1  src/services/ExchangeService.ts[90m:11[0m
     1  src/services/StrategyService.ts[90m:12[0m
    17  src/websocket/index.ts[90m:24[0m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 2.[39m

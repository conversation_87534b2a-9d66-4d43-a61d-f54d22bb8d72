// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  email         String    @unique
  username      String?   @unique
  name          String?
  password      String
  avatar        String?
  role          UserRole  @default(USER)
  isActive      Boolean   @default(true)
  isVerified    Boolean   @default(false)
  lastLoginAt   DateTime?
  loginAttempts Int       @default(0)
  lockedUntil   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关联关系
  strategies    Strategy[]
  sessions      UserSession[]
  apiKeys       ApiKey[]
  notifications Notification[]
  settings      UserSettings?

  @@map("users")
}

model UserSession {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  userAgent String?
  ipAddress String?
  expiresAt DateTime
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

model ApiKey {
  id          String   @id @default(cuid())
  userId      String
  name        String
  key         String   @unique
  secret      String
  permissions Json     @default("[]")
  isActive    Boolean  @default(true)
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  createdAt   DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

model UserSettings {
  id                   String  @id @default(cuid())
  userId               String  @unique
  theme                String  @default("light")
  language             String  @default("zh-CN")
  timezone             String  @default("Asia/Shanghai")
  emailNotifications   Boolean @default(true)
  pushNotifications    Boolean @default(true)
  tradingNotifications Boolean @default(true)
  defaultCurrency      String  @default("USDT")
  riskLevel            String  @default("medium")
  autoTrade            Boolean @default(false)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_settings")
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  data      Json?
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model Strategy {
  id          String        @id @default(cuid())
  userId      String
  name        String
  description String?
  type        StrategyType
  status      StrategyStatus @default(STOPPED)
  config      Json          @default("{}")
  exchanges   String[]      @default([])
  symbols     String[]      @default([])
  parameters  Json          @default("{}")
  
  // 性能指标
  totalReturn     Float @default(0)
  dailyReturn     Float @default(0)
  maxDrawdown     Float @default(0)
  sharpeRatio     Float @default(0)
  winRate         Float @default(0)
  totalTrades     Int   @default(0)
  
  // 风险管理
  maxPosition     Float? 
  stopLoss        Float?
  takeProfit      Float?
  
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  trades Trade[]
  logs   StrategyLog[]

  @@map("strategies")
}

model Trade {
  id         String    @id @default(cuid())
  strategyId String
  symbol     String
  side       TradeSide
  amount     Float
  price      Float
  fee        Float     @default(0)
  profit     Float?
  exchangeId String?
  orderId    String?
  status     TradeStatus @default(PENDING)
  executedAt DateTime?
  createdAt  DateTime  @default(now())

  strategy Strategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@map("trades")
}

model StrategyLog {
  id         String    @id @default(cuid())
  strategyId String
  level      LogLevel
  message    String
  data       Json?
  createdAt  DateTime  @default(now())

  strategy Strategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@map("strategy_logs")
}

model Exchange {
  id          String      @id @default(cuid())
  name        String      @unique
  type        ExchangeType
  apiUrl      String?
  wsUrl       String?
  isActive    Boolean     @default(true)
  config      Json        @default("{}")
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@map("exchanges")
}

model MarketData {
  id          String   @id @default(cuid())
  symbol      String
  exchange    String
  timestamp   DateTime
  open        Float
  high        Float
  low         Float
  close       Float
  volume      Float
  bid         Float?
  ask         Float?
  createdAt   DateTime @default(now())

  @@unique([symbol, exchange, timestamp])
  @@map("market_data")
}

// 枚举类型
enum UserRole {
  ADMIN
  USER
  PREMIUM
  DEVELOPER
}

enum NotificationType {
  SYSTEM
  STRATEGY
  TRADE
  PRICE_ALERT
  SECURITY
}

enum StrategyType {
  ARBITRAGE
  TREND
  MARKET_MAKER
  HIGH_FREQUENCY
  AI
  GRID
  DCA
  CUSTOM
}

enum StrategyStatus {
  RUNNING
  STOPPED
  PAUSED
  ERROR
  BACKTESTING
}

enum TradeSide {
  BUY
  SELL
}

enum TradeStatus {
  PENDING
  FILLED
  CANCELLED
  FAILED
}

enum LogLevel {
  DEBUG
  INFO
  WARN
  ERROR
}

enum ExchangeType {
  CEX
  DEX
}

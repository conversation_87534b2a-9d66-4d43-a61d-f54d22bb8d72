import { TechnicalIndicatorService, OHLCV } from './TechnicalIndicatorService'
import { MarketData } from '@sfquant/core'

export interface StrategyTemplate {
  id: string
  name: string
  description: string
  type: 'TREND' | 'ARBITRAGE' | 'GRID' | 'MARKET_MAKER' | 'AI' | 'SCALPING'
  parameters: Record<string, any>
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  expectedReturn: number
  maxDrawdown: number
  timeframe: string
  symbols: string[]
  code: string
}

export interface StrategySignal {
  action: 'BUY' | 'SELL' | 'HOLD'
  quantity: number
  price: number
  confidence: number
  reason: string
  timestamp: number
}

export interface BacktestResult {
  totalReturn: number
  sharpeRatio: number
  maxDrawdown: number
  winRate: number
  totalTrades: number
  profitableTrades: number
  avgWin: number
  avgLoss: number
  profitFactor: number
}

export class StrategyTemplateService {
  private technicalIndicatorService: TechnicalIndicatorService

  constructor() {
    this.technicalIndicatorService = new TechnicalIndicatorService()
  }

  // 获取所有策略模板
  getStrategyTemplates(): StrategyTemplate[] {
    return [
      {
        id: 'rsi_mean_reversion',
        name: 'RSI均值回归策略',
        description: '基于RSI指标的均值回归策略，在超买超卖区域进行反向交易',
        type: 'TREND',
        parameters: {
          rsiPeriod: 14,
          oversoldLevel: 30,
          overboughtLevel: 70,
          positionSize: 0.1,
          stopLoss: 0.02,
          takeProfit: 0.03
        },
        riskLevel: 'MEDIUM',
        expectedReturn: 15.5,
        maxDrawdown: 8.2,
        timeframe: '1h',
        symbols: ['BTC/USDT', 'ETH/USDT'],
        code: this.getRSIMeanReversionCode()
      },
      {
        id: 'macd_trend_following',
        name: 'MACD趋势跟踪策略',
        description: '基于MACD指标的趋势跟踪策略，在趋势确认后进行交易',
        type: 'TREND',
        parameters: {
          fastPeriod: 12,
          slowPeriod: 26,
          signalPeriod: 9,
          positionSize: 0.15,
          stopLoss: 0.03,
          takeProfit: 0.05
        },
        riskLevel: 'MEDIUM',
        expectedReturn: 22.3,
        maxDrawdown: 12.1,
        timeframe: '4h',
        symbols: ['BTC/USDT', 'ETH/USDT', 'BNB/USDT'],
        code: this.getMACDTrendFollowingCode()
      },
      {
        id: 'bollinger_bands_breakout',
        name: '布林带突破策略',
        description: '基于布林带的突破策略，在价格突破布林带时进行交易',
        type: 'TREND',
        parameters: {
          period: 20,
          stdDev: 2,
          positionSize: 0.12,
          stopLoss: 0.025,
          takeProfit: 0.04
        },
        riskLevel: 'HIGH',
        expectedReturn: 28.7,
        maxDrawdown: 15.3,
        timeframe: '1h',
        symbols: ['BTC/USDT', 'ETH/USDT'],
        code: this.getBollingerBreakoutCode()
      },
      {
        id: 'grid_trading',
        name: '网格交易策略',
        description: '在震荡市场中使用网格交易策略，通过多次小额交易获利',
        type: 'GRID',
        parameters: {
          gridCount: 10,
          gridSpacing: 0.005,
          baseAmount: 100,
          upperLimit: 1.1,
          lowerLimit: 0.9
        },
        riskLevel: 'LOW',
        expectedReturn: 12.8,
        maxDrawdown: 5.5,
        timeframe: '15m',
        symbols: ['BTC/USDT'],
        code: this.getGridTradingCode()
      },
      {
        id: 'scalping_strategy',
        name: '剥头皮策略',
        description: '高频短线交易策略，利用小幅价格波动快速获利',
        type: 'SCALPING',
        parameters: {
          minSpread: 0.001,
          maxHoldTime: 300, // 5分钟
          positionSize: 0.05,
          stopLoss: 0.005,
          takeProfit: 0.008
        },
        riskLevel: 'HIGH',
        expectedReturn: 35.2,
        maxDrawdown: 18.7,
        timeframe: '1m',
        symbols: ['BTC/USDT', 'ETH/USDT'],
        code: this.getScalpingCode()
      }
    ]
  }

  // 执行策略信号生成
  async executeStrategy(templateId: string, marketData: MarketData[], ohlcvData: OHLCV[]): Promise<StrategySignal | null> {
    const template = this.getStrategyTemplates().find(t => t.id === templateId)
    if (!template) {
      throw new Error(`Strategy template ${templateId} not found`)
    }

    switch (templateId) {
      case 'rsi_mean_reversion':
        return this.executeRSIMeanReversion(template, marketData, ohlcvData)
      case 'macd_trend_following':
        return this.executeMACDTrendFollowing(template, marketData, ohlcvData)
      case 'bollinger_bands_breakout':
        return this.executeBollingerBreakout(template, marketData, ohlcvData)
      case 'grid_trading':
        return this.executeGridTrading(template, marketData, ohlcvData)
      case 'scalping_strategy':
        return this.executeScalping(template, marketData, ohlcvData)
      default:
        return null
    }
  }

  // RSI均值回归策略实现
  private executeRSIMeanReversion(template: StrategyTemplate, marketData: MarketData[], ohlcvData: OHLCV[]): StrategySignal | null {
    if (ohlcvData.length < template.parameters.rsiPeriod + 1) {
      return null
    }

    const closes = ohlcvData.map(d => d.close)
    const rsi = this.technicalIndicatorService.calculateRSI(closes, template.parameters.rsiPeriod)
    const currentRSI = rsi[rsi.length - 1]
    const currentPrice = marketData[marketData.length - 1].last

    if (currentRSI < template.parameters.oversoldLevel) {
      return {
        action: 'BUY',
        quantity: template.parameters.positionSize,
        price: currentPrice,
        confidence: Math.max(0, (template.parameters.oversoldLevel - currentRSI) / template.parameters.oversoldLevel * 100),
        reason: `RSI(${currentRSI.toFixed(2)}) 低于超卖线(${template.parameters.oversoldLevel})`,
        timestamp: Date.now()
      }
    } else if (currentRSI > template.parameters.overboughtLevel) {
      return {
        action: 'SELL',
        quantity: template.parameters.positionSize,
        price: currentPrice,
        confidence: Math.max(0, (currentRSI - template.parameters.overboughtLevel) / (100 - template.parameters.overboughtLevel) * 100),
        reason: `RSI(${currentRSI.toFixed(2)}) 高于超买线(${template.parameters.overboughtLevel})`,
        timestamp: Date.now()
      }
    }

    return {
      action: 'HOLD',
      quantity: 0,
      price: currentPrice,
      confidence: 0,
      reason: `RSI(${currentRSI.toFixed(2)}) 在正常范围内`,
      timestamp: Date.now()
    }
  }

  // MACD趋势跟踪策略实现
  private executeMACDTrendFollowing(template: StrategyTemplate, marketData: MarketData[], ohlcvData: OHLCV[]): StrategySignal | null {
    if (ohlcvData.length < template.parameters.slowPeriod + template.parameters.signalPeriod) {
      return null
    }

    const closes = ohlcvData.map(d => d.close)
    const macd = this.technicalIndicatorService.calculateMACD(
      closes,
      template.parameters.fastPeriod,
      template.parameters.slowPeriod,
      template.parameters.signalPeriod
    )

    const currentMACD = macd.macd[macd.macd.length - 1]
    const currentSignal = macd.signal[macd.signal.length - 1]
    const currentPrice = marketData[marketData.length - 1].last

    if (currentMACD > currentSignal && macd.macd.length > 1) {
      const prevMACD = macd.macd[macd.macd.length - 2]
      const prevSignal = macd.signal[macd.signal.length - 2]
      
      if (prevMACD <= prevSignal) { // 金叉
        return {
          action: 'BUY',
          quantity: template.parameters.positionSize,
          price: currentPrice,
          confidence: Math.min(100, Math.abs(currentMACD - currentSignal) * 100),
          reason: 'MACD金叉，趋势转为看涨',
          timestamp: Date.now()
        }
      }
    } else if (currentMACD < currentSignal && macd.macd.length > 1) {
      const prevMACD = macd.macd[macd.macd.length - 2]
      const prevSignal = macd.signal[macd.signal.length - 2]
      
      if (prevMACD >= prevSignal) { // 死叉
        return {
          action: 'SELL',
          quantity: template.parameters.positionSize,
          price: currentPrice,
          confidence: Math.min(100, Math.abs(currentMACD - currentSignal) * 100),
          reason: 'MACD死叉，趋势转为看跌',
          timestamp: Date.now()
        }
      }
    }

    return {
      action: 'HOLD',
      quantity: 0,
      price: currentPrice,
      confidence: 0,
      reason: 'MACD无明确信号',
      timestamp: Date.now()
    }
  }

  // 布林带突破策略实现
  private executeBollingerBreakout(template: StrategyTemplate, marketData: MarketData[], ohlcvData: OHLCV[]): StrategySignal | null {
    if (ohlcvData.length < template.parameters.period) {
      return null
    }

    const closes = ohlcvData.map(d => d.close)
    const bollinger = this.technicalIndicatorService.calculateBollingerBands(
      closes,
      template.parameters.period,
      template.parameters.stdDev
    )

    const currentPrice = marketData[marketData.length - 1].last
    const upperBand = bollinger.upper[bollinger.upper.length - 1]
    const lowerBand = bollinger.lower[bollinger.lower.length - 1]
    const middleBand = bollinger.middle[bollinger.middle.length - 1]

    if (currentPrice > upperBand) {
      return {
        action: 'BUY',
        quantity: template.parameters.positionSize,
        price: currentPrice,
        confidence: Math.min(100, (currentPrice - upperBand) / upperBand * 100 * 10),
        reason: `价格突破布林带上轨(${upperBand.toFixed(2)})`,
        timestamp: Date.now()
      }
    } else if (currentPrice < lowerBand) {
      return {
        action: 'BUY',
        quantity: template.parameters.positionSize,
        price: currentPrice,
        confidence: Math.min(100, (lowerBand - currentPrice) / lowerBand * 100 * 10),
        reason: `价格跌破布林带下轨(${lowerBand.toFixed(2)})，反弹机会`,
        timestamp: Date.now()
      }
    }

    return {
      action: 'HOLD',
      quantity: 0,
      price: currentPrice,
      confidence: 0,
      reason: `价格在布林带中轨附近(${middleBand.toFixed(2)})`,
      timestamp: Date.now()
    }
  }

  // 网格交易策略实现
  private executeGridTrading(template: StrategyTemplate, marketData: MarketData[], ohlcvData: OHLCV[]): StrategySignal | null {
    const currentPrice = marketData[marketData.length - 1].last
    const basePrice = ohlcvData[ohlcvData.length - 1].close
    const gridSpacing = template.parameters.gridSpacing
    
    // 计算网格位置
    const gridLevel = Math.round((currentPrice - basePrice) / (basePrice * gridSpacing))
    
    if (gridLevel < -template.parameters.gridCount / 2) {
      return {
        action: 'BUY',
        quantity: template.parameters.baseAmount / currentPrice,
        price: currentPrice,
        confidence: 80,
        reason: `价格到达网格买入位置 (Level: ${gridLevel})`,
        timestamp: Date.now()
      }
    } else if (gridLevel > template.parameters.gridCount / 2) {
      return {
        action: 'SELL',
        quantity: template.parameters.baseAmount / currentPrice,
        price: currentPrice,
        confidence: 80,
        reason: `价格到达网格卖出位置 (Level: ${gridLevel})`,
        timestamp: Date.now()
      }
    }

    return {
      action: 'HOLD',
      quantity: 0,
      price: currentPrice,
      confidence: 0,
      reason: `价格在网格中性区域 (Level: ${gridLevel})`,
      timestamp: Date.now()
    }
  }

  // 剥头皮策略实现
  private executeScalping(template: StrategyTemplate, marketData: MarketData[], ohlcvData: OHLCV[]): StrategySignal | null {
    if (marketData.length < 2) return null

    const currentData = marketData[marketData.length - 1]
    const spread = (currentData.ask - currentData.bid) / currentData.last

    if (spread < template.parameters.minSpread) {
      // 价差足够小，可以进行剥头皮交易
      const shortTermTrend = ohlcvData.slice(-5).map(d => d.close)
      const isUptrend = shortTermTrend[shortTermTrend.length - 1] > shortTermTrend[0]

      if (isUptrend) {
        return {
          action: 'BUY',
          quantity: template.parameters.positionSize,
          price: currentData.ask,
          confidence: 70,
          reason: `短期上涨趋势，价差(${(spread * 100).toFixed(3)}%)适合剥头皮`,
          timestamp: Date.now()
        }
      } else {
        return {
          action: 'SELL',
          quantity: template.parameters.positionSize,
          price: currentData.bid,
          confidence: 70,
          reason: `短期下跌趋势，价差(${(spread * 100).toFixed(3)}%)适合剥头皮`,
          timestamp: Date.now()
        }
      }
    }

    return {
      action: 'HOLD',
      quantity: 0,
      price: currentData.last,
      confidence: 0,
      reason: `价差(${(spread * 100).toFixed(3)}%)过大，不适合剥头皮`,
      timestamp: Date.now()
    }
  }

  // 策略代码模板
  private getRSIMeanReversionCode(): string {
    return `
// RSI均值回归策略
function executeStrategy(marketData, ohlcvData, parameters) {
  const rsi = calculateRSI(ohlcvData.map(d => d.close), parameters.rsiPeriod);
  const currentRSI = rsi[rsi.length - 1];
  const currentPrice = marketData[marketData.length - 1].last;
  
  if (currentRSI < parameters.oversoldLevel) {
    return { action: 'BUY', quantity: parameters.positionSize, price: currentPrice };
  } else if (currentRSI > parameters.overboughtLevel) {
    return { action: 'SELL', quantity: parameters.positionSize, price: currentPrice };
  }
  
  return { action: 'HOLD', quantity: 0, price: currentPrice };
}
    `.trim()
  }

  private getMACDTrendFollowingCode(): string {
    return `
// MACD趋势跟踪策略
function executeStrategy(marketData, ohlcvData, parameters) {
  const macd = calculateMACD(ohlcvData.map(d => d.close), parameters.fastPeriod, parameters.slowPeriod, parameters.signalPeriod);
  const currentMACD = macd.macd[macd.macd.length - 1];
  const currentSignal = macd.signal[macd.signal.length - 1];
  const currentPrice = marketData[marketData.length - 1].last;
  
  if (currentMACD > currentSignal) {
    return { action: 'BUY', quantity: parameters.positionSize, price: currentPrice };
  } else if (currentMACD < currentSignal) {
    return { action: 'SELL', quantity: parameters.positionSize, price: currentPrice };
  }
  
  return { action: 'HOLD', quantity: 0, price: currentPrice };
}
    `.trim()
  }

  private getBollingerBreakoutCode(): string {
    return `
// 布林带突破策略
function executeStrategy(marketData, ohlcvData, parameters) {
  const bollinger = calculateBollingerBands(ohlcvData.map(d => d.close), parameters.period, parameters.stdDev);
  const currentPrice = marketData[marketData.length - 1].last;
  const upperBand = bollinger.upper[bollinger.upper.length - 1];
  const lowerBand = bollinger.lower[bollinger.lower.length - 1];
  
  if (currentPrice > upperBand) {
    return { action: 'BUY', quantity: parameters.positionSize, price: currentPrice };
  } else if (currentPrice < lowerBand) {
    return { action: 'BUY', quantity: parameters.positionSize, price: currentPrice };
  }
  
  return { action: 'HOLD', quantity: 0, price: currentPrice };
}
    `.trim()
  }

  private getGridTradingCode(): string {
    return `
// 网格交易策略
function executeStrategy(marketData, ohlcvData, parameters) {
  const currentPrice = marketData[marketData.length - 1].last;
  const basePrice = ohlcvData[ohlcvData.length - 1].close;
  const gridLevel = Math.round((currentPrice - basePrice) / (basePrice * parameters.gridSpacing));
  
  if (gridLevel < -parameters.gridCount / 2) {
    return { action: 'BUY', quantity: parameters.baseAmount / currentPrice, price: currentPrice };
  } else if (gridLevel > parameters.gridCount / 2) {
    return { action: 'SELL', quantity: parameters.baseAmount / currentPrice, price: currentPrice };
  }
  
  return { action: 'HOLD', quantity: 0, price: currentPrice };
}
    `.trim()
  }

  private getScalpingCode(): string {
    return `
// 剥头皮策略
function executeStrategy(marketData, ohlcvData, parameters) {
  const currentData = marketData[marketData.length - 1];
  const spread = (currentData.ask - currentData.bid) / currentData.last;
  
  if (spread < parameters.minSpread) {
    const shortTermTrend = ohlcvData.slice(-5).map(d => d.close);
    const isUptrend = shortTermTrend[shortTermTrend.length - 1] > shortTermTrend[0];
    
    if (isUptrend) {
      return { action: 'BUY', quantity: parameters.positionSize, price: currentData.ask };
    } else {
      return { action: 'SELL', quantity: parameters.positionSize, price: currentData.bid };
    }
  }
  
  return { action: 'HOLD', quantity: 0, price: currentData.last };
}
    `.trim()
  }
}

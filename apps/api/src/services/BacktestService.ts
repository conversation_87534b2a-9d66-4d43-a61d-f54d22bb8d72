import { StrategyTemplateService, StrategyTemplate, StrategySignal, BacktestResult } from './StrategyTemplateService'
import { TechnicalIndicatorService, OHLCV } from './TechnicalIndicatorService'
import { MarketData } from '@sfquant/core'
import axios from 'axios'

export interface BacktestConfig {
  strategyId: string
  symbol: string
  startDate: string
  endDate: string
  initialCapital: number
  commission: number
  slippage: number
  timeframe: string
}

export interface Trade {
  id: string
  timestamp: number
  action: 'BUY' | 'SELL'
  quantity: number
  price: number
  commission: number
  pnl?: number
  cumulativePnl?: number
}

export interface BacktestMetrics {
  totalReturn: number
  annualizedReturn: number
  sharpeRatio: number
  maxDrawdown: number
  winRate: number
  totalTrades: number
  profitableTrades: number
  avgWin: number
  avgLoss: number
  profitFactor: number
  calmarRatio: number
  sortinoRatio: number
  volatility: number
  beta: number
  alpha: number
}

export interface BacktestReport {
  config: BacktestConfig
  metrics: BacktestMetrics
  trades: Trade[]
  equity: { timestamp: number; value: number }[]
  drawdown: { timestamp: number; value: number }[]
  monthlyReturns: { month: string; return: number }[]
}

export class BacktestService {
  private strategyService: StrategyTemplateService
  private technicalIndicatorService: TechnicalIndicatorService

  constructor() {
    this.strategyService = new StrategyTemplateService()
    this.technicalIndicatorService = new TechnicalIndicatorService()
  }

  // 执行回测
  async runBacktest(config: BacktestConfig): Promise<BacktestReport> {
    console.log(`🔄 开始回测策略 ${config.strategyId} for ${config.symbol}`)
    
    // 获取历史数据
    const historicalData = await this.getHistoricalData(config.symbol, config.startDate, config.endDate, config.timeframe)
    
    if (historicalData.length === 0) {
      throw new Error('无法获取历史数据')
    }

    // 获取策略模板
    const strategy = this.strategyService.getStrategyTemplates().find(s => s.id === config.strategyId)
    if (!strategy) {
      throw new Error(`策略模板 ${config.strategyId} 不存在`)
    }

    // 执行回测
    const result = await this.executeBacktest(strategy, historicalData, config)
    
    console.log(`✅ 回测完成，总收益率: ${result.metrics.totalReturn.toFixed(2)}%`)
    
    return result
  }

  // 获取历史数据
  private async getHistoricalData(symbol: string, startDate: string, endDate: string, timeframe: string): Promise<OHLCV[]> {
    try {
      // 转换交易对格式 (BTC/USDT -> BTCUSDT)
      const binanceSymbol = symbol.replace('/', '')
      
      // 计算需要获取的数据量
      const start = new Date(startDate).getTime()
      const end = new Date(endDate).getTime()
      const timeframeMs = this.getTimeframeMs(timeframe)
      const limit = Math.min(1000, Math.ceil((end - start) / timeframeMs))
      
      // 从Binance获取K线数据
      const response = await axios.get(`https://api.binance.com/api/v3/klines`, {
        params: {
          symbol: binanceSymbol,
          interval: timeframe,
          startTime: start,
          endTime: end,
          limit: limit
        },
        timeout: 10000
      })

      return response.data.map((kline: any[]) => ({
        timestamp: kline[0],
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5])
      }))
    } catch (error: any) {
      console.error('获取历史数据失败:', error.message)
      return []
    }
  }

  // 执行回测逻辑
  private async executeBacktest(strategy: StrategyTemplate, data: OHLCV[], config: BacktestConfig): Promise<BacktestReport> {
    const trades: Trade[] = []
    const equity: { timestamp: number; value: number }[] = []
    const drawdown: { timestamp: number; value: number }[] = []
    
    let capital = config.initialCapital
    let position = 0
    let maxEquity = capital
    let tradeId = 1

    // 模拟市场数据
    for (let i = strategy.parameters.rsiPeriod || 20; i < data.length; i++) {
      const currentData = data.slice(0, i + 1)
      const marketData: MarketData[] = currentData.map(d => ({
        symbol: config.symbol,
        timestamp: d.timestamp,
        bid: d.close * 0.999,
        ask: d.close * 1.001,
        last: d.close,
        volume: d.volume,
        high: d.high,
        low: d.low,
        open: d.open,
        close: d.close
      }))

      // 生成交易信号
      const signal = await this.strategyService.executeStrategy(strategy.id, marketData, currentData)
      
      if (signal && signal.action !== 'HOLD') {
        const currentPrice = data[i].close
        const commission = currentPrice * signal.quantity * config.commission
        
        if (signal.action === 'BUY' && position <= 0) {
          // 买入
          const cost = currentPrice * signal.quantity + commission
          if (capital >= cost) {
            position += signal.quantity
            capital -= cost
            
            trades.push({
              id: `trade_${tradeId++}`,
              timestamp: data[i].timestamp,
              action: 'BUY',
              quantity: signal.quantity,
              price: currentPrice,
              commission: commission
            })
          }
        } else if (signal.action === 'SELL' && position > 0) {
          // 卖出
          const sellQuantity = Math.min(position, signal.quantity)
          const revenue = currentPrice * sellQuantity - commission
          
          capital += revenue
          position -= sellQuantity
          
          // 计算盈亏
          const buyTrade = trades.filter(t => t.action === 'BUY').pop()
          if (buyTrade) {
            const pnl = (currentPrice - buyTrade.price) * sellQuantity - commission - buyTrade.commission
            trades.push({
              id: `trade_${tradeId++}`,
              timestamp: data[i].timestamp,
              action: 'SELL',
              quantity: sellQuantity,
              price: currentPrice,
              commission: commission,
              pnl: pnl
            })
          }
        }
      }

      // 计算当前权益
      const currentEquity = capital + position * data[i].close
      equity.push({ timestamp: data[i].timestamp, value: currentEquity })
      
      // 计算回撤
      if (currentEquity > maxEquity) {
        maxEquity = currentEquity
      }
      const currentDrawdown = (maxEquity - currentEquity) / maxEquity * 100
      drawdown.push({ timestamp: data[i].timestamp, value: currentDrawdown })
    }

    // 计算指标
    const metrics = this.calculateMetrics(trades, equity, config.initialCapital)
    
    // 计算月度收益
    const monthlyReturns = this.calculateMonthlyReturns(equity)

    return {
      config,
      metrics,
      trades,
      equity,
      drawdown,
      monthlyReturns
    }
  }

  // 计算回测指标
  private calculateMetrics(trades: Trade[], equity: { timestamp: number; value: number }[], initialCapital: number): BacktestMetrics {
    const finalEquity = equity[equity.length - 1]?.value || initialCapital
    const totalReturn = (finalEquity - initialCapital) / initialCapital * 100
    
    // 计算收益率序列
    const returns: number[] = []
    for (let i = 1; i < equity.length; i++) {
      const ret = (equity[i].value - equity[i - 1].value) / equity[i - 1].value
      returns.push(ret)
    }
    
    // 计算各项指标
    const profitableTrades = trades.filter(t => t.pnl && t.pnl > 0).length
    const losingTrades = trades.filter(t => t.pnl && t.pnl < 0).length
    const winRate = trades.length > 0 ? (profitableTrades / trades.length) * 100 : 0
    
    const wins = trades.filter(t => t.pnl && t.pnl > 0).map(t => t.pnl!)
    const losses = trades.filter(t => t.pnl && t.pnl < 0).map(t => Math.abs(t.pnl!))
    
    const avgWin = wins.length > 0 ? wins.reduce((a, b) => a + b, 0) / wins.length : 0
    const avgLoss = losses.length > 0 ? losses.reduce((a, b) => a + b, 0) / losses.length : 0
    const profitFactor = avgLoss > 0 ? avgWin / avgLoss : 0
    
    // 最大回撤
    let maxDrawdown = 0
    let peak = initialCapital
    for (const point of equity) {
      if (point.value > peak) {
        peak = point.value
      }
      const drawdown = (peak - point.value) / peak * 100
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown
      }
    }
    
    // 波动率
    const avgReturn = returns.reduce((a, b) => a + b, 0) / returns.length
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length
    const volatility = Math.sqrt(variance) * Math.sqrt(252) * 100 // 年化波动率
    
    // 夏普比率
    const riskFreeRate = 0.02 // 假设无风险利率2%
    const excessReturn = totalReturn / 100 - riskFreeRate
    const sharpeRatio = volatility > 0 ? excessReturn / (volatility / 100) : 0
    
    // 年化收益率
    const days = (equity[equity.length - 1].timestamp - equity[0].timestamp) / (1000 * 60 * 60 * 24)
    const annualizedReturn = Math.pow(finalEquity / initialCapital, 365 / days) - 1
    
    return {
      totalReturn,
      annualizedReturn: annualizedReturn * 100,
      sharpeRatio,
      maxDrawdown,
      winRate,
      totalTrades: trades.length,
      profitableTrades,
      avgWin,
      avgLoss,
      profitFactor,
      calmarRatio: annualizedReturn > 0 && maxDrawdown > 0 ? (annualizedReturn * 100) / maxDrawdown : 0,
      sortinoRatio: sharpeRatio, // 简化实现
      volatility,
      beta: 1, // 简化实现
      alpha: totalReturn - 10 // 简化实现，假设基准收益10%
    }
  }

  // 计算月度收益
  private calculateMonthlyReturns(equity: { timestamp: number; value: number }[]): { month: string; return: number }[] {
    const monthlyData: Record<string, { start: number; end: number }> = {}
    
    for (const point of equity) {
      const date = new Date(point.timestamp)
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = { start: point.value, end: point.value }
      } else {
        monthlyData[monthKey].end = point.value
      }
    }
    
    return Object.entries(monthlyData).map(([month, data]) => ({
      month,
      return: (data.end - data.start) / data.start * 100
    }))
  }

  // 获取时间框架的毫秒数
  private getTimeframeMs(timeframe: string): number {
    const timeframeMap: Record<string, number> = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000
    }
    
    return timeframeMap[timeframe] || 60 * 60 * 1000
  }

  // 获取策略性能报告
  async getStrategyPerformance(strategyId: string): Promise<any> {
    const templates = this.strategyService.getStrategyTemplates()
    const strategy = templates.find(s => s.id === strategyId)
    
    if (!strategy) {
      throw new Error(`策略 ${strategyId} 不存在`)
    }

    // 运行快速回测
    const quickBacktest = await this.runBacktest({
      strategyId,
      symbol: 'BTC/USDT',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天前
      endDate: new Date().toISOString(),
      initialCapital: 10000,
      commission: 0.001,
      slippage: 0.0005,
      timeframe: strategy.timeframe
    })

    return {
      strategy,
      performance: quickBacktest.metrics,
      recentTrades: quickBacktest.trades.slice(-10)
    }
  }
}

import {
  UnifiedExchange,
  ExchangeConfig,
  ExchangeError,
  UnifiedOrder,
  OrderResult,
  MarketData,
  OrderBook,
  Balance
} from '@sfquant/core'
import { CCXTAdapter } from '@sfquant/ccxt-adapter'
import { DEXAdapter } from '@sfquant/dex-adapter'
import { config } from '../config'

export class ExchangeService {
  private exchanges: Map<string, UnifiedExchange> = new Map()
  private exchangeConfigs: Map<string, ExchangeConfig> = new Map()

  constructor() {
    this.initializeDefaultExchanges()
  }

  // 初始化默认交易所配置
  private initializeDefaultExchanges(): void {
    // CEX配置示例
    const cexConfigs: ExchangeConfig[] = [
      {
        id: 'binance',
        name: 'Binance',
        type: 'cex',
        sandbox: config.NODE_ENV !== 'production'
      },
      {
        id: 'okx',
        name: 'OKX',
        type: 'cex',
        sandbox: config.NODE_ENV !== 'production'
      }
    ]

    // DEX配置示例
    const dexConfigs: ExchangeConfig[] = [
      {
        id: 'ethereum-dex',
        name: 'Ethereum DEX',
        type: 'dex',
        chainId: 1,
        rpcUrl: 'https://eth.llamarpc.com',
        sandbox: config.NODE_ENV !== 'production'
      },
      {
        id: 'bsc-dex',
        name: 'BSC DEX',
        type: 'dex',
        chainId: 56,
        rpcUrl: 'https://bsc-dataseed.binance.org',
        sandbox: config.NODE_ENV !== 'production'
      }
    ]

    // 存储配置
    const allConfigs = cexConfigs.concat(dexConfigs)
    allConfigs.forEach(config => {
      this.exchangeConfigs.set(config.id, config)
    })
  }

  // 添加交易所
  async addExchange(exchangeConfig: ExchangeConfig): Promise<void> {
    try {
      let exchange: UnifiedExchange

      if (exchangeConfig.type === 'cex') {
        exchange = new CCXTAdapter()
      } else {
        exchange = new DEXAdapter()
      }

      await exchange.initialize(exchangeConfig)

      this.exchanges.set(exchangeConfig.id, exchange)
      this.exchangeConfigs.set(exchangeConfig.id, exchangeConfig)

      console.log(`Exchange ${exchangeConfig.name} initialized successfully`)
    } catch (error) {
      throw new ExchangeError(
        `Failed to initialize exchange ${exchangeConfig.name}: ${error}`,
        exchangeConfig.id
      )
    }
  }

  // 移除交易所
  removeExchange(exchangeId: string): void {
    this.exchanges.delete(exchangeId)
    this.exchangeConfigs.delete(exchangeId)
  }

  // 获取交易所
  getExchange(exchangeId: string): UnifiedExchange | undefined {
    return this.exchanges.get(exchangeId)
  }

  // 获取所有交易所
  getAllExchanges(): Map<string, UnifiedExchange> {
    return new Map(this.exchanges)
  }

  // 获取交易所列表
  getExchangeList(): Array<{id: string, name: string, type: 'cex' | 'dex', initialized: boolean}> {
    return Array.from(this.exchangeConfigs.entries()).map(([id, config]) => ({
      id,
      name: config.name,
      type: config.type,
      initialized: this.exchanges.has(id)
    }))
  }

  // 获取支持的市场
  async getMarkets(exchangeId: string): Promise<string[]> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.fetchMarkets()
  }

  // 获取市场数据
  async getTicker(exchangeId: string, symbol: string): Promise<MarketData> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.fetchTicker(symbol)
  }

  // 获取订单簿
  async getOrderBook(exchangeId: string, symbol: string, limit?: number): Promise<OrderBook> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.fetchOrderBook(symbol, limit)
  }

  // 获取账户余额
  async getBalance(exchangeId: string): Promise<Record<string, Balance>> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.fetchBalance()
  }

  // 创建订单
  async createOrder(exchangeId: string, order: UnifiedOrder): Promise<OrderResult> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.createOrder(order)
  }

  // 取消订单
  async cancelOrder(exchangeId: string, orderId: string, symbol?: string): Promise<OrderResult> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.cancelOrder(orderId, symbol)
  }

  // 获取未完成订单
  async getOpenOrders(exchangeId: string, symbol?: string): Promise<OrderResult[]> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.fetchOpenOrders(symbol)
  }

  // 获取历史订单
  async getClosedOrders(
    exchangeId: string,
    symbol?: string,
    since?: number,
    limit?: number
  ): Promise<OrderResult[]> {
    const exchange = this.getExchange(exchangeId)
    if (!exchange) {
      throw new ExchangeError(`Exchange ${exchangeId} not found`, exchangeId)
    }

    return await exchange.fetchClosedOrders(symbol, since, limit)
  }

  // 批量获取多个交易所的价格
  async getMultiExchangePrices(symbol: string): Promise<Record<string, MarketData>> {
    const prices: Record<string, MarketData> = {}
    const promises: Promise<void>[] = []

    for (const [exchangeId, exchange] of this.exchanges) {
      promises.push(
        exchange.fetchTicker(symbol)
          .then(ticker => {
            prices[exchangeId] = ticker
          })
          .catch(error => {
            console.warn(`Failed to get price from ${exchangeId}:`, error.message)
          })
      )
    }

    await Promise.all(promises)
    return prices
  }

  // 寻找套利机会
  async findArbitrageOpportunities(symbol: string): Promise<Array<{
    buyExchange: string
    sellExchange: string
    buyPrice: number
    sellPrice: number
    spread: number
    spreadPercent: number
  }>> {
    const prices = await this.getMultiExchangePrices(symbol)
    const opportunities: Array<{
      buyExchange: string
      sellExchange: string
      buyPrice: number
      sellPrice: number
      spread: number
      spreadPercent: number
    }> = []

    const exchanges = Object.keys(prices)

    for (let i = 0; i < exchanges.length; i++) {
      for (let j = i + 1; j < exchanges.length; j++) {
        const exchange1 = exchanges[i]
        const exchange2 = exchanges[j]

        const price1 = prices[exchange1]
        const price2 = prices[exchange2]

        if (!price1 || !price2) continue

        // 检查从exchange1买入，exchange2卖出
        if (price1.ask < price2.bid) {
          const spread = price2.bid - price1.ask
          const spreadPercent = (spread / price1.ask) * 100

          opportunities.push({
            buyExchange: exchange1,
            sellExchange: exchange2,
            buyPrice: price1.ask,
            sellPrice: price2.bid,
            spread,
            spreadPercent
          })
        }

        // 检查从exchange2买入，exchange1卖出
        if (price2.ask < price1.bid) {
          const spread = price1.bid - price2.ask
          const spreadPercent = (spread / price2.ask) * 100

          opportunities.push({
            buyExchange: exchange2,
            sellExchange: exchange1,
            buyPrice: price2.ask,
            sellPrice: price1.bid,
            spread,
            spreadPercent
          })
        }
      }
    }

    // 按收益率排序
    return opportunities.sort((a, b) => b.spreadPercent - a.spreadPercent)
  }

  // 健康检查
  async healthCheck(): Promise<Record<string, {status: 'ok' | 'error', message?: string}>> {
    const results: Record<string, {status: 'ok' | 'error', message?: string}> = {}

    for (const [exchangeId, exchange] of this.exchanges) {
      try {
        if (exchange.isInitialized()) {
          // 尝试获取市场列表来测试连接
          await exchange.fetchMarkets()
          results[exchangeId] = { status: 'ok' }
        } else {
          results[exchangeId] = { status: 'error', message: 'Not initialized' }
        }
      } catch (error) {
        results[exchangeId] = {
          status: 'error',
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      }
    }

    return results
  }
}

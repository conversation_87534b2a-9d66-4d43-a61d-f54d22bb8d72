import { MarketData } from '@sfquant/core'

export interface OHLCV {
  timestamp: number
  open: number
  high: number
  low: number
  close: number
  volume: number
}

export interface TechnicalIndicators {
  sma: number[]
  ema: number[]
  rsi: number[]
  macd: {
    macd: number[]
    signal: number[]
    histogram: number[]
  }
  bollinger: {
    upper: number[]
    middle: number[]
    lower: number[]
  }
  stochastic: {
    k: number[]
    d: number[]
  }
  atr: number[]
  adx: number[]
}

export class TechnicalIndicatorService {
  
  // 简单移动平均线 (SMA)
  calculateSMA(prices: number[], period: number): number[] {
    const sma: number[] = []
    
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
      sma.push(sum / period)
    }
    
    return sma
  }

  // 指数移动平均线 (EMA)
  calculateEMA(prices: number[], period: number): number[] {
    const ema: number[] = []
    const multiplier = 2 / (period + 1)
    
    // 第一个EMA值使用SMA
    const firstSMA = prices.slice(0, period).reduce((a, b) => a + b, 0) / period
    ema.push(firstSMA)
    
    for (let i = period; i < prices.length; i++) {
      const emaValue = (prices[i] - ema[ema.length - 1]) * multiplier + ema[ema.length - 1]
      ema.push(emaValue)
    }
    
    return ema
  }

  // 相对强弱指数 (RSI)
  calculateRSI(prices: number[], period: number = 14): number[] {
    const rsi: number[] = []
    const gains: number[] = []
    const losses: number[] = []
    
    // 计算价格变化
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1]
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? Math.abs(change) : 0)
    }
    
    // 计算初始平均收益和损失
    let avgGain = gains.slice(0, period).reduce((a, b) => a + b, 0) / period
    let avgLoss = losses.slice(0, period).reduce((a, b) => a + b, 0) / period
    
    for (let i = period; i < gains.length; i++) {
      avgGain = (avgGain * (period - 1) + gains[i]) / period
      avgLoss = (avgLoss * (period - 1) + losses[i]) / period
      
      const rs = avgGain / avgLoss
      const rsiValue = 100 - (100 / (1 + rs))
      rsi.push(rsiValue)
    }
    
    return rsi
  }

  // MACD指标
  calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): {
    macd: number[]
    signal: number[]
    histogram: number[]
  } {
    const fastEMA = this.calculateEMA(prices, fastPeriod)
    const slowEMA = this.calculateEMA(prices, slowPeriod)
    
    const macd: number[] = []
    const startIndex = slowPeriod - fastPeriod
    
    for (let i = 0; i < fastEMA.length - startIndex; i++) {
      macd.push(fastEMA[i + startIndex] - slowEMA[i])
    }
    
    const signal = this.calculateEMA(macd, signalPeriod)
    const histogram: number[] = []
    
    const signalStartIndex = macd.length - signal.length
    for (let i = 0; i < signal.length; i++) {
      histogram.push(macd[i + signalStartIndex] - signal[i])
    }
    
    return { macd, signal, histogram }
  }

  // 布林带
  calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2): {
    upper: number[]
    middle: number[]
    lower: number[]
  } {
    const sma = this.calculateSMA(prices, period)
    const upper: number[] = []
    const lower: number[] = []
    
    for (let i = 0; i < sma.length; i++) {
      const slice = prices.slice(i, i + period)
      const mean = sma[i]
      const variance = slice.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / period
      const standardDeviation = Math.sqrt(variance)
      
      upper.push(mean + (standardDeviation * stdDev))
      lower.push(mean - (standardDeviation * stdDev))
    }
    
    return {
      upper,
      middle: sma,
      lower
    }
  }

  // 随机指标 (Stochastic)
  calculateStochastic(highs: number[], lows: number[], closes: number[], kPeriod: number = 14, dPeriod: number = 3): {
    k: number[]
    d: number[]
  } {
    const k: number[] = []
    
    for (let i = kPeriod - 1; i < closes.length; i++) {
      const highestHigh = Math.max(...highs.slice(i - kPeriod + 1, i + 1))
      const lowestLow = Math.min(...lows.slice(i - kPeriod + 1, i + 1))
      const currentClose = closes[i]
      
      const kValue = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100
      k.push(kValue)
    }
    
    const d = this.calculateSMA(k, dPeriod)
    
    return { k, d }
  }

  // 平均真实波幅 (ATR)
  calculateATR(highs: number[], lows: number[], closes: number[], period: number = 14): number[] {
    const trueRanges: number[] = []
    
    for (let i = 1; i < highs.length; i++) {
      const tr1 = highs[i] - lows[i]
      const tr2 = Math.abs(highs[i] - closes[i - 1])
      const tr3 = Math.abs(lows[i] - closes[i - 1])
      
      trueRanges.push(Math.max(tr1, tr2, tr3))
    }
    
    return this.calculateSMA(trueRanges, period)
  }

  // 平均方向指数 (ADX)
  calculateADX(highs: number[], lows: number[], closes: number[], period: number = 14): number[] {
    const adx: number[] = []
    // ADX计算较复杂，这里提供简化版本
    // 实际应用中可能需要更精确的实现
    
    for (let i = period; i < closes.length; i++) {
      // 简化的ADX计算
      const slice = closes.slice(i - period, i)
      const trend = slice[slice.length - 1] - slice[0]
      const volatility = Math.max(...highs.slice(i - period, i)) - Math.min(...lows.slice(i - period, i))
      
      const adxValue = Math.abs(trend) / volatility * 100
      adx.push(Math.min(100, Math.max(0, adxValue)))
    }
    
    return adx
  }

  // 计算所有技术指标
  calculateAllIndicators(ohlcvData: OHLCV[]): TechnicalIndicators {
    const closes = ohlcvData.map(d => d.close)
    const highs = ohlcvData.map(d => d.high)
    const lows = ohlcvData.map(d => d.low)
    
    return {
      sma: this.calculateSMA(closes, 20),
      ema: this.calculateEMA(closes, 20),
      rsi: this.calculateRSI(closes, 14),
      macd: this.calculateMACD(closes),
      bollinger: this.calculateBollingerBands(closes),
      stochastic: this.calculateStochastic(highs, lows, closes),
      atr: this.calculateATR(highs, lows, closes),
      adx: this.calculateADX(highs, lows, closes)
    }
  }

  // 生成交易信号
  generateSignals(indicators: TechnicalIndicators, currentPrice: number): {
    signals: string[]
    strength: number // 0-100
    recommendation: 'BUY' | 'SELL' | 'HOLD'
  } {
    const signals: string[] = []
    let bullishSignals = 0
    let bearishSignals = 0
    
    // RSI信号
    const lastRSI = indicators.rsi[indicators.rsi.length - 1]
    if (lastRSI < 30) {
      signals.push('RSI超卖 - 买入信号')
      bullishSignals++
    } else if (lastRSI > 70) {
      signals.push('RSI超买 - 卖出信号')
      bearishSignals++
    }
    
    // MACD信号
    const lastMACD = indicators.macd.macd[indicators.macd.macd.length - 1]
    const lastSignal = indicators.macd.signal[indicators.macd.signal.length - 1]
    if (lastMACD > lastSignal) {
      signals.push('MACD金叉 - 买入信号')
      bullishSignals++
    } else {
      signals.push('MACD死叉 - 卖出信号')
      bearishSignals++
    }
    
    // 布林带信号
    const lastUpper = indicators.bollinger.upper[indicators.bollinger.upper.length - 1]
    const lastLower = indicators.bollinger.lower[indicators.bollinger.lower.length - 1]
    if (currentPrice <= lastLower) {
      signals.push('价格触及布林带下轨 - 买入信号')
      bullishSignals++
    } else if (currentPrice >= lastUpper) {
      signals.push('价格触及布林带上轨 - 卖出信号')
      bearishSignals++
    }
    
    const totalSignals = bullishSignals + bearishSignals
    const strength = totalSignals > 0 ? Math.abs(bullishSignals - bearishSignals) / totalSignals * 100 : 0
    
    let recommendation: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
    if (bullishSignals > bearishSignals) {
      recommendation = 'BUY'
    } else if (bearishSignals > bullishSignals) {
      recommendation = 'SELL'
    }
    
    return { signals, strength, recommendation }
  }
}

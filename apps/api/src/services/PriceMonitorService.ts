import { MarketData } from '@sfquant/core'
import { RedisService } from './RedisService'
import { EventEmitter } from 'events'

export interface PriceAlert {
  id: string
  symbol: string
  type: 'PRICE_ABOVE' | 'PRICE_BELOW' | 'CHANGE_ABOVE' | 'CHANGE_BELOW' | 'VOLUME_SPIKE' | 'VOLATILITY_HIGH'
  threshold: number
  isActive: boolean
  createdAt: number
  triggeredAt?: number
  message?: string
}

export interface PriceAnomaly {
  id: string
  symbol: string
  type: 'PRICE_SPIKE' | 'PRICE_DROP' | 'VOLUME_ANOMALY' | 'SPREAD_ANOMALY' | 'FLASH_CRASH'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  timestamp: number
  currentPrice: number
  previousPrice: number
  changePercent: number
  volume: number
  description: string
  metadata: Record<string, any>
}

export interface MonitoringStats {
  totalAlerts: number
  activeAlerts: number
  triggeredToday: number
  anomaliesDetected: number
  monitoredSymbols: number
  lastUpdate: number
}

export class PriceMonitorService extends EventEmitter {
  private alerts: Map<string, PriceAlert> = new Map()
  private priceHistory: Map<string, MarketData[]> = new Map()
  private anomalies: PriceAnomaly[] = []
  private monitoringInterval: NodeJS.Timeout | null = null
  private readonly HISTORY_LIMIT = 100
  private readonly ANOMALY_LIMIT = 1000

  constructor(private redis: RedisService) {
    super()
    this.startMonitoring()
  }

  // 开始监控
  private startMonitoring(): void {
    console.log('🔍 启动价格异常监控服务')
    
    // 每5秒检查一次
    this.monitoringInterval = setInterval(() => {
      this.checkAnomalies()
    }, 5000)

    // 订阅价格更新
    this.redis.subscribe('price_updates', (message: string) => {
      try {
        const update = JSON.parse(message)
        this.handlePriceUpdate(update.symbol, update.data)
      } catch (error) {
        console.error('处理价格更新失败:', error)
      }
    })
  }

  // 停止监控
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
    console.log('⏹️ 价格监控服务已停止')
  }

  // 添加价格提醒
  addAlert(alert: Omit<PriceAlert, 'id' | 'createdAt'>): string {
    const id = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const newAlert: PriceAlert = {
      ...alert,
      id,
      createdAt: Date.now()
    }
    
    this.alerts.set(id, newAlert)
    console.log(`📢 添加价格提醒: ${alert.symbol} ${alert.type} ${alert.threshold}`)
    
    return id
  }

  // 移除价格提醒
  removeAlert(alertId: string): boolean {
    const removed = this.alerts.delete(alertId)
    if (removed) {
      console.log(`🗑️ 移除价格提醒: ${alertId}`)
    }
    return removed
  }

  // 获取所有提醒
  getAlerts(): PriceAlert[] {
    return Array.from(this.alerts.values())
  }

  // 获取异常记录
  getAnomalies(limit: number = 50): PriceAnomaly[] {
    return this.anomalies.slice(-limit).reverse()
  }

  // 获取监控统计
  getStats(): MonitoringStats {
    const now = Date.now()
    const todayStart = new Date().setHours(0, 0, 0, 0)
    
    return {
      totalAlerts: this.alerts.size,
      activeAlerts: Array.from(this.alerts.values()).filter(a => a.isActive).length,
      triggeredToday: Array.from(this.alerts.values()).filter(a => a.triggeredAt && a.triggeredAt > todayStart).length,
      anomaliesDetected: this.anomalies.length,
      monitoredSymbols: this.priceHistory.size,
      lastUpdate: now
    }
  }

  // 处理价格更新
  private handlePriceUpdate(symbol: string, marketData: MarketData): void {
    // 更新价格历史
    if (!this.priceHistory.has(symbol)) {
      this.priceHistory.set(symbol, [])
    }
    
    const history = this.priceHistory.get(symbol)!
    history.push(marketData)
    
    // 保持历史记录限制
    if (history.length > this.HISTORY_LIMIT) {
      history.shift()
    }

    // 检查价格提醒
    this.checkAlerts(symbol, marketData)
    
    // 检查价格异常
    this.detectAnomalies(symbol, marketData, history)
  }

  // 检查价格提醒
  private checkAlerts(symbol: string, marketData: MarketData): void {
    for (const alert of this.alerts.values()) {
      if (!alert.isActive || alert.symbol !== symbol) {
        continue
      }

      let triggered = false
      let message = ''

      switch (alert.type) {
        case 'PRICE_ABOVE':
          if (marketData.last >= alert.threshold) {
            triggered = true
            message = `${symbol} 价格 $${marketData.last.toFixed(2)} 超过设定值 $${alert.threshold}`
          }
          break
          
        case 'PRICE_BELOW':
          if (marketData.last <= alert.threshold) {
            triggered = true
            message = `${symbol} 价格 $${marketData.last.toFixed(2)} 低于设定值 $${alert.threshold}`
          }
          break
          
        case 'CHANGE_ABOVE':
          if (marketData.changePercent && Math.abs(marketData.changePercent) >= alert.threshold) {
            triggered = true
            message = `${symbol} 24h变化 ${marketData.changePercent.toFixed(2)}% 超过设定值 ${alert.threshold}%`
          }
          break
          
        case 'VOLUME_SPIKE':
          // 简化的成交量异常检测
          if (marketData.volume > alert.threshold) {
            triggered = true
            message = `${symbol} 成交量 ${marketData.volume.toFixed(2)} 超过设定值 ${alert.threshold}`
          }
          break
      }

      if (triggered) {
        alert.triggeredAt = Date.now()
        alert.message = message
        alert.isActive = false // 触发后自动禁用
        
        console.log(`🚨 价格提醒触发: ${message}`)
        
        // 发送事件
        this.emit('alert_triggered', {
          alert,
          marketData,
          message
        })
      }
    }
  }

  // 检测价格异常
  private detectAnomalies(symbol: string, currentData: MarketData, history: MarketData[]): void {
    if (history.length < 10) return // 需要足够的历史数据

    const anomalies: PriceAnomaly[] = []
    const previousData = history[history.length - 2]
    
    if (!previousData) return

    const priceChange = (currentData.last - previousData.last) / previousData.last * 100
    const volumeChange = (currentData.volume - previousData.volume) / previousData.volume * 100

    // 检测价格剧烈波动
    if (Math.abs(priceChange) > 10) {
      const severity = Math.abs(priceChange) > 20 ? 'CRITICAL' : Math.abs(priceChange) > 15 ? 'HIGH' : 'MEDIUM'
      
      anomalies.push({
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol,
        type: priceChange > 0 ? 'PRICE_SPIKE' : 'PRICE_DROP',
        severity,
        timestamp: currentData.timestamp,
        currentPrice: currentData.last,
        previousPrice: previousData.last,
        changePercent: priceChange,
        volume: currentData.volume,
        description: `${symbol} 价格${priceChange > 0 ? '暴涨' : '暴跌'} ${Math.abs(priceChange).toFixed(2)}%`,
        metadata: {
          timeframe: '5s',
          volumeChange: volumeChange.toFixed(2)
        }
      })
    }

    // 检测成交量异常
    if (Math.abs(volumeChange) > 200) {
      anomalies.push({
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol,
        type: 'VOLUME_ANOMALY',
        severity: Math.abs(volumeChange) > 500 ? 'HIGH' : 'MEDIUM',
        timestamp: currentData.timestamp,
        currentPrice: currentData.last,
        previousPrice: previousData.last,
        changePercent: priceChange,
        volume: currentData.volume,
        description: `${symbol} 成交量异常变化 ${volumeChange.toFixed(2)}%`,
        metadata: {
          previousVolume: previousData.volume,
          volumeRatio: (currentData.volume / previousData.volume).toFixed(2)
        }
      })
    }

    // 检测买卖价差异常
    const spread = (currentData.ask - currentData.bid) / currentData.last * 100
    if (spread > 1) { // 价差超过1%
      anomalies.push({
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol,
        type: 'SPREAD_ANOMALY',
        severity: spread > 2 ? 'HIGH' : 'MEDIUM',
        timestamp: currentData.timestamp,
        currentPrice: currentData.last,
        previousPrice: previousData.last,
        changePercent: priceChange,
        volume: currentData.volume,
        description: `${symbol} 买卖价差异常 ${spread.toFixed(3)}%`,
        metadata: {
          bid: currentData.bid,
          ask: currentData.ask,
          spread: spread.toFixed(3)
        }
      })
    }

    // 检测闪崩
    const recentPrices = history.slice(-5).map(d => d.last)
    const maxRecent = Math.max(...recentPrices)
    const minRecent = Math.min(...recentPrices)
    const flashCrashThreshold = (maxRecent - minRecent) / maxRecent * 100

    if (flashCrashThreshold > 5 && currentData.last === minRecent) {
      anomalies.push({
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        symbol,
        type: 'FLASH_CRASH',
        severity: flashCrashThreshold > 10 ? 'CRITICAL' : 'HIGH',
        timestamp: currentData.timestamp,
        currentPrice: currentData.last,
        previousPrice: maxRecent,
        changePercent: -flashCrashThreshold,
        volume: currentData.volume,
        description: `${symbol} 疑似闪崩，短时间内下跌 ${flashCrashThreshold.toFixed(2)}%`,
        metadata: {
          maxPrice: maxRecent,
          minPrice: minRecent,
          timeWindow: '25s'
        }
      })
    }

    // 添加异常到记录
    for (const anomaly of anomalies) {
      this.anomalies.push(anomaly)
      
      // 保持异常记录限制
      if (this.anomalies.length > this.ANOMALY_LIMIT) {
        this.anomalies.shift()
      }

      console.log(`⚠️ 检测到价格异常: ${anomaly.description}`)
      
      // 发送事件
      this.emit('anomaly_detected', {
        anomaly,
        marketData: currentData
      })
    }
  }

  // 定期检查异常
  private checkAnomalies(): void {
    // 这里可以添加更复杂的异常检测逻辑
    // 例如：跨交易所价格差异、技术指标异常等
  }

  // 获取符号的价格历史
  getPriceHistory(symbol: string, limit: number = 50): MarketData[] {
    const history = this.priceHistory.get(symbol) || []
    return history.slice(-limit)
  }

  // 清理过期数据
  cleanup(): void {
    const now = Date.now()
    const oneDay = 24 * 60 * 60 * 1000

    // 清理过期的异常记录（保留7天）
    this.anomalies = this.anomalies.filter(a => now - a.timestamp < 7 * oneDay)

    // 清理已触发的旧提醒（保留1天）
    for (const [id, alert] of this.alerts.entries()) {
      if (alert.triggeredAt && now - alert.triggeredAt > oneDay) {
        this.alerts.delete(id)
      }
    }

    console.log('🧹 价格监控数据清理完成')
  }
}

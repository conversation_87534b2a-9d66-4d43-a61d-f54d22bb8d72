import {
  StrategyConfig,
  StrategyContext,
  StrategyResult,
  StrategyLanguage,
  Signal
} from '@sfquant/core'
import {
  StrategyRuntimeManager,
  TypeScriptRuntime,
  ValidationResult
} from '@sfquant/strategy-runtime'
import { DatabaseService } from './DatabaseService'
import { RedisService } from './RedisService'
import { config } from '../config'

export interface StrategyExecution {
  id: string
  strategyId: string
  status: 'running' | 'stopped' | 'error'
  startTime: number
  lastExecution: number
  executionCount: number
  signals: Signal[]
  errors: string[]
  metrics: Record<string, number>
}

export class StrategyService {
  private runtimeManager: StrategyRuntimeManager
  private runningStrategies: Map<string, StrategyExecution> = new Map()
  private executionIntervals: Map<string, NodeJS.Timeout> = new Map()

  constructor(
    private db: DatabaseService,
    private redis: RedisService
  ) {
    this.runtimeManager = new StrategyRuntimeManager()
    this.initializeRuntimes()
  }

  private initializeRuntimes(): void {
    // 注册TypeScript运行时
    const tsRuntime = new TypeScriptRuntime()
    this.runtimeManager.registerRuntime(tsRuntime)

    // TODO: 注册Python运行时
    // const pythonRuntime = new PythonRuntime()
    // this.runtimeManager.registerRuntime(pythonRuntime)
  }

  // 创建策略
  async createStrategy(strategy: Omit<StrategyConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<StrategyConfig> {
    const now = Date.now()
    const strategyConfig: StrategyConfig = {
      ...strategy,
      id: this.generateStrategyId(),
      createdAt: now,
      updatedAt: now
    }

    // 验证策略代码
    const validation = await this.validateStrategy(strategy.language, strategy.code)
    if (!validation.valid) {
      throw new Error(`Strategy validation failed: ${validation.errors.join(', ')}`)
    }

    // 保存到数据库
    await this.saveStrategy(strategyConfig)

    // 缓存到Redis
    await this.redis.setex(
      `strategy:${strategyConfig.id}`,
      config.MARKET_DATA_CACHE_TTL,
      JSON.stringify(strategyConfig)
    )

    return strategyConfig
  }

  // 更新策略
  async updateStrategy(id: string, updates: Partial<StrategyConfig>): Promise<StrategyConfig> {
    const existingStrategy = await this.getStrategy(id)
    if (!existingStrategy) {
      throw new Error(`Strategy ${id} not found`)
    }

    // 如果更新了代码，需要重新验证
    if (updates.code) {
      const language = updates.language || existingStrategy.language
      const validation = await this.validateStrategy(language, updates.code)
      if (!validation.valid) {
        throw new Error(`Strategy validation failed: ${validation.errors.join(', ')}`)
      }
    }

    const updatedStrategy: StrategyConfig = {
      ...existingStrategy,
      ...updates,
      updatedAt: Date.now()
    }

    // 如果策略正在运行，需要停止并重新启动
    if (this.runningStrategies.has(id)) {
      await this.stopStrategy(id)
    }

    // 保存到数据库
    await this.saveStrategy(updatedStrategy)

    // 更新缓存
    await this.redis.setex(
      `strategy:${id}`,
      config.MARKET_DATA_CACHE_TTL,
      JSON.stringify(updatedStrategy)
    )

    return updatedStrategy
  }

  // 获取策略
  async getStrategy(id: string): Promise<StrategyConfig | null> {
    // 先从缓存获取
    const cached = await this.redis.get(`strategy:${id}`)
    if (cached) {
      return JSON.parse(cached)
    }

    // 从数据库获取
    const strategy = await this.loadStrategy(id)
    if (strategy) {
      // 缓存结果
      await this.redis.setex(
        `strategy:${id}`,
        config.MARKET_DATA_CACHE_TTL,
        JSON.stringify(strategy)
      )
    }

    return strategy
  }

  // 获取所有策略
  async getAllStrategies(): Promise<StrategyConfig[]> {
    return await this.loadAllStrategies()
  }

  // 删除策略
  async deleteStrategy(id: string): Promise<void> {
    // 停止运行中的策略
    if (this.runningStrategies.has(id)) {
      await this.stopStrategy(id)
    }

    // 从数据库删除
    await this.removeStrategy(id)

    // 从缓存删除
    await this.redis.del(`strategy:${id}`)
  }

  // 验证策略
  async validateStrategy(language: StrategyLanguage, code: string): Promise<ValidationResult> {
    return await this.runtimeManager.validateStrategy(language, code)
  }

  // 启动策略
  async startStrategy(id: string): Promise<void> {
    const strategy = await this.getStrategy(id)
    if (!strategy) {
      throw new Error(`Strategy ${id} not found`)
    }

    if (this.runningStrategies.has(id)) {
      throw new Error(`Strategy ${id} is already running`)
    }

    // 检查并发限制
    if (this.runningStrategies.size >= config.MAX_CONCURRENT_STRATEGIES) {
      throw new Error('Maximum concurrent strategies limit reached')
    }

    const execution: StrategyExecution = {
      id: this.generateExecutionId(),
      strategyId: id,
      status: 'running',
      startTime: Date.now(),
      lastExecution: 0,
      executionCount: 0,
      signals: [],
      errors: [],
      metrics: {}
    }

    this.runningStrategies.set(id, execution)

    // 设置执行间隔（这里简化为每秒执行一次）
    const interval = setInterval(async () => {
      await this.executeStrategy(id)
    }, 1000)

    this.executionIntervals.set(id, interval)

    console.log(`Strategy ${strategy.name} started`)
  }

  // 停止策略
  async stopStrategy(id: string): Promise<void> {
    const execution = this.runningStrategies.get(id)
    if (!execution) {
      throw new Error(`Strategy ${id} is not running`)
    }

    // 清除执行间隔
    const interval = this.executionIntervals.get(id)
    if (interval) {
      clearInterval(interval)
      this.executionIntervals.delete(id)
    }

    // 更新状态
    execution.status = 'stopped'
    this.runningStrategies.delete(id)

    console.log(`Strategy ${id} stopped`)
  }

  // 执行策略
  private async executeStrategy(id: string): Promise<void> {
    const execution = this.runningStrategies.get(id)
    const strategy = await this.getStrategy(id)

    if (!execution || !strategy) {
      return
    }

    try {
      // 构建策略执行上下文
      const context = await this.buildStrategyContext(strategy)

      // 执行策略
      const result = await this.runtimeManager.executeStrategy(
        strategy.language,
        strategy.code,
        context
      )

      // 更新执行信息
      execution.lastExecution = Date.now()
      execution.executionCount++
      execution.signals.push(...result.signals)
      execution.metrics = { ...execution.metrics, ...result.metrics }

      // 处理策略信号
      await this.processStrategySignals(strategy, result.signals)

      // 记录日志
      if (result.logs && result.logs.length > 0) {
        console.log(`Strategy ${strategy.name} logs:`, result.logs)
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      execution.errors.push(errorMessage)
      execution.status = 'error'

      console.error(`Strategy ${strategy.name} execution error:`, errorMessage)

      // 如果错误过多，自动停止策略
      if (execution.errors.length > 10) {
        await this.stopStrategy(id)
      }
    }
  }

  // 构建策略执行上下文
  private async buildStrategyContext(strategy: StrategyConfig): Promise<StrategyContext> {
    // 这里需要从市场数据服务获取实时数据
    // 简化实现，返回模拟数据
    const marketData: Record<string, any> = {}
    const orderBooks: Record<string, any> = {}
    const balances: Record<string, any> = {}
    const openOrders: any[] = []

    return {
      strategy,
      marketData,
      orderBooks,
      balances,
      openOrders,
      timestamp: Date.now()
    }
  }

  // 处理策略信号
  private async processStrategySignals(strategy: StrategyConfig, signals: Signal[]): Promise<void> {
    for (const signal of signals) {
      try {
        // 这里应该根据信号执行实际的交易操作
        // 简化实现，只记录日志
        console.log(`Strategy ${strategy.name} signal:`, {
          type: signal.type,
          symbol: signal.symbol,
          amount: signal.amount,
          price: signal.price,
          confidence: signal.confidence
        })

        // TODO: 实现实际的交易执行逻辑
        // await this.executeTradeSignal(strategy, signal)

      } catch (error) {
        console.error(`Failed to process signal:`, error)
      }
    }
  }

  // 获取策略执行状态
  getStrategyExecution(id: string): StrategyExecution | undefined {
    return this.runningStrategies.get(id)
  }

  // 获取所有运行中的策略
  getRunningStrategies(): StrategyExecution[] {
    return Array.from(this.runningStrategies.values())
  }

  // 获取策略统计信息
  async getStrategyStats(id: string): Promise<{
    totalExecutions: number
    totalSignals: number
    errorRate: number
    avgExecutionTime: number
    lastError?: string
  }> {
    const execution = this.runningStrategies.get(id)

    if (!execution) {
      return {
        totalExecutions: 0,
        totalSignals: 0,
        errorRate: 0,
        avgExecutionTime: 0
      }
    }

    return {
      totalExecutions: execution.executionCount,
      totalSignals: execution.signals.length,
      errorRate: execution.errors.length / Math.max(execution.executionCount, 1),
      avgExecutionTime: 0, // TODO: 实现执行时间统计
      lastError: execution.errors[execution.errors.length - 1]
    }
  }

  // 私有辅助方法
  private generateStrategyId(): string {
    return `strategy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateExecutionId(): string {
    return `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 数据库操作方法（简化实现）
  private async saveStrategy(strategy: StrategyConfig): Promise<void> {
    // TODO: 实现数据库保存逻辑
    console.log(`Saving strategy ${strategy.id} to database`)
  }

  private async loadStrategy(id: string): Promise<StrategyConfig | null> {
    // TODO: 实现数据库加载逻辑
    console.log(`Loading strategy ${id} from database`)
    return null
  }

  private async loadAllStrategies(): Promise<StrategyConfig[]> {
    // TODO: 实现数据库加载逻辑
    console.log(`Loading all strategies from database`)
    return []
  }

  private async removeStrategy(id: string): Promise<void> {
    // TODO: 实现数据库删除逻辑
    console.log(`Removing strategy ${id} from database`)
  }
}

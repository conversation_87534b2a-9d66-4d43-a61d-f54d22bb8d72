import { EventEmitter } from 'events'
import { RedisService } from './RedisService'

export interface SystemMetrics {
  timestamp: number
  cpu: {
    usage: number
    load: number[]
  }
  memory: {
    used: number
    total: number
    usage: number
  }
  network: {
    bytesIn: number
    bytesOut: number
    connectionsActive: number
  }
  database: {
    connections: number
    queryTime: number
    slowQueries: number
  }
  api: {
    requestsPerSecond: number
    averageResponseTime: number
    errorRate: number
    activeConnections: number
  }
  websocket: {
    connections: number
    messagesPerSecond: number
    latency: number
  }
}

export interface PerformanceAlert {
  id: string
  type: 'CPU_HIGH' | 'MEMORY_HIGH' | 'RESPONSE_SLOW' | 'ERROR_RATE_HIGH' | 'DB_SLOW' | 'WS_LATENCY_HIGH'
  severity: 'WARNING' | 'CRITICAL'
  message: string
  value: number
  threshold: number
  timestamp: number
  resolved?: boolean
  resolvedAt?: number
}

export interface PerformanceReport {
  period: string
  startTime: number
  endTime: number
  summary: {
    avgCpuUsage: number
    avgMemoryUsage: number
    avgResponseTime: number
    totalRequests: number
    errorRate: number
    uptime: number
  }
  trends: {
    cpuTrend: 'INCREASING' | 'DECREASING' | 'STABLE'
    memoryTrend: 'INCREASING' | 'DECREASING' | 'STABLE'
    responseTrend: 'INCREASING' | 'DECREASING' | 'STABLE'
  }
  recommendations: string[]
}

export class PerformanceTrackingService extends EventEmitter {
  private metrics: SystemMetrics[] = []
  private alerts: PerformanceAlert[] = []
  private monitoringInterval: NodeJS.Timeout | null = null
  private readonly METRICS_LIMIT = 1440 // 24小时的分钟数据
  private readonly ALERT_LIMIT = 1000

  // 性能阈值配置
  private readonly thresholds = {
    cpuUsage: 80,
    memoryUsage: 85,
    responseTime: 1000, // ms
    errorRate: 5, // %
    dbQueryTime: 500, // ms
    wsLatency: 100 // ms
  }

  private startTime = Date.now()
  private requestCount = 0
  private errorCount = 0
  private responseTimes: number[] = []

  constructor(private redis: RedisService) {
    super()
    this.startMonitoring()
  }

  // 开始性能监控
  private startMonitoring(): void {
    console.log('📊 启动性能指标追踪服务')

    // 每分钟收集一次指标
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics()
    }, 60000)

    // 立即收集一次
    setTimeout(() => {
      this.collectMetrics()
    }, 1000) // 延迟1秒启动
  }

  // 停止监控
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
    console.log('⏹️ 性能监控服务已停止')
  }

  // 收集系统指标
  private async collectMetrics(): Promise<void> {
    try {
      const metrics: SystemMetrics = {
        timestamp: Date.now(),
        cpu: await this.getCpuMetrics(),
        memory: await this.getMemoryMetrics(),
        network: await this.getNetworkMetrics(),
        database: await this.getDatabaseMetrics(),
        api: this.getApiMetrics(),
        websocket: await this.getWebSocketMetrics()
      }

      this.metrics.push(metrics)

      // 保持指标数量限制
      if (this.metrics.length > this.METRICS_LIMIT) {
        this.metrics.shift()
      }

      // 检查性能阈值
      this.checkThresholds(metrics)

      // 缓存到Redis
      await this.redis.set('system_metrics:latest', JSON.stringify(metrics), 300) // 5分钟过期

      console.log(`📈 收集性能指标: CPU ${metrics.cpu.usage.toFixed(1)}%, 内存 ${metrics.memory.usage.toFixed(1)}%, API响应 ${metrics.api.averageResponseTime.toFixed(0)}ms`)

    } catch (error) {
      console.error('收集性能指标失败:', error)
    }
  }

  // 获取CPU指标
  private async getCpuMetrics(): Promise<{ usage: number; load: number[] }> {
    // 在实际环境中，这里会使用系统API获取真实的CPU数据
    // 这里提供模拟数据
    const usage = Math.random() * 30 + 20 // 20-50%的CPU使用率
    const load = [
      Math.random() * 2,
      Math.random() * 2,
      Math.random() * 2
    ]

    return { usage, load }
  }

  // 获取内存指标
  private async getMemoryMetrics(): Promise<{ used: number; total: number; usage: number }> {
    // 模拟内存数据
    const total = 8 * 1024 * 1024 * 1024 // 8GB
    const used = total * (0.3 + Math.random() * 0.3) // 30-60%使用率
    const usage = (used / total) * 100

    return { used, total, usage }
  }

  // 获取网络指标
  private async getNetworkMetrics(): Promise<{ bytesIn: number; bytesOut: number; connectionsActive: number }> {
    return {
      bytesIn: Math.random() * 1000000,
      bytesOut: Math.random() * 1000000,
      connectionsActive: Math.floor(Math.random() * 100) + 10
    }
  }

  // 获取数据库指标
  private async getDatabaseMetrics(): Promise<{ connections: number; queryTime: number; slowQueries: number }> {
    return {
      connections: Math.floor(Math.random() * 20) + 5,
      queryTime: Math.random() * 200 + 50, // 50-250ms
      slowQueries: Math.floor(Math.random() * 5)
    }
  }

  // 获取API指标
  private getApiMetrics(): { requestsPerSecond: number; averageResponseTime: number; errorRate: number; activeConnections: number } {
    const now = Date.now()
    const timeElapsed = (now - this.startTime) / 1000
    const requestsPerSecond = timeElapsed > 0 ? this.requestCount / timeElapsed : 0
    const averageResponseTime = this.responseTimes.length > 0
      ? this.responseTimes.reduce((a, b) => a + b, 0) / this.responseTimes.length
      : 0
    const errorRate = this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0

    return {
      requestsPerSecond,
      averageResponseTime,
      errorRate,
      activeConnections: Math.floor(Math.random() * 50) + 10
    }
  }

  // 获取WebSocket指标
  private async getWebSocketMetrics(): Promise<{ connections: number; messagesPerSecond: number; latency: number }> {
    return {
      connections: Math.floor(Math.random() * 30) + 5,
      messagesPerSecond: Math.random() * 100 + 10,
      latency: Math.random() * 50 + 20 // 20-70ms
    }
  }

  // 检查性能阈值
  private checkThresholds(metrics: SystemMetrics): void {
    const alerts: PerformanceAlert[] = []

    // CPU使用率检查
    if (metrics.cpu.usage > this.thresholds.cpuUsage) {
      alerts.push({
        id: `alert_${Date.now()}_cpu`,
        type: 'CPU_HIGH',
        severity: metrics.cpu.usage > 90 ? 'CRITICAL' : 'WARNING',
        message: `CPU使用率过高: ${metrics.cpu.usage.toFixed(1)}%`,
        value: metrics.cpu.usage,
        threshold: this.thresholds.cpuUsage,
        timestamp: metrics.timestamp
      })
    }

    // 内存使用率检查
    if (metrics.memory.usage > this.thresholds.memoryUsage) {
      alerts.push({
        id: `alert_${Date.now()}_memory`,
        type: 'MEMORY_HIGH',
        severity: metrics.memory.usage > 95 ? 'CRITICAL' : 'WARNING',
        message: `内存使用率过高: ${metrics.memory.usage.toFixed(1)}%`,
        value: metrics.memory.usage,
        threshold: this.thresholds.memoryUsage,
        timestamp: metrics.timestamp
      })
    }

    // API响应时间检查
    if (metrics.api.averageResponseTime > this.thresholds.responseTime) {
      alerts.push({
        id: `alert_${Date.now()}_response`,
        type: 'RESPONSE_SLOW',
        severity: metrics.api.averageResponseTime > 2000 ? 'CRITICAL' : 'WARNING',
        message: `API响应时间过慢: ${metrics.api.averageResponseTime.toFixed(0)}ms`,
        value: metrics.api.averageResponseTime,
        threshold: this.thresholds.responseTime,
        timestamp: metrics.timestamp
      })
    }

    // 错误率检查
    if (metrics.api.errorRate > this.thresholds.errorRate) {
      alerts.push({
        id: `alert_${Date.now()}_error`,
        type: 'ERROR_RATE_HIGH',
        severity: metrics.api.errorRate > 10 ? 'CRITICAL' : 'WARNING',
        message: `API错误率过高: ${metrics.api.errorRate.toFixed(1)}%`,
        value: metrics.api.errorRate,
        threshold: this.thresholds.errorRate,
        timestamp: metrics.timestamp
      })
    }

    // 数据库查询时间检查
    if (metrics.database.queryTime > this.thresholds.dbQueryTime) {
      alerts.push({
        id: `alert_${Date.now()}_db`,
        type: 'DB_SLOW',
        severity: metrics.database.queryTime > 1000 ? 'CRITICAL' : 'WARNING',
        message: `数据库查询过慢: ${metrics.database.queryTime.toFixed(0)}ms`,
        value: metrics.database.queryTime,
        threshold: this.thresholds.dbQueryTime,
        timestamp: metrics.timestamp
      })
    }

    // WebSocket延迟检查
    if (metrics.websocket.latency > this.thresholds.wsLatency) {
      alerts.push({
        id: `alert_${Date.now()}_ws`,
        type: 'WS_LATENCY_HIGH',
        severity: metrics.websocket.latency > 200 ? 'CRITICAL' : 'WARNING',
        message: `WebSocket延迟过高: ${metrics.websocket.latency.toFixed(0)}ms`,
        value: metrics.websocket.latency,
        threshold: this.thresholds.wsLatency,
        timestamp: metrics.timestamp
      })
    }

    // 添加新告警
    for (const alert of alerts) {
      this.alerts.push(alert)
      console.log(`🚨 性能告警: ${alert.message}`)
      this.emit('performance_alert', alert)
    }

    // 保持告警数量限制
    if (this.alerts.length > this.ALERT_LIMIT) {
      this.alerts = this.alerts.slice(-this.ALERT_LIMIT)
    }
  }

  // 记录API请求
  recordApiRequest(responseTime: number, isError: boolean = false): void {
    this.requestCount++
    this.responseTimes.push(responseTime)

    if (isError) {
      this.errorCount++
    }

    // 保持响应时间数组大小
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000)
    }
  }

  // 获取最新指标
  getLatestMetrics(): SystemMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null
  }

  // 获取历史指标
  getHistoricalMetrics(hours: number = 24): SystemMetrics[] {
    const cutoff = Date.now() - (hours * 60 * 60 * 1000)
    return this.metrics.filter(m => m.timestamp > cutoff)
  }

  // 获取告警
  getAlerts(limit: number = 50): PerformanceAlert[] {
    return this.alerts.slice(-limit).reverse()
  }

  // 生成性能报告
  generateReport(hours: number = 24): PerformanceReport {
    const endTime = Date.now()
    const startTime = endTime - (hours * 60 * 60 * 1000)
    const periodMetrics = this.metrics.filter(m => m.timestamp >= startTime && m.timestamp <= endTime)

    if (periodMetrics.length === 0) {
      throw new Error('指定时间段内没有性能数据')
    }

    // 计算平均值
    const avgCpuUsage = periodMetrics.reduce((sum, m) => sum + m.cpu.usage, 0) / periodMetrics.length
    const avgMemoryUsage = periodMetrics.reduce((sum, m) => sum + m.memory.usage, 0) / periodMetrics.length
    const avgResponseTime = periodMetrics.reduce((sum, m) => sum + m.api.averageResponseTime, 0) / periodMetrics.length
    const totalRequests = periodMetrics.reduce((sum, m) => sum + m.api.requestsPerSecond, 0) * hours * 3600
    const errorRate = periodMetrics.reduce((sum, m) => sum + m.api.errorRate, 0) / periodMetrics.length

    // 计算趋势
    const firstHalf = periodMetrics.slice(0, Math.floor(periodMetrics.length / 2))
    const secondHalf = periodMetrics.slice(Math.floor(periodMetrics.length / 2))

    const cpuTrend = this.calculateTrend(
      firstHalf.reduce((sum, m) => sum + m.cpu.usage, 0) / firstHalf.length,
      secondHalf.reduce((sum, m) => sum + m.cpu.usage, 0) / secondHalf.length
    )

    const memoryTrend = this.calculateTrend(
      firstHalf.reduce((sum, m) => sum + m.memory.usage, 0) / firstHalf.length,
      secondHalf.reduce((sum, m) => sum + m.memory.usage, 0) / secondHalf.length
    )

    const responseTrend = this.calculateTrend(
      firstHalf.reduce((sum, m) => sum + m.api.averageResponseTime, 0) / firstHalf.length,
      secondHalf.reduce((sum, m) => sum + m.api.averageResponseTime, 0) / secondHalf.length
    )

    // 生成建议
    const recommendations: string[] = []
    if (avgCpuUsage > 70) {
      recommendations.push('考虑优化CPU密集型操作或增加服务器资源')
    }
    if (avgMemoryUsage > 80) {
      recommendations.push('检查内存泄漏或考虑增加内存容量')
    }
    if (avgResponseTime > 500) {
      recommendations.push('优化API响应时间，检查数据库查询和网络延迟')
    }
    if (errorRate > 2) {
      recommendations.push('调查并修复导致高错误率的问题')
    }

    return {
      period: `${hours}小时`,
      startTime,
      endTime,
      summary: {
        avgCpuUsage,
        avgMemoryUsage,
        avgResponseTime,
        totalRequests,
        errorRate,
        uptime: ((endTime - this.startTime) / 1000 / 3600).toFixed(1) as any
      },
      trends: {
        cpuTrend,
        memoryTrend,
        responseTrend
      },
      recommendations
    }
  }

  // 计算趋势
  private calculateTrend(first: number, second: number): 'INCREASING' | 'DECREASING' | 'STABLE' {
    const change = ((second - first) / first) * 100
    if (change > 5) return 'INCREASING'
    if (change < -5) return 'DECREASING'
    return 'STABLE'
  }

  // 获取系统健康状态
  getHealthStatus(): {
    status: 'HEALTHY' | 'WARNING' | 'CRITICAL'
    score: number
    issues: string[]
  } {
    const latest = this.getLatestMetrics()
    if (!latest) {
      return { status: 'CRITICAL', score: 0, issues: ['无法获取系统指标'] }
    }

    const issues: string[] = []
    let score = 100

    // CPU检查
    if (latest.cpu.usage > 90) {
      issues.push('CPU使用率过高')
      score -= 30
    } else if (latest.cpu.usage > 70) {
      issues.push('CPU使用率较高')
      score -= 15
    }

    // 内存检查
    if (latest.memory.usage > 95) {
      issues.push('内存使用率过高')
      score -= 30
    } else if (latest.memory.usage > 80) {
      issues.push('内存使用率较高')
      score -= 15
    }

    // API响应时间检查
    if (latest.api.averageResponseTime > 2000) {
      issues.push('API响应时间过慢')
      score -= 25
    } else if (latest.api.averageResponseTime > 1000) {
      issues.push('API响应时间较慢')
      score -= 10
    }

    // 错误率检查
    if (latest.api.errorRate > 10) {
      issues.push('API错误率过高')
      score -= 25
    } else if (latest.api.errorRate > 5) {
      issues.push('API错误率较高')
      score -= 10
    }

    let status: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY'
    if (score < 50) {
      status = 'CRITICAL'
    } else if (score < 80) {
      status = 'WARNING'
    }

    return { status, score: Math.max(0, score), issues }
  }
}

import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { PrismaClient } from '@prisma/client'
import { FastifyRequest } from 'fastify'

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  name?: string
  username?: string
}

export interface AuthUser {
  id: string
  email: string
  username?: string
  name?: string
  role: string
  isActive: boolean
  isVerified: boolean
}

export interface AuthToken {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

export class AuthService {
  private prisma: PrismaClient
  private jwtSecret: string
  private jwtRefreshSecret: string
  private tokenExpiry: string
  private refreshTokenExpiry: string

  constructor(prisma: PrismaClient) {
    this.prisma = prisma
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key'
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key'
    this.tokenExpiry = process.env.JWT_EXPIRY || '1h'
    this.refreshTokenExpiry = process.env.JWT_REFRESH_EXPIRY || '7d'
  }

  // 用户注册
  async register(data: RegisterData): Promise<{ user: AuthUser; tokens: AuthToken }> {
    // 检查邮箱是否已存在
    const existingUser = await this.prisma.user.findUnique({
      where: { email: data.email }
    })

    if (existingUser) {
      throw new Error('邮箱已被注册')
    }

    // 检查用户名是否已存在
    if (data.username) {
      const existingUsername = await this.prisma.user.findUnique({
        where: { username: data.username }
      })

      if (existingUsername) {
        throw new Error('用户名已被使用')
      }
    }

    // 密码加密
    const hashedPassword = await bcrypt.hash(data.password, 12)

    // 创建用户
    const user = await this.prisma.user.create({
      data: {
        email: data.email,
        username: data.username,
        name: data.name,
        password: hashedPassword,
        settings: {
          create: {} // 创建默认设置
        }
      },
      include: {
        settings: true
      }
    })

    // 生成令牌
    const tokens = await this.generateTokens(user.id)

    // 创建会话
    await this.createSession(user.id, tokens.accessToken)

    return {
      user: this.formatUser(user),
      tokens
    }
  }

  // 用户登录
  async login(credentials: LoginCredentials, request?: FastifyRequest): Promise<{ user: AuthUser; tokens: AuthToken }> {
    // 查找用户
    const user = await this.prisma.user.findUnique({
      where: { email: credentials.email },
      include: { settings: true }
    })

    if (!user) {
      throw new Error('邮箱或密码错误')
    }

    // 检查账户状态
    if (!user.isActive) {
      throw new Error('账户已被禁用')
    }

    // 检查账户锁定状态
    if (user.lockedUntil && user.lockedUntil > new Date()) {
      throw new Error('账户已被锁定，请稍后再试')
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(credentials.password, user.password)

    if (!isPasswordValid) {
      // 增加登录失败次数
      await this.handleFailedLogin(user.id)
      throw new Error('邮箱或密码错误')
    }

    // 重置登录失败次数
    await this.resetLoginAttempts(user.id)

    // 更新最后登录时间
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    })

    // 生成令牌
    const tokens = await this.generateTokens(user.id)

    // 创建会话
    await this.createSession(user.id, tokens.accessToken, request)

    return {
      user: this.formatUser(user),
      tokens
    }
  }

  // 刷新令牌
  async refreshToken(refreshToken: string): Promise<AuthToken> {
    try {
      const decoded = jwt.verify(refreshToken, this.jwtRefreshSecret) as any
      const userId = decoded.userId

      // 验证用户是否存在且活跃
      const user = await this.prisma.user.findUnique({
        where: { id: userId }
      })

      if (!user || !user.isActive) {
        throw new Error('用户不存在或已被禁用')
      }

      // 生成新令牌
      return await this.generateTokens(userId)
    } catch (error) {
      throw new Error('刷新令牌无效')
    }
  }

  // 登出
  async logout(token: string): Promise<void> {
    await this.prisma.userSession.deleteMany({
      where: { token }
    })
  }

  // 验证令牌
  async verifyToken(token: string): Promise<AuthUser> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as any
      const userId = decoded.userId

      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        include: { settings: true }
      })

      if (!user || !user.isActive) {
        throw new Error('用户不存在或已被禁用')
      }

      return this.formatUser(user)
    } catch (error) {
      throw new Error('令牌无效')
    }
  }

  // 修改密码
  async changePassword(userId: string, oldPassword: string, newPassword: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      throw new Error('用户不存在')
    }

    // 验证旧密码
    const isOldPasswordValid = await bcrypt.compare(oldPassword, user.password)
    if (!isOldPasswordValid) {
      throw new Error('原密码错误')
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, 12)

    // 更新密码
    await this.prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword }
    })

    // 删除所有会话，强制重新登录
    await this.prisma.userSession.deleteMany({
      where: { userId }
    })
  }

  // 生成令牌
  private async generateTokens(userId: string): Promise<AuthToken> {
    const accessToken = jwt.sign(
      { userId, type: 'access' },
      this.jwtSecret,
      { expiresIn: this.tokenExpiry }
    )

    const refreshToken = jwt.sign(
      { userId, type: 'refresh' },
      this.jwtRefreshSecret,
      { expiresIn: this.refreshTokenExpiry }
    )

    return {
      accessToken,
      refreshToken,
      expiresIn: this.parseExpiry(this.tokenExpiry)
    }
  }

  // 创建会话
  private async createSession(userId: string, token: string, request?: FastifyRequest): Promise<void> {
    const expiresAt = new Date()
    expiresAt.setTime(expiresAt.getTime() + this.parseExpiry(this.tokenExpiry) * 1000)

    await this.prisma.userSession.create({
      data: {
        userId,
        token,
        userAgent: request?.headers['user-agent'],
        ipAddress: request?.ip,
        expiresAt
      }
    })
  }

  // 处理登录失败
  private async handleFailedLogin(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) return

    const newAttempts = user.loginAttempts + 1
    const updateData: any = { loginAttempts: newAttempts }

    // 如果失败次数达到5次，锁定账户30分钟
    if (newAttempts >= 5) {
      const lockUntil = new Date()
      lockUntil.setMinutes(lockUntil.getMinutes() + 30)
      updateData.lockedUntil = lockUntil
    }

    await this.prisma.user.update({
      where: { id: userId },
      data: updateData
    })
  }

  // 重置登录失败次数
  private async resetLoginAttempts(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        loginAttempts: 0,
        lockedUntil: null
      }
    })
  }

  // 格式化用户信息
  private formatUser(user: any): AuthUser {
    return {
      id: user.id,
      email: user.email,
      username: user.username,
      name: user.name,
      role: user.role,
      isActive: user.isActive,
      isVerified: user.isVerified
    }
  }

  // 解析过期时间
  private parseExpiry(expiry: string): number {
    const unit = expiry.slice(-1)
    const value = parseInt(expiry.slice(0, -1))

    switch (unit) {
      case 's': return value
      case 'm': return value * 60
      case 'h': return value * 60 * 60
      case 'd': return value * 60 * 60 * 24
      default: return 3600 // 默认1小时
    }
  }
}

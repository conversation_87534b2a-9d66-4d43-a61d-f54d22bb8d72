import { MarketData, OrderBook } from '@sfquant/core'
import { RedisService } from './RedisService'
import { ExchangeService } from './ExchangeService'
import { config } from '../config'
import axios from 'axios'
import WebSocket from 'ws'

export interface PriceAlert {
  id: string
  symbol: string
  condition: 'above' | 'below'
  price: number
  userId: string
  active: boolean
  createdAt: number
}

export class MarketDataService {
  private priceUpdateIntervals: Map<string, NodeJS.Timeout> = new Map()
  private subscribedSymbols: Set<string> = new Set()
  private priceAlerts: Map<string, PriceAlert> = new Map()
  private exchangeSubscriptions: Map<string, Map<string, any>> = new Map() // exchangeId -> symbol -> subscription
  private exchangeService?: ExchangeService
  private binanceWs: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectInterval = 5000
  private wsSubscriptions: Set<string> = new Set()

  constructor(private redis: RedisService, exchangeService?: ExchangeService) {
    this.exchangeService = exchangeService
  }

  async initialize(): Promise<void> {
    console.log('Initializing Market Data Service...')

    // 初始化Binance WebSocket连接
    await this.initializeBinanceWebSocket()

    // 启动常用交易对的价格更新
    const commonSymbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT']

    for (const symbol of commonSymbols) {
      await this.subscribeToSymbol(symbol)
    }

    console.log('Market Data Service initialized successfully')
  }

  // 订阅交易对价格更新
  async subscribeToSymbol(symbol: string): Promise<void> {
    if (this.subscribedSymbols.has(symbol)) {
      return
    }

    this.subscribedSymbols.add(symbol)

    // 尝试使用交易所的实时订阅功能
    if (this.exchangeService) {
      await this.subscribeToExchangeRealTimeData(symbol)
    }

    // 启动定期价格更新作为备用
    const interval = setInterval(async () => {
      await this.updateMarketData(symbol)
    }, config.PRICE_UPDATE_INTERVAL)

    this.priceUpdateIntervals.set(symbol, interval)

    // 立即获取一次价格
    await this.updateMarketData(symbol)

    console.log(`Subscribed to ${symbol} price updates`)
  }

  // 取消订阅交易对
  async unsubscribeFromSymbol(symbol: string): Promise<void> {
    if (!this.subscribedSymbols.has(symbol)) {
      return
    }

    this.subscribedSymbols.delete(symbol)

    // 取消交易所实时订阅
    if (this.exchangeService) {
      await this.unsubscribeFromExchangeRealTimeData(symbol)
    }

    const interval = this.priceUpdateIntervals.get(symbol)
    if (interval) {
      clearInterval(interval)
      this.priceUpdateIntervals.delete(symbol)
    }

    console.log(`Unsubscribed from ${symbol} price updates`)
  }

  // 获取市场数据
  async getMarketData(symbol: string): Promise<MarketData | null> {
    // 先从缓存获取
    const cached = await this.redis.getCachedMarketData(symbol)
    if (cached) {
      return cached
    }

    // 如果缓存中没有，立即获取并缓存
    await this.updateMarketData(symbol)
    return await this.redis.getCachedMarketData(symbol)
  }

  // 获取订单簿数据
  async getOrderBook(symbol: string): Promise<OrderBook | null> {
    // 先从缓存获取
    const cached = await this.redis.getCachedOrderBook(symbol)
    if (cached) {
      return cached
    }

    // 如果缓存中没有，立即获取并缓存
    await this.updateOrderBook(symbol)
    return await this.redis.getCachedOrderBook(symbol)
  }

  // 获取多个交易对的价格
  async getMultipleMarketData(symbols: string[]): Promise<Record<string, MarketData>> {
    const results: Record<string, MarketData> = {}

    const promises = symbols.map(async (symbol) => {
      const data = await this.getMarketData(symbol)
      if (data) {
        results[symbol] = data
      }
    })

    await Promise.all(promises)
    return results
  }

  // 获取价格历史数据
  async getPriceHistory(symbol: string, timeframe: string, limit: number = 100): Promise<any[]> {
    try {
      // 转换交易对格式 (BTC/USDT -> BTCUSDT)
      const binanceSymbol = symbol.replace('/', '')

      // 转换时间框架格式
      const intervalMap: Record<string, string> = {
        '1m': '1m',
        '5m': '5m',
        '15m': '15m',
        '30m': '30m',
        '1h': '1h',
        '4h': '4h',
        '1d': '1d',
        '1w': '1w'
      }

      const interval = intervalMap[timeframe] || '1h'

      // 获取K线数据
      const response = await axios.get(`https://api.binance.com/api/v3/klines`, {
        params: {
          symbol: binanceSymbol,
          interval: interval,
          limit: Math.min(limit, 1000) // Binance限制最多1000条
        },
        timeout: 10000
      })

      return response.data.map((kline: any[]) => ({
        timestamp: kline[0], // 开盘时间
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5]),
        closeTime: kline[6],
        quoteVolume: parseFloat(kline[7]),
        trades: kline[8],
        buyBaseVolume: parseFloat(kline[9]),
        buyQuoteVolume: parseFloat(kline[10])
      }))
    } catch (error: any) {
      if (error.response?.status === 400) {
        console.warn(`Symbol ${symbol} not found on Binance`)
      } else {
        console.error(`Failed to get price history for ${symbol}:`, error.message)
      }
      return []
    }
  }

  // 创建价格提醒
  async createPriceAlert(alert: Omit<PriceAlert, 'id' | 'createdAt'>): Promise<PriceAlert> {
    const priceAlert: PriceAlert = {
      ...alert,
      id: this.generateAlertId(),
      createdAt: Date.now()
    }

    this.priceAlerts.set(priceAlert.id, priceAlert)

    // 确保订阅了该交易对
    await this.subscribeToSymbol(alert.symbol)

    console.log(`Created price alert for ${alert.symbol} ${alert.condition} ${alert.price}`)
    return priceAlert
  }

  // 删除价格提醒
  async deletePriceAlert(alertId: string): Promise<void> {
    this.priceAlerts.delete(alertId)
    console.log(`Deleted price alert ${alertId}`)
  }

  // 获取用户的价格提醒
  getUserPriceAlerts(userId: string): PriceAlert[] {
    return Array.from(this.priceAlerts.values()).filter(alert => alert.userId === userId)
  }

  // 检查价格提醒
  private async checkPriceAlerts(symbol: string, currentPrice: number): Promise<void> {
    const alerts = Array.from(this.priceAlerts.values()).filter(
      alert => alert.symbol === symbol && alert.active
    )

    for (const alert of alerts) {
      let triggered = false

      if (alert.condition === 'above' && currentPrice >= alert.price) {
        triggered = true
      } else if (alert.condition === 'below' && currentPrice <= alert.price) {
        triggered = true
      }

      if (triggered) {
        await this.triggerPriceAlert(alert, currentPrice)
      }
    }
  }

  // 触发价格提醒
  private async triggerPriceAlert(alert: PriceAlert, currentPrice: number): Promise<void> {
    console.log(`Price alert triggered: ${alert.symbol} is ${alert.condition} ${alert.price}, current: ${currentPrice}`)

    // 禁用提醒（避免重复触发）
    alert.active = false

    // 这里应该发送通知给用户
    // 可以通过WebSocket、邮件、短信等方式
    await this.sendAlertNotification(alert, currentPrice)
  }

  // 发送提醒通知
  private async sendAlertNotification(alert: PriceAlert, currentPrice: number): Promise<void> {
    // 通过Redis发布消息，WebSocket服务可以订阅并推送给客户端
    const notification = {
      type: 'price_alert',
      userId: alert.userId,
      message: `${alert.symbol} price is ${alert.condition} ${alert.price}. Current price: ${currentPrice}`,
      timestamp: Date.now()
    }

    await this.redis.publish('notifications', JSON.stringify(notification))
  }

  // 更新市场数据
  private async updateMarketData(symbol: string): Promise<void> {
    try {
      // 这里应该从交易所API获取实时数据
      // 简化实现，使用模拟数据
      const marketData = await this.fetchMarketDataFromAPI(symbol)

      if (marketData) {
        // 缓存数据
        await this.redis.cacheMarketData(symbol, marketData, config.MARKET_DATA_CACHE_TTL)

        // 检查价格提醒
        await this.checkPriceAlerts(symbol, marketData.last)

        // 发布价格更新事件
        await this.redis.publish('price_updates', JSON.stringify({
          symbol,
          data: marketData,
          timestamp: Date.now()
        }))
      }
    } catch (error) {
      console.error(`Failed to update market data for ${symbol}:`, error)
    }
  }

  // 更新订单簿数据
  private async updateOrderBook(symbol: string): Promise<void> {
    try {
      const orderBook = await this.fetchOrderBookFromAPI(symbol)

      if (orderBook) {
        await this.redis.cacheOrderBook(symbol, orderBook, 30) // 30秒缓存

        // 发布订单簿更新事件
        await this.redis.publish('orderbook_updates', JSON.stringify({
          symbol,
          data: orderBook,
          timestamp: Date.now()
        }))
      }
    } catch (error) {
      console.error(`Failed to update order book for ${symbol}:`, error)
    }
  }

  // 从Binance API获取真实市场数据
  private async fetchMarketDataFromAPI(symbol: string): Promise<MarketData | null> {
    try {
      // 转换交易对格式 (BTC/USDT -> BTCUSDT)
      const binanceSymbol = symbol.replace('/', '')

      // 获取24小时价格统计
      const tickerResponse = await axios.get(`https://api.binance.com/api/v3/ticker/24hr`, {
        params: { symbol: binanceSymbol },
        timeout: 5000
      })

      // 获取当前最优价格
      const bookTickerResponse = await axios.get(`https://api.binance.com/api/v3/ticker/bookTicker`, {
        params: { symbol: binanceSymbol },
        timeout: 5000
      })

      const ticker = tickerResponse.data
      const bookTicker = bookTickerResponse.data

      return {
        symbol,
        timestamp: Date.now(),
        bid: parseFloat(bookTicker.bidPrice),
        ask: parseFloat(bookTicker.askPrice),
        last: parseFloat(ticker.lastPrice),
        volume: parseFloat(ticker.volume),
        high: parseFloat(ticker.highPrice),
        low: parseFloat(ticker.lowPrice),
        open: parseFloat(ticker.openPrice),
        close: parseFloat(ticker.lastPrice),
        change: parseFloat(ticker.priceChange),
        changePercent: parseFloat(ticker.priceChangePercent)
      }
    } catch (error: any) {
      if (error.response?.status === 400) {
        console.warn(`Symbol ${symbol} not found on Binance`)
      } else {
        console.error(`Failed to fetch market data for ${symbol}:`, error.message)
      }
      return null
    }
  }

  // 从Binance API获取真实订单簿数据
  private async fetchOrderBookFromAPI(symbol: string): Promise<OrderBook | null> {
    try {
      // 转换交易对格式 (BTC/USDT -> BTCUSDT)
      const binanceSymbol = symbol.replace('/', '')

      // 获取订单簿数据
      const response = await axios.get(`https://api.binance.com/api/v3/depth`, {
        params: {
          symbol: binanceSymbol,
          limit: 20 // 获取前20档
        },
        timeout: 5000
      })

      const data = response.data

      // 转换数据格式
      const bids = data.bids.map((bid: string[]) => ({
        price: parseFloat(bid[0]),
        amount: parseFloat(bid[1])
      }))

      const asks = data.asks.map((ask: string[]) => ({
        price: parseFloat(ask[0]),
        amount: parseFloat(ask[1])
      }))

      return {
        symbol,
        timestamp: Date.now(),
        bids,
        asks
      }
    } catch (error: any) {
      if (error.response?.status === 400) {
        console.warn(`Symbol ${symbol} not found on Binance`)
      } else {
        console.error(`Failed to fetch order book for ${symbol}:`, error.message)
      }
      return null
    }
  }

  // 获取市场统计信息
  async getMarketStats(): Promise<{
    subscribedSymbols: number
    activeAlerts: number
    cacheHitRate: number
  }> {
    return {
      subscribedSymbols: this.subscribedSymbols.size,
      activeAlerts: Array.from(this.priceAlerts.values()).filter(alert => alert.active).length,
      cacheHitRate: 0.85 // 模拟缓存命中率
    }
  }

  // 清理资源
  async cleanup(): Promise<void> {
    // 清除所有定时器
    for (const interval of this.priceUpdateIntervals.values()) {
      clearInterval(interval)
    }

    this.priceUpdateIntervals.clear()
    this.subscribedSymbols.clear()
    this.priceAlerts.clear()

    console.log('Market Data Service cleaned up')
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 订阅交易所实时数据
  private async subscribeToExchangeRealTimeData(symbol: string): Promise<void> {
    if (!this.exchangeService) return

    try {
      const exchanges = this.exchangeService.getAllExchanges()

      for (const [exchangeId, exchange] of exchanges) {
        if (!this.exchangeSubscriptions.has(exchangeId)) {
          this.exchangeSubscriptions.set(exchangeId, new Map())
        }

        const exchangeSubs = this.exchangeSubscriptions.get(exchangeId)!

        // 订阅价格数据
        try {
          await exchange.subscribeToTicker(symbol, (marketData: any) => {
            this.handleRealTimeMarketData(symbol, marketData, exchangeId)
          })

          // 订阅订单簿数据
          await exchange.subscribeToOrderBook(symbol, (orderBook: any) => {
            this.handleRealTimeOrderBook(symbol, orderBook, exchangeId)
          })

          // 订阅交易数据
          await exchange.subscribeToTrades(symbol, (trade: any) => {
            this.handleRealTimeTrade(symbol, trade, exchangeId)
          })

          exchangeSubs.set(symbol, { ticker: true, orderbook: true, trades: true })
          console.log(`Subscribed to real-time data for ${symbol} on ${exchangeId}`)

        } catch (error) {
          console.warn(`Failed to subscribe to real-time data for ${symbol} on ${exchangeId}:`, error)
        }
      }
    } catch (error) {
      console.error(`Error setting up real-time subscriptions for ${symbol}:`, error)
    }
  }

  // 处理实时市场数据
  private async handleRealTimeMarketData(symbol: string, marketData: any, exchangeId: string): Promise<void> {
    try {
      // 缓存数据
      await this.redis.cacheMarketData(symbol, marketData, config.MARKET_DATA_CACHE_TTL)

      // 检查价格提醒
      await this.checkPriceAlerts(symbol, marketData.last)

      // 发布价格更新事件
      await this.redis.publish('price_updates', JSON.stringify({
        symbol,
        data: marketData,
        exchangeId,
        timestamp: Date.now()
      }))

      console.log(`Real-time price update for ${symbol} from ${exchangeId}: ${marketData.last}`)
    } catch (error) {
      console.error(`Error handling real-time market data for ${symbol}:`, error)
    }
  }

  // 处理实时订单簿数据
  private async handleRealTimeOrderBook(symbol: string, orderBook: any, exchangeId: string): Promise<void> {
    try {
      // 缓存订单簿数据
      await this.redis.cacheOrderBook(symbol, orderBook, 30)

      // 发布订单簿更新事件
      await this.redis.publish('orderbook_updates', JSON.stringify({
        symbol,
        data: orderBook,
        exchangeId,
        timestamp: Date.now()
      }))

      console.log(`Real-time orderbook update for ${symbol} from ${exchangeId}`)
    } catch (error) {
      console.error(`Error handling real-time order book for ${symbol}:`, error)
    }
  }

  // 处理实时交易数据
  private async handleRealTimeTrade(symbol: string, trade: any, exchangeId: string): Promise<void> {
    try {
      // 发布交易更新事件
      await this.redis.publish('trade_updates', JSON.stringify({
        symbol,
        data: trade,
        exchangeId,
        timestamp: Date.now()
      }))

      console.log(`Real-time trade for ${symbol} from ${exchangeId}: ${trade.price} x ${trade.amount}`)
    } catch (error) {
      console.error(`Error handling real-time trade for ${symbol}:`, error)
    }
  }

  // 取消交易所实时数据订阅
  private async unsubscribeFromExchangeRealTimeData(symbol: string): Promise<void> {
    if (!this.exchangeService) return

    try {
      const exchanges = this.exchangeService.getAllExchanges()

      for (const [exchangeId, exchange] of exchanges) {
        const exchangeSubs = this.exchangeSubscriptions.get(exchangeId)
        if (exchangeSubs && exchangeSubs.has(symbol)) {
          try {
            await exchange.unsubscribe(symbol, 'ticker')
            await exchange.unsubscribe(symbol, 'orderbook')
            await exchange.unsubscribe(symbol, 'trades')

            exchangeSubs.delete(symbol)
            console.log(`Unsubscribed from real-time data for ${symbol} on ${exchangeId}`)
          } catch (error) {
            console.warn(`Failed to unsubscribe from real-time data for ${symbol} on ${exchangeId}:`, error)
          }
        }
      }
    } catch (error) {
      console.error(`Error unsubscribing from real-time data for ${symbol}:`, error)
    }
  }

  // 初始化Binance WebSocket连接
  private async initializeBinanceWebSocket(): Promise<void> {
    try {
      const wsUrl = 'wss://stream.binance.com:9443/ws/btcusdt@ticker/ethusdt@ticker/bnbusdt@ticker/adausdt@ticker/solusdt@ticker'

      this.binanceWs = new WebSocket(wsUrl)

      this.binanceWs.on('open', () => {
        console.log('✅ Binance WebSocket connected')
        this.reconnectAttempts = 0
      })

      this.binanceWs.on('message', (data: Buffer) => {
        try {
          const message = JSON.parse(data.toString())
          this.handleBinanceMessage(message)
        } catch (error) {
          console.error('Error parsing Binance WebSocket message:', error)
        }
      })

      this.binanceWs.on('close', (code: number, reason: Buffer) => {
        console.warn(`Binance WebSocket closed: ${code} ${reason.toString()}`)
        this.scheduleReconnect()
      })

      this.binanceWs.on('error', (error: Error) => {
        console.error('Binance WebSocket error:', error)
        this.scheduleReconnect()
      })

      // 设置心跳
      this.setupHeartbeat()

    } catch (error) {
      console.error('Failed to initialize Binance WebSocket:', error)
      this.scheduleReconnect()
    }
  }

  // 处理Binance WebSocket消息
  private async handleBinanceMessage(message: any): Promise<void> {
    try {
      if (message.e === '24hrTicker') {
        const symbol = this.formatSymbol(message.s) // BTCUSDT -> BTC/USDT

        const marketData: MarketData = {
          symbol,
          timestamp: Date.now(),
          bid: parseFloat(message.b),
          ask: parseFloat(message.a),
          last: parseFloat(message.c),
          volume: parseFloat(message.v),
          high: parseFloat(message.h),
          low: parseFloat(message.l),
          open: parseFloat(message.o),
          close: parseFloat(message.c),
          change: parseFloat(message.P),
          changePercent: parseFloat(message.P)
        }

        // 缓存数据
        await this.redis.cacheMarketData(symbol, marketData, config.MARKET_DATA_CACHE_TTL)

        // 检查价格提醒
        await this.checkPriceAlerts(symbol, marketData.last)

        // 发布实时价格更新
        await this.redis.publish('price_updates', JSON.stringify({
          symbol,
          data: marketData,
          source: 'binance_ws',
          timestamp: Date.now()
        }))

        console.log(`📊 Real-time price: ${symbol} = $${marketData.last.toFixed(2)} (${marketData.changePercent >= 0 ? '+' : ''}${marketData.changePercent.toFixed(2)}%)`)
      }
    } catch (error) {
      console.error('Error handling Binance message:', error)
    }
  }

  // 格式化交易对符号
  private formatSymbol(binanceSymbol: string): string {
    // BTCUSDT -> BTC/USDT
    const symbolMap: Record<string, string> = {
      'BTCUSDT': 'BTC/USDT',
      'ETHUSDT': 'ETH/USDT',
      'BNBUSDT': 'BNB/USDT',
      'ADAUSDT': 'ADA/USDT',
      'SOLUSDT': 'SOL/USDT'
    }

    return symbolMap[binanceSymbol] || binanceSymbol
  }

  // 设置心跳
  private setupHeartbeat(): void {
    setInterval(() => {
      if (this.binanceWs && this.binanceWs.readyState === WebSocket.OPEN) {
        this.binanceWs.ping()
      }
    }, 30000) // 每30秒发送一次心跳
  }

  // 安排重连
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached for Binance WebSocket')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1) // 指数退避

    console.log(`Scheduling Binance WebSocket reconnection in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`)

    setTimeout(() => {
      this.initializeBinanceWebSocket()
    }, delay)
  }
}

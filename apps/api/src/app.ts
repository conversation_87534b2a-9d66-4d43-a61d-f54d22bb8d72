import Fastify, { FastifyInstance, FastifyServerOptions } from 'fastify'
import cors from '@fastify/cors'
import helmet from '@fastify/helmet'
import rateLimit from '@fastify/rate-limit'
import swagger from '@fastify/swagger'
import swaggerUi from '@fastify/swagger-ui'
import jwt from '@fastify/jwt'
import websocket from '@fastify/websocket'

import { config } from './config'
import { setupRoutes } from './routes'
import { setupWebSocket } from './websocket'
import { DatabaseService } from './services/DatabaseService'
import { RedisService } from './services/RedisService'
import { ExchangeService } from './services/ExchangeService'
import { MarketDataService } from './services/MarketDataService'
import { StrategyService } from './services/StrategyService'
import { PriceMonitorService } from './services/PriceMonitorService'
import { PerformanceTrackingService } from './services/PerformanceTrackingService'

export async function build(opts: FastifyServerOptions = {}): Promise<FastifyInstance> {
  const fastify = Fastify({
    logger: opts.logger !== false ? {
      level: config.LOG_LEVEL,
      transport: config.NODE_ENV === 'development' ? {
        target: 'pino-pretty',
        options: {
          colorize: true,
          translateTime: 'HH:MM:ss Z',
          ignore: 'pid,hostname'
        }
      } : undefined
    } : false,
    ...opts
  })

  // 注册插件
  await fastify.register(cors, {
    origin: config.CORS_ORIGIN,
    credentials: true
  })

  await fastify.register(helmet, {
    contentSecurityPolicy: false
  })

  await fastify.register(rateLimit, {
    max: config.RATE_LIMIT_MAX,
    timeWindow: config.RATE_LIMIT_WINDOW
  })

  await fastify.register(jwt, {
    secret: config.JWT_SECRET
  })

  await fastify.register(swagger, {
    swagger: {
      info: {
        title: 'SFQuant API',
        description: 'Cryptocurrency Quantitative Strategy Management System API',
        version: '1.0.0'
      },
      host: `localhost:${config.PORT}`,
      schemes: ['http', 'https'],
      consumes: ['application/json'],
      produces: ['application/json'],
      tags: [
        { name: 'Health', description: 'Health check endpoints' },
        { name: 'Auth', description: 'Authentication endpoints' },
        { name: 'Exchanges', description: 'Exchange management endpoints' },
        { name: 'Market Data', description: 'Market data endpoints' },
        { name: 'Strategies', description: 'Strategy management endpoints' }
      ]
    }
  })

  await fastify.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false
    }
  })

  // WebSocket
  await fastify.register(websocket)

  // 初始化服务
  await initializeServices(fastify)

  // 设置路由
  await setupRoutes(fastify)

  // 设置WebSocket
  await setupWebSocket(fastify)

  return fastify
}

async function initializeServices(fastify: FastifyInstance): Promise<void> {
  // 数据库服务
  const databaseService = new DatabaseService()
  await databaseService.initialize()
  fastify.decorate('database', databaseService)

  // Redis服务
  const redisService = new RedisService()
  await redisService.initialize()
  fastify.decorate('redis', redisService)

  // 交易所服务
  const exchangeService = new ExchangeService()
  fastify.decorate('exchangeService', exchangeService)

  // 策略服务
  const strategyService = new StrategyService(databaseService, redisService)
  fastify.decorate('strategyService', strategyService)

  // 市场数据服务
  const marketDataService = new MarketDataService(redisService, exchangeService)
  await marketDataService.initialize()
  fastify.decorate('marketDataService', marketDataService)

  // 价格监控服务
  const priceMonitorService = new PriceMonitorService(redisService)
  fastify.decorate('priceMonitorService', priceMonitorService)

  // 性能追踪服务
  const performanceService = new PerformanceTrackingService(redisService)
  fastify.decorate('performanceService', performanceService)

  fastify.log.info('All services initialized successfully')
}

// 声明类型扩展
declare module 'fastify' {
  interface FastifyInstance {
    database: DatabaseService
    redis: RedisService
    exchangeService: ExchangeService
    marketDataService: MarketDataService
    strategyService: StrategyService
    priceMonitorService: PriceMonitorService
    performanceService: PerformanceTrackingService
  }
}

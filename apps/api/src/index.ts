import Fastify from 'fastify'
import cors from '@fastify/cors'
import helmet from '@fastify/helmet'
import rateLimit from '@fastify/rate-limit'
import swagger from '@fastify/swagger'
import swaggerUi from '@fastify/swagger-ui'
import jwt from '@fastify/jwt'
// import websocket from '@fastify/websocket'

import { config } from './config'
import { setupRoutes } from './routes'
// import { setupWebSocket } from './websocket'
import { DatabaseService } from './services/DatabaseService'
import { RedisService } from './services/RedisService'
import { ExchangeService } from './services/ExchangeService'
import { StrategyService } from './services/StrategyService'
import { MarketDataService } from './services/MarketDataService'

// 创建Fastify实例
const fastify = Fastify({
  logger: {
    level: config.LOG_LEVEL,
    transport: config.NODE_ENV === 'development' ? {
      target: 'pino-pretty',
      options: {
        colorize: true
      }
    } : undefined
  }
})

// 全局错误处理
fastify.setErrorHandler((error, request, reply) => {
  fastify.log.error(error)

  if (error.validation) {
    reply.status(400).send({
      error: 'Validation Error',
      message: error.message,
      details: error.validation
    })
    return
  }

  if (error.statusCode) {
    reply.status(error.statusCode).send({
      error: error.name,
      message: error.message
    })
    return
  }

  reply.status(500).send({
    error: 'Internal Server Error',
    message: config.NODE_ENV === 'production' ? 'Something went wrong' : error.message
  })
})

// 注册插件
async function registerPlugins() {
  // 安全插件
  await fastify.register(helmet, {
    contentSecurityPolicy: false
  })

  // CORS
  await fastify.register(cors, {
    origin: config.CORS_ORIGINS,
    credentials: true
  })

  // 限流
  await fastify.register(rateLimit, {
    max: config.RATE_LIMIT_MAX,
    timeWindow: config.RATE_LIMIT_WINDOW
  })

  // JWT认证
  await fastify.register(jwt, {
    secret: config.JWT_SECRET
  })

  // WebSocket (disabled for now)
  // await fastify.register(websocket)

  // Swagger文档
  if (config.NODE_ENV === 'development') {
    await fastify.register(swagger, {
      swagger: {
        info: {
          title: 'SFQuant API',
          description: 'Cryptocurrency Quantitative Strategy Management System API',
          version: '1.0.0'
        },
        host: `localhost:${config.PORT}`,
        schemes: ['http', 'https'],
        consumes: ['application/json'],
        produces: ['application/json'],
        securityDefinitions: {
          Bearer: {
            type: 'apiKey',
            name: 'Authorization',
            in: 'header'
          }
        }
      }
    })

    await fastify.register(swaggerUi, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'full',
        deepLinking: false
      }
    })
  }
}

// 初始化服务
async function initializeServices() {
  // 数据库服务
  const databaseService = new DatabaseService()
  await databaseService.connect()
  fastify.decorate('db', databaseService)

  // Redis服务
  const redisService = new RedisService(config.REDIS_URL)
  await redisService.connect()
  fastify.decorate('redis', redisService)

  // 交易所服务
  const exchangeService = new ExchangeService()
  fastify.decorate('exchangeService', exchangeService)

  // 策略服务
  const strategyService = new StrategyService(databaseService, redisService)
  fastify.decorate('strategyService', strategyService)

  // 市场数据服务
  const marketDataService = new MarketDataService(redisService)
  await marketDataService.initialize()
  fastify.decorate('marketDataService', marketDataService)

  fastify.log.info('All services initialized successfully')
}

// 启动服务器
async function start() {
  try {
    // 注册插件
    await registerPlugins()

    // 初始化服务
    await initializeServices()

    // 设置路由
    await setupRoutes(fastify)

    // 设置WebSocket (disabled for now)
    // await setupWebSocket(fastify)

    // 启动服务器
    await fastify.listen({
      port: config.PORT,
      host: config.HOST
    })

    fastify.log.info(`Server is running on http://${config.HOST}:${config.PORT}`)

    if (config.NODE_ENV === 'development') {
      fastify.log.info(`Swagger docs available at http://${config.HOST}:${config.PORT}/docs`)
    }

  } catch (error) {
    fastify.log.error(error)
    process.exit(1)
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  fastify.log.info('Received SIGINT, shutting down gracefully...')

  try {
    await fastify.close()
    process.exit(0)
  } catch (error) {
    fastify.log.error('Error during shutdown:', error)
    process.exit(1)
  }
})

process.on('SIGTERM', async () => {
  fastify.log.info('Received SIGTERM, shutting down gracefully...')

  try {
    await fastify.close()
    process.exit(0)
  } catch (error) {
    fastify.log.error('Error during shutdown:', error)
    process.exit(1)
  }
})

// 启动应用
start()

// 类型声明
declare module 'fastify' {
  interface FastifyInstance {
    db: DatabaseService
    redis: RedisService
    exchangeService: ExchangeService
    strategyService: StrategyService
    marketDataService: MarketDataService
  }
}

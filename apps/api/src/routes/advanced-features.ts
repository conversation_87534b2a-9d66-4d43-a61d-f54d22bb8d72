import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { StrategyTemplateService } from '../services/StrategyTemplateService'
import { BacktestService, BacktestConfig } from '../services/BacktestService'
import { TechnicalIndicatorService, OHLCV } from '../services/TechnicalIndicatorService'
import { PriceMonitorService } from '../services/PriceMonitorService'
import { PerformanceTrackingService } from '../services/PerformanceTrackingService'

export async function advancedFeaturesRoutes(fastify: FastifyInstance) {
  const strategyService = new StrategyTemplateService()
  const backtestService = new BacktestService()
  const technicalService = new TechnicalIndicatorService()

  // 获取策略模板
  fastify.get('/strategy-templates', {
    schema: {
      description: 'Get all available strategy templates',
      tags: ['advanced-features'],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  description: { type: 'string' },
                  type: { type: 'string' },
                  riskLevel: { type: 'string' },
                  expectedReturn: { type: 'number' },
                  maxDrawdown: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const templates = strategyService.getStrategyTemplates()
      return {
        success: true,
        data: templates.map(t => ({
          id: t.id,
          name: t.name,
          description: t.description,
          type: t.type,
          riskLevel: t.riskLevel,
          expectedReturn: t.expectedReturn,
          maxDrawdown: t.maxDrawdown,
          timeframe: t.timeframe,
          symbols: t.symbols,
          parameters: t.parameters
        }))
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取策略详情
  fastify.get('/strategy-templates/:id', {
    schema: {
      description: 'Get strategy template details',
      tags: ['advanced-features'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      const templates = strategyService.getStrategyTemplates()
      const template = templates.find(t => t.id === id)
      
      if (!template) {
        return reply.status(404).send({
          success: false,
          error: 'Strategy template not found'
        })
      }

      return {
        success: true,
        data: template
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 运行回测
  fastify.post('/backtest', {
    schema: {
      description: 'Run strategy backtest',
      tags: ['advanced-features'],
      body: {
        type: 'object',
        properties: {
          strategyId: { type: 'string' },
          symbol: { type: 'string' },
          startDate: { type: 'string' },
          endDate: { type: 'string' },
          initialCapital: { type: 'number', default: 10000 },
          commission: { type: 'number', default: 0.001 },
          slippage: { type: 'number', default: 0.0005 },
          timeframe: { type: 'string', default: '1h' }
        },
        required: ['strategyId', 'symbol', 'startDate', 'endDate']
      }
    }
  }, async (request: FastifyRequest<{ Body: BacktestConfig }>, reply: FastifyReply) => {
    try {
      const config = request.body
      const result = await backtestService.runBacktest(config)
      
      return {
        success: true,
        data: result
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取策略性能
  fastify.get('/strategy-performance/:id', {
    schema: {
      description: 'Get strategy performance metrics',
      tags: ['advanced-features'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      const performance = await backtestService.getStrategyPerformance(id)
      
      return {
        success: true,
        data: performance
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 计算技术指标
  fastify.post('/technical-indicators', {
    schema: {
      description: 'Calculate technical indicators',
      tags: ['advanced-features'],
      body: {
        type: 'object',
        properties: {
          symbol: { type: 'string' },
          timeframe: { type: 'string', default: '1h' },
          limit: { type: 'number', default: 100 }
        },
        required: ['symbol']
      }
    }
  }, async (request: FastifyRequest<{ Body: { symbol: string; timeframe?: string; limit?: number } }>, reply: FastifyReply) => {
    try {
      const { symbol, timeframe = '1h', limit = 100 } = request.body
      
      // 获取历史数据
      const history = await fastify.marketDataService.getPriceHistory(symbol, timeframe, limit)
      
      if (history.length === 0) {
        return reply.status(404).send({
          success: false,
          error: 'No historical data available'
        })
      }

      // 转换为OHLCV格式
      const ohlcvData: OHLCV[] = history.map((item: any) => ({
        timestamp: item.timestamp,
        open: item.open,
        high: item.high,
        low: item.low,
        close: item.close,
        volume: item.volume
      }))

      // 计算技术指标
      const indicators = technicalService.calculateAllIndicators(ohlcvData)
      
      // 生成交易信号
      const currentPrice = ohlcvData[ohlcvData.length - 1].close
      const signals = technicalService.generateSignals(indicators, currentPrice)

      return {
        success: true,
        data: {
          symbol,
          timeframe,
          indicators,
          signals,
          dataPoints: ohlcvData.length
        }
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取价格监控统计
  fastify.get('/price-monitor/stats', {
    schema: {
      description: 'Get price monitoring statistics',
      tags: ['advanced-features']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = fastify.priceMonitorService.getStats()
      return {
        success: true,
        data: stats
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取价格异常
  fastify.get('/price-monitor/anomalies', {
    schema: {
      description: 'Get price anomalies',
      tags: ['advanced-features'],
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'string', pattern: '^[1-9][0-9]*$', default: '50' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { limit?: string } }>, reply: FastifyReply) => {
    try {
      const limit = parseInt(request.query.limit || '50')
      const anomalies = fastify.priceMonitorService.getAnomalies(limit)
      
      return {
        success: true,
        data: anomalies
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 添加价格提醒
  fastify.post('/price-monitor/alerts', {
    schema: {
      description: 'Add price alert',
      tags: ['advanced-features'],
      body: {
        type: 'object',
        properties: {
          symbol: { type: 'string' },
          type: { type: 'string', enum: ['PRICE_ABOVE', 'PRICE_BELOW', 'CHANGE_ABOVE', 'CHANGE_BELOW', 'VOLUME_SPIKE'] },
          threshold: { type: 'number' },
          isActive: { type: 'boolean', default: true }
        },
        required: ['symbol', 'type', 'threshold']
      }
    }
  }, async (request: FastifyRequest<{ Body: { symbol: string; type: string; threshold: number; isActive?: boolean } }>, reply: FastifyReply) => {
    try {
      const alertData = request.body
      const alertId = fastify.priceMonitorService.addAlert(alertData)
      
      return {
        success: true,
        data: { alertId }
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取性能指标
  fastify.get('/performance/metrics', {
    schema: {
      description: 'Get system performance metrics',
      tags: ['advanced-features'],
      querystring: {
        type: 'object',
        properties: {
          hours: { type: 'string', pattern: '^[1-9][0-9]*$', default: '24' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { hours?: string } }>, reply: FastifyReply) => {
    try {
      const hours = parseInt(request.query.hours || '24')
      const metrics = fastify.performanceService.getHistoricalMetrics(hours)
      
      return {
        success: true,
        data: {
          metrics,
          latest: fastify.performanceService.getLatestMetrics(),
          count: metrics.length
        }
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取性能报告
  fastify.get('/performance/report', {
    schema: {
      description: 'Get performance report',
      tags: ['advanced-features'],
      querystring: {
        type: 'object',
        properties: {
          hours: { type: 'string', pattern: '^[1-9][0-9]*$', default: '24' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { hours?: string } }>, reply: FastifyReply) => {
    try {
      const hours = parseInt(request.query.hours || '24')
      const report = fastify.performanceService.generateReport(hours)
      
      return {
        success: true,
        data: report
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取系统健康状态
  fastify.get('/performance/health', {
    schema: {
      description: 'Get system health status',
      tags: ['advanced-features']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const health = fastify.performanceService.getHealthStatus()
      
      return {
        success: true,
        data: health
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取性能告警
  fastify.get('/performance/alerts', {
    schema: {
      description: 'Get performance alerts',
      tags: ['advanced-features'],
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'string', pattern: '^[1-9][0-9]*$', default: '50' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { limit?: string } }>, reply: FastifyReply) => {
    try {
      const limit = parseInt(request.query.limit || '50')
      const alerts = fastify.performanceService.getAlerts(limit)
      
      return {
        success: true,
        data: alerts
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })
}

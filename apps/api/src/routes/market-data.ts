import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'

interface MarketDataParams {
  base: string
  quote: string
}

interface HistoryQuery {
  timeframe?: string
  limit?: string
}

export async function marketDataRoutes(fastify: FastifyInstance) {
  // 获取单个交易对的市场数据
  fastify.get('/:base/:quote', {
    schema: {
      description: 'Get market data for a trading pair',
      tags: ['market-data'],
      params: {
        type: 'object',
        properties: {
          base: { type: 'string', description: 'Base currency (e.g., BTC)' },
          quote: { type: 'string', description: 'Quote currency (e.g., USDT)' }
        },
        required: ['base', 'quote']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                symbol: { type: 'string' },
                timestamp: { type: 'number' },
                bid: { type: 'number' },
                ask: { type: 'number' },
                last: { type: 'number' },
                volume: { type: 'number' },
                high: { type: 'number' },
                low: { type: 'number' },
                open: { type: 'number' },
                close: { type: 'number' },
                change: { type: 'number' },
                changePercent: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: MarketDataParams }>, reply: FastifyReply) => {
    try {
      const { base, quote } = request.params
      const symbol = `${base.toUpperCase()}/${quote.toUpperCase()}`

      const marketData = await fastify.marketDataService.getMarketData(symbol)

      if (!marketData) {
        return reply.status(404).send({
          success: false,
          error: `Market data not found for ${symbol}`
        })
      }

      return {
        success: true,
        data: marketData
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取订单簿数据
  fastify.get('/:base/:quote/orderbook', {
    schema: {
      description: 'Get order book for a trading pair',
      tags: ['market-data'],
      params: {
        type: 'object',
        properties: {
          base: { type: 'string' },
          quote: { type: 'string' }
        },
        required: ['base', 'quote']
      }
    }
  }, async (request: FastifyRequest<{ Params: MarketDataParams }>, reply: FastifyReply) => {
    try {
      const { base, quote } = request.params
      const symbol = `${base.toUpperCase()}/${quote.toUpperCase()}`

      const orderBook = await fastify.marketDataService.getOrderBook(symbol)

      if (!orderBook) {
        return reply.status(404).send({
          success: false,
          error: `Order book not found for ${symbol}`
        })
      }

      return {
        success: true,
        data: orderBook
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取价格历史数据
  fastify.get('/:base/:quote/history', {
    schema: {
      description: 'Get price history for a trading pair',
      tags: ['market-data'],
      params: {
        type: 'object',
        properties: {
          base: { type: 'string' },
          quote: { type: 'string' }
        },
        required: ['base', 'quote']
      },
      querystring: {
        type: 'object',
        properties: {
          timeframe: { type: 'string', enum: ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'], default: '1h' },
          limit: { type: 'string', pattern: '^[1-9][0-9]*$', default: '100' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: MarketDataParams; Querystring: HistoryQuery }>, reply: FastifyReply) => {
    try {
      const { base, quote } = request.params
      const { timeframe = '1h', limit = '100' } = request.query
      const symbol = `${base.toUpperCase()}/${quote.toUpperCase()}`

      const history = await fastify.marketDataService.getPriceHistory(symbol, timeframe, parseInt(limit))

      return {
        success: true,
        data: {
          symbol,
          timeframe,
          history
        }
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取多个交易对的市场数据
  fastify.get('/tickers', {
    schema: {
      description: 'Get market data for multiple trading pairs',
      tags: ['market-data'],
      querystring: {
        type: 'object',
        properties: {
          symbols: { type: 'string', description: 'Comma-separated list of symbols (e.g., BTC/USDT,ETH/USDT)' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { symbols?: string } }>, reply: FastifyReply) => {
    try {
      const { symbols } = request.query

      if (!symbols) {
        // 返回默认的热门交易对
        const defaultSymbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT', 'SOL/USDT']
        const results = await Promise.all(
          defaultSymbols.map(async (symbol) => {
            const data = await fastify.marketDataService.getMarketData(symbol)
            return data
          })
        )

        return {
          success: true,
          data: results.filter(Boolean)
        }
      }

      const symbolList = symbols.split(',').map(s => s.trim().toUpperCase())
      const results = await Promise.all(
        symbolList.map(async (symbol) => {
          const data = await fastify.marketDataService.getMarketData(symbol)
          return data
        })
      )

      return {
        success: true,
        data: results.filter(Boolean)
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })

  // 获取交易所信息
  fastify.get('/exchanges', {
    schema: {
      description: 'Get available exchanges',
      tags: ['market-data']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      return {
        success: true,
        data: [
          {
            id: 'binance',
            name: 'Binance',
            type: 'CEX',
            status: 'active',
            features: ['spot', 'futures', 'options']
          }
        ]
      }
    } catch (error: any) {
      reply.status(500).send({
        success: false,
        error: error.message
      })
    }
  })
}

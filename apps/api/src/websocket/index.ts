import { FastifyInstance } from 'fastify'
import { config } from '../config'

interface WebSocketClient {
  id: string
  socket: any
  subscriptions: Set<string>
  lastPing: number
  userId?: string
}

export async function setupWebSocket(fastify: FastifyInstance): Promise<void> {
  const clients = new Map<string, WebSocketClient>()
  let clientIdCounter = 0

  // WebSocket路由
  fastify.register(async function (fastify) {
    fastify.get('/ws', { websocket: true }, (connection, request) => {
      const clientId = `client_${++clientIdCounter}_${Date.now()}`
      const client: WebSocketClient = {
        id: clientId,
        socket: connection.socket,
        subscriptions: new Set(),
        lastPing: Date.now()
      }

      clients.set(clientId, client)
      console.log(`WebSocket client connected: ${clientId}`)

      // 发送欢迎消息
      connection.socket.send(JSON.stringify({
        type: 'welcome',
        clientId,
        timestamp: Date.now()
      }))

      // 处理消息
      connection.socket.on('message', async (message: any) => {
        try {
          const data = JSON.parse(message.toString())
          await handleWebSocketMessage(client, data, fastify)
        } catch (error) {
          console.error('WebSocket message error:', error)
          connection.socket.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format',
            timestamp: Date.now()
          }))
        }
      })

      // 处理连接关闭
      connection.socket.on('close', () => {
        console.log(`WebSocket client disconnected: ${clientId}`)
        clients.delete(clientId)
      })

      // 处理错误
      connection.socket.on('error', (error: any) => {
        console.error(`WebSocket error for client ${clientId}:`, error)
        clients.delete(clientId)
      })
    })
  })

  // 心跳检测
  const heartbeatInterval = setInterval(() => {
    const now = Date.now()
    for (const [clientId, client] of clients.entries()) {
      if (now - client.lastPing > config.WS_HEARTBEAT_TIMEOUT) {
        console.log(`WebSocket client ${clientId} timed out`)
        try {
          client.socket.close()
        } catch (error) {
          console.error('Error closing WebSocket:', error)
        }
        clients.delete(clientId)
      } else {
        // 发送ping
        try {
          client.socket.send(JSON.stringify({
            type: 'ping',
            timestamp: now
          }))
        } catch (error) {
          console.error('Error sending ping:', error)
          clients.delete(clientId)
        }
      }
    }
  }, config.WS_HEARTBEAT_INTERVAL)

  // 订阅Redis消息
  await setupRedisSubscriptions(fastify, clients)

  // 清理函数
  fastify.addHook('onClose', async () => {
    clearInterval(heartbeatInterval)
    // 关闭所有WebSocket连接
    for (const client of clients.values()) {
      try {
        client.socket.close()
      } catch (error) {
        console.error('Error closing WebSocket:', error)
      }
    }
    clients.clear()
  })

  console.log('WebSocket server setup completed')
}

// 处理WebSocket消息
async function handleWebSocketMessage(client: WebSocketClient, data: any, fastify: FastifyInstance): Promise<void> {
  const { type, payload } = data

  switch (type) {
    case 'pong':
      client.lastPing = Date.now()
      break
    case 'auth':
      await handleAuth(client, payload, fastify)
      break
    case 'subscribe':
      await handleSubscribe(client, payload, fastify)
      break
    case 'unsubscribe':
      await handleUnsubscribe(client, payload)
      break
    case 'get_market_data':
      await handleGetMarketData(client, payload, fastify)
      break
    case 'get_strategy_status':
      await handleGetStrategyStatus(client, payload, fastify)
      break
    default:
      client.socket.send(JSON.stringify({
        type: 'error',
        message: `Unknown message type: ${type}`,
        timestamp: Date.now()
      }))
  }
}

// 处理认证
async function handleAuth(client: WebSocketClient, payload: any, fastify: FastifyInstance): Promise<void> {
  const { token } = payload

  if (!token) {
    client.socket.send(JSON.stringify({
      type: 'auth_error',
      message: 'Token required',
      timestamp: Date.now()
    }))
    return
  }

  try {
    // 验证JWT token
    const decoded = fastify.jwt.verify(token) as any
    client.userId = decoded.userId

    client.socket.send(JSON.stringify({
      type: 'auth_success',
      userId: client.userId,
      timestamp: Date.now()
    }))

    console.log(`Client ${client.id} authenticated as user ${client.userId}`)
  } catch (error) {
    client.socket.send(JSON.stringify({
      type: 'auth_error',
      message: 'Invalid token',
      timestamp: Date.now()
    }))
  }
}

// 处理订阅
async function handleSubscribe(client: WebSocketClient, payload: any, fastify: FastifyInstance): Promise<void> {
  const { channel, symbol } = payload

  if (!channel) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'Channel required',
      timestamp: Date.now()
    }))
    return
  }

  const subscriptionKey = symbol ? `${channel}:${symbol}` : channel
  client.subscriptions.add(subscriptionKey)

  // 如果是价格订阅，确保市场数据服务订阅了该交易对
  if (channel === 'price' && symbol) {
    await fastify.marketDataService.subscribeToSymbol(symbol)
  }

  client.socket.send(JSON.stringify({
    type: 'subscribed',
    channel,
    symbol,
    timestamp: Date.now()
  }))

  console.log(`Client ${client.id} subscribed to ${subscriptionKey}`)
}

// 处理取消订阅
async function handleUnsubscribe(client: WebSocketClient, payload: any): Promise<void> {
  const { channel, symbol } = payload

  if (!channel) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'Channel required',
      timestamp: Date.now()
    }))
    return
  }

  const subscriptionKey = symbol ? `${channel}:${symbol}` : channel
  client.subscriptions.delete(subscriptionKey)

  client.socket.send(JSON.stringify({
    type: 'unsubscribed',
    channel,
    symbol,
    timestamp: Date.now()
  }))

  console.log(`Client ${client.id} unsubscribed from ${subscriptionKey}`)
}

// 处理获取市场数据
async function handleGetMarketData(client: WebSocketClient, payload: any, fastify: FastifyInstance): Promise<void> {
  const { symbol } = payload

  if (!symbol) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'Symbol required',
      timestamp: Date.now()
    }))
    return
  }

  try {
    const marketData = await fastify.marketDataService.getMarketData(symbol)
    client.socket.send(JSON.stringify({
      type: 'market_data',
      symbol,
      data: marketData,
      timestamp: Date.now()
    }))
  } catch (error) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: `Failed to get market data for ${symbol}`,
      timestamp: Date.now()
    }))
  }
}

// 处理获取策略状态
async function handleGetStrategyStatus(client: WebSocketClient, payload: any, fastify: FastifyInstance): Promise<void> {
  const { strategyId } = payload

  if (!strategyId) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: 'Strategy ID required',
      timestamp: Date.now()
    }))
    return
  }

  try {
    const strategy = await fastify.strategyService.getStrategy(strategyId)
    client.socket.send(JSON.stringify({
      type: 'strategy_status',
      strategyId,
      data: strategy,
      timestamp: Date.now()
    }))
  } catch (error) {
    client.socket.send(JSON.stringify({
      type: 'error',
      message: `Failed to get strategy status for ${strategyId}`,
      timestamp: Date.now()
    }))
  }
}

// 设置Redis订阅
async function setupRedisSubscriptions(fastify: FastifyInstance, clients: Map<string, WebSocketClient>): Promise<void> {
  // 订阅价格更新
  await fastify.redis.subscribe('price_updates', (message: string) => {
    try {
      const data = JSON.parse(message)
      broadcastToSubscribers(clients, `price:${data.symbol}`, {
        type: 'price_update',
        symbol: data.symbol,
        data: data.data,
        exchangeId: data.exchangeId,
        timestamp: data.timestamp
      })
    } catch (error) {
      console.error('Error processing price update:', error)
    }
  })

  // 订阅订单簿更新
  await fastify.redis.subscribe('orderbook_updates', (message: string) => {
    try {
      const data = JSON.parse(message)
      broadcastToSubscribers(clients, `orderbook:${data.symbol}`, {
        type: 'orderbook_update',
        symbol: data.symbol,
        data: data.data,
        exchangeId: data.exchangeId,
        timestamp: data.timestamp
      })
    } catch (error) {
      console.error('Error processing orderbook update:', error)
    }
  })

  // 订阅交易更新
  await fastify.redis.subscribe('trade_updates', (message: string) => {
    try {
      const data = JSON.parse(message)
      broadcastToSubscribers(clients, `trades:${data.symbol}`, {
        type: 'trade_update',
        symbol: data.symbol,
        data: data.data,
        exchangeId: data.exchangeId,
        timestamp: data.timestamp
      })
    } catch (error) {
      console.error('Error processing trade update:', error)
    }
  })

  // 订阅通知
  await fastify.redis.subscribe('notifications', (message: string) => {
    try {
      const notification = JSON.parse(message)
      broadcastToUser(clients, notification.userId, {
        type: 'notification',
        ...notification
      })
    } catch (error) {
      console.error('Error processing notification:', error)
    }
  })

  console.log('Redis subscriptions setup completed')
}

// 广播消息给订阅了特定频道的客户端
function broadcastToSubscribers(clients: Map<string, WebSocketClient>, channel: string, message: any): void {
  for (const client of clients.values()) {
    if (client.subscriptions.has(channel)) {
      try {
        client.socket.send(JSON.stringify(message))
      } catch (error) {
        console.error(`Error sending message to client ${client.id}:`, error)
      }
    }
  }
}

// 广播消息给特定用户
function broadcastToUser(clients: Map<string, WebSocketClient>, userId: string, message: any): void {
  for (const client of clients.values()) {
    if (client.userId === userId) {
      try {
        client.socket.send(JSON.stringify(message))
      } catch (error) {
        console.error(`Error sending message to user ${userId}:`, error)
      }
    }
  }
}

// 广播消息给所有客户端
function broadcastToAll(clients: Map<string, WebSocketClient>, message: any): void {
  for (const client of clients.values()) {
    try {
      client.socket.send(JSON.stringify(message))
    } catch (error) {
      console.error(`Error broadcasting message to client ${client.id}:`, error)
    }
  }
}

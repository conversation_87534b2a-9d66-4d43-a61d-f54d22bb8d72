import { StrategyLanguage, StrategyContext, StrategyResult } from '../../core/dist/index.js';
import { BaseStrategyRuntime, ValidationResult } from './StrategyRuntime';
export declare class TypeScriptRuntime extends BaseStrategyRuntime {
    readonly language: StrategyLanguage;
    private compilerOptions;
    constructor();
    execute(code: string, context: StrategyContext): Promise<StrategyResult>;
    validate(code: string): Promise<ValidationResult>;
    dispose(): Promise<void>;
    private compileTypeScript;
    private validateStrategySyntax;
    private calculateMetrics;
}
export declare const TYPESCRIPT_STRATEGY_TEMPLATE = "\n/**\n * TypeScript\u7B56\u7565\u6A21\u677F\n *\n * \u53EF\u7528\u7684\u5168\u5C40\u53D8\u91CF:\n * - marketData: \u5E02\u573A\u6570\u636E\n * - orderBooks: \u8BA2\u5355\u7C3F\u6570\u636E\n * - balances: \u8D26\u6237\u4F59\u989D\n * - openOrders: \u672A\u5B8C\u6210\u8BA2\u5355\n * - strategy: \u7B56\u7565\u914D\u7F6E\n * - utils: \u5DE5\u5177\u51FD\u6570 (log, warn, error)\n * - indicators: \u6280\u672F\u6307\u6807 (sma, ema, rsi, macd)\n * - createSignal: \u521B\u5EFA\u4EA4\u6613\u4FE1\u53F7\u51FD\u6570\n */\n\ninterface MarketData {\n  symbol: string;\n  timestamp: number;\n  bid: number;\n  ask: number;\n  last: number;\n  volume: number;\n  high: number;\n  low: number;\n  open: number;\n  close: number;\n}\n\ninterface Signal {\n  type: 'buy' | 'sell' | 'hold';\n  symbol: string;\n  amount?: number;\n  price?: number;\n  confidence?: number;\n  metadata?: Record<string, any>;\n}\n\n// \u4E3B\u7B56\u7565\u51FD\u6570 - \u6BCF\u6B21\u5E02\u573A\u6570\u636E\u66F4\u65B0\u65F6\u8C03\u7528\nfunction onTick(\n  marketData: Record<string, MarketData>,\n  orderBooks: Record<string, any>,\n  balances: Record<string, any>,\n  openOrders: any[]\n): void {\n  // \u83B7\u53D6\u7B56\u7565\u53C2\u6570\n  const symbol = strategy.symbols[0]; // \u7B2C\u4E00\u4E2A\u4EA4\u6613\u5BF9\n  const data = marketData[symbol];\n\n  if (!data) {\n    utils.warn(`No market data for ${symbol}`);\n    return;\n  }\n\n  // \u793A\u4F8B: \u7B80\u5355\u7684\u79FB\u52A8\u5E73\u5747\u7B56\u7565\n  const prices = [data.close]; // \u8FD9\u91CC\u5E94\u8BE5\u662F\u5386\u53F2\u4EF7\u683C\u6570\u7EC4\n  const shortMA = indicators.sma(prices, 5);\n  const longMA = indicators.sma(prices, 20);\n\n  if (shortMA.length > 0 && longMA.length > 0) {\n    const currentShortMA = shortMA[shortMA.length - 1];\n    const currentLongMA = longMA[longMA.length - 1];\n\n    if (currentShortMA > currentLongMA) {\n      // \u77ED\u671F\u5747\u7EBF\u4E0A\u7A7F\u957F\u671F\u5747\u7EBF\uFF0C\u4E70\u5165\u4FE1\u53F7\n      createSignal('buy', symbol, 0.1, data.ask, 0.8, {\n        shortMA: currentShortMA,\n        longMA: currentLongMA,\n        reason: 'MA crossover bullish'\n      });\n      utils.log(`Buy signal for ${symbol} at ${data.ask}`);\n    } else if (currentShortMA < currentLongMA) {\n      // \u77ED\u671F\u5747\u7EBF\u4E0B\u7A7F\u957F\u671F\u5747\u7EBF\uFF0C\u5356\u51FA\u4FE1\u53F7\n      createSignal('sell', symbol, 0.1, data.bid, 0.8, {\n        shortMA: currentShortMA,\n        longMA: currentLongMA,\n        reason: 'MA crossover bearish'\n      });\n      utils.log(`Sell signal for ${symbol} at ${data.bid}`);\n    }\n  }\n}\n";
//# sourceMappingURL=TypeScriptRuntime.d.ts.map
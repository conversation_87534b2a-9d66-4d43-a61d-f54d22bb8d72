import { z } from 'zod';
// 交易所配置
export const ExchangeConfig = z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum(['cex', 'dex']),
    apiKey: z.string().optional(),
    secret: z.string().optional(),
    passphrase: z.string().optional(),
    sandbox: z.boolean().default(false),
    rateLimit: z.number().optional(),
    // DEX特有配置
    chainId: z.number().optional(),
    rpcUrl: z.string().optional(),
    privateKey: z.string().optional(),
    // 其他配置
    options: z.record(z.any()).optional()
});
// 交易所能力
export const ExchangeCapabilities = z.object({
    trading: z.boolean(),
    fetchTicker: z.boolean(),
    fetchOrderBook: z.boolean(),
    fetchTrades: z.boolean(),
    fetchOHLCV: z.boolean(),
    fetchBalance: z.boolean(),
    fetchOpenOrders: z.boolean(),
    fetchClosedOrders: z.boolean(),
    createOrder: z.boolean(),
    cancelOrder: z.boolean(),
    cancelAllOrders: z.boolean(),
    // WebSocket支持
    ws: z.boolean(),
    watchTicker: z.boolean(),
    watchOrderBook: z.boolean(),
    watchTrades: z.boolean(),
    watchBalance: z.boolean(),
    watchOrders: z.boolean(),
    // DEX特有能力
    swapTokens: z.boolean().optional(),
    addLiquidity: z.boolean().optional(),
    removeLiquidity: z.boolean().optional(),
    flashLoan: z.boolean().optional()
});
// 交易所信息
export const ExchangeInfo = z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum(['cex', 'dex']),
    countries: z.array(z.string()).optional(),
    urls: z.object({
        logo: z.string().optional(),
        www: z.string().optional(),
        api: z.string().optional(),
        doc: z.string().optional()
    }).optional(),
    capabilities: ExchangeCapabilities,
    fees: z.object({
        trading: z.object({
            maker: z.number(),
            taker: z.number()
        }).optional(),
        funding: z.object({
            withdraw: z.record(z.number()),
            deposit: z.record(z.number())
        }).optional()
    }).optional(),
    limits: z.object({
        amount: z.object({
            min: z.number().optional(),
            max: z.number().optional()
        }).optional(),
        price: z.object({
            min: z.number().optional(),
            max: z.number().optional()
        }).optional(),
        cost: z.object({
            min: z.number().optional(),
            max: z.number().optional()
        }).optional()
    }).optional()
});
// 交易所错误类型
export class ExchangeError extends Error {
    constructor(message, exchangeId, code, details) {
        super(message);
        this.exchangeId = exchangeId;
        this.code = code;
        this.details = details;
        this.name = 'ExchangeError';
    }
}
export class NetworkError extends ExchangeError {
    constructor(message, exchangeId, details) {
        super(message, exchangeId, 'NETWORK_ERROR', details);
        this.name = 'NetworkError';
    }
}
export class AuthenticationError extends ExchangeError {
    constructor(message, exchangeId, details) {
        super(message, exchangeId, 'AUTH_ERROR', details);
        this.name = 'AuthenticationError';
    }
}
export class InsufficientFunds extends ExchangeError {
    constructor(message, exchangeId, details) {
        super(message, exchangeId, 'INSUFFICIENT_FUNDS', details);
        this.name = 'InsufficientFunds';
    }
}
export class InvalidOrder extends ExchangeError {
    constructor(message, exchangeId, details) {
        super(message, exchangeId, 'INVALID_ORDER', details);
        this.name = 'InvalidOrder';
    }
}
export class RateLimitExceeded extends ExchangeError {
    constructor(message, exchangeId, details) {
        super(message, exchangeId, 'RATE_LIMIT', details);
        this.name = 'RateLimitExceeded';
    }
}
//# sourceMappingURL=exchange.js.map
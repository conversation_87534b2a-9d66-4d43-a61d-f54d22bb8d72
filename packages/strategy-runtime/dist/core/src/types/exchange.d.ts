import { z } from 'zod';
import { <PERSON><PERSON>rde<PERSON>, OrderR<PERSON>ult, OrderBook, Balance, MarketData } from './strategy';
export declare const ExchangeConfig: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    type: z.<PERSON><PERSON><PERSON><["cex", "dex"]>;
    apiKey: z.<PERSON>ptional<z.ZodString>;
    secret: z.ZodOptional<z.ZodString>;
    passphrase: z.ZodOptional<z.ZodString>;
    sandbox: z.ZodDefault<z.ZodBoolean>;
    rateLimit: z.ZodOptional<z.ZodNumber>;
    chainId: z.ZodOptional<z.ZodNumber>;
    rpcUrl: z.ZodOptional<z.ZodString>;
    privateKey: z.ZodOptional<z.ZodString>;
    options: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
    type: "cex" | "dex";
    id: string;
    name: string;
    sandbox: boolean;
    options?: Record<string, any> | undefined;
    apiKey?: string | undefined;
    secret?: string | undefined;
    passphrase?: string | undefined;
    rateLimit?: number | undefined;
    chainId?: number | undefined;
    rpcUrl?: string | undefined;
    privateKey?: string | undefined;
}, {
    type: "cex" | "dex";
    id: string;
    name: string;
    options?: Record<string, any> | undefined;
    apiKey?: string | undefined;
    secret?: string | undefined;
    passphrase?: string | undefined;
    sandbox?: boolean | undefined;
    rateLimit?: number | undefined;
    chainId?: number | undefined;
    rpcUrl?: string | undefined;
    privateKey?: string | undefined;
}>;
export type ExchangeConfig = z.infer<typeof ExchangeConfig>;
export interface UnifiedExchange {
    readonly id: string;
    readonly name: string;
    readonly type: 'cex' | 'dex';
    initialize(config: ExchangeConfig): Promise<void>;
    isInitialized(): boolean;
    fetchMarkets(): Promise<string[]>;
    fetchTicker(symbol: string): Promise<MarketData>;
    fetchOrderBook(symbol: string, limit?: number): Promise<OrderBook>;
    fetchTrades(symbol: string, limit?: number): Promise<any[]>;
    fetchOHLCV(symbol: string, timeframe: string, since?: number, limit?: number): Promise<any[]>;
    fetchBalance(): Promise<Record<string, Balance>>;
    fetchOpenOrders(symbol?: string): Promise<OrderResult[]>;
    fetchClosedOrders(symbol?: string, since?: number, limit?: number): Promise<OrderResult[]>;
    createOrder(order: UnifiedOrder): Promise<OrderResult>;
    cancelOrder(id: string, symbol?: string): Promise<OrderResult>;
    cancelAllOrders(symbol?: string): Promise<OrderResult[]>;
    subscribeToTicker(symbol: string, callback: (data: MarketData) => void): Promise<void>;
    subscribeToOrderBook(symbol: string, callback: (data: OrderBook) => void): Promise<void>;
    subscribeToTrades(symbol: string, callback: (data: any) => void): Promise<void>;
    unsubscribe(symbol: string, type: 'ticker' | 'orderbook' | 'trades'): Promise<void>;
    estimateGas?(transaction: any): Promise<bigint>;
    getTokenPrice?(tokenAddress: string, baseToken?: string): Promise<number>;
    getLiquidityPools?(tokenA: string, tokenB: string): Promise<any[]>;
    fetchDepositAddress?(currency: string): Promise<any>;
    fetchWithdrawals?(currency?: string, since?: number, limit?: number): Promise<any[]>;
    fetchDeposits?(currency?: string, since?: number, limit?: number): Promise<any[]>;
}
export declare const ExchangeCapabilities: z.ZodObject<{
    trading: z.ZodBoolean;
    fetchTicker: z.ZodBoolean;
    fetchOrderBook: z.ZodBoolean;
    fetchTrades: z.ZodBoolean;
    fetchOHLCV: z.ZodBoolean;
    fetchBalance: z.ZodBoolean;
    fetchOpenOrders: z.ZodBoolean;
    fetchClosedOrders: z.ZodBoolean;
    createOrder: z.ZodBoolean;
    cancelOrder: z.ZodBoolean;
    cancelAllOrders: z.ZodBoolean;
    ws: z.ZodBoolean;
    watchTicker: z.ZodBoolean;
    watchOrderBook: z.ZodBoolean;
    watchTrades: z.ZodBoolean;
    watchBalance: z.ZodBoolean;
    watchOrders: z.ZodBoolean;
    swapTokens: z.ZodOptional<z.ZodBoolean>;
    addLiquidity: z.ZodOptional<z.ZodBoolean>;
    removeLiquidity: z.ZodOptional<z.ZodBoolean>;
    flashLoan: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    trading: boolean;
    fetchTicker: boolean;
    fetchOrderBook: boolean;
    fetchTrades: boolean;
    fetchOHLCV: boolean;
    fetchBalance: boolean;
    fetchOpenOrders: boolean;
    fetchClosedOrders: boolean;
    createOrder: boolean;
    cancelOrder: boolean;
    cancelAllOrders: boolean;
    ws: boolean;
    watchTicker: boolean;
    watchOrderBook: boolean;
    watchTrades: boolean;
    watchBalance: boolean;
    watchOrders: boolean;
    swapTokens?: boolean | undefined;
    addLiquidity?: boolean | undefined;
    removeLiquidity?: boolean | undefined;
    flashLoan?: boolean | undefined;
}, {
    trading: boolean;
    fetchTicker: boolean;
    fetchOrderBook: boolean;
    fetchTrades: boolean;
    fetchOHLCV: boolean;
    fetchBalance: boolean;
    fetchOpenOrders: boolean;
    fetchClosedOrders: boolean;
    createOrder: boolean;
    cancelOrder: boolean;
    cancelAllOrders: boolean;
    ws: boolean;
    watchTicker: boolean;
    watchOrderBook: boolean;
    watchTrades: boolean;
    watchBalance: boolean;
    watchOrders: boolean;
    swapTokens?: boolean | undefined;
    addLiquidity?: boolean | undefined;
    removeLiquidity?: boolean | undefined;
    flashLoan?: boolean | undefined;
}>;
export type ExchangeCapabilities = z.infer<typeof ExchangeCapabilities>;
export declare const ExchangeInfo: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    type: z.ZodEnum<["cex", "dex"]>;
    countries: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    urls: z.ZodOptional<z.ZodObject<{
        logo: z.ZodOptional<z.ZodString>;
        www: z.ZodOptional<z.ZodString>;
        api: z.ZodOptional<z.ZodString>;
        doc: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        logo?: string | undefined;
        www?: string | undefined;
        api?: string | undefined;
        doc?: string | undefined;
    }, {
        logo?: string | undefined;
        www?: string | undefined;
        api?: string | undefined;
        doc?: string | undefined;
    }>>;
    capabilities: z.ZodObject<{
        trading: z.ZodBoolean;
        fetchTicker: z.ZodBoolean;
        fetchOrderBook: z.ZodBoolean;
        fetchTrades: z.ZodBoolean;
        fetchOHLCV: z.ZodBoolean;
        fetchBalance: z.ZodBoolean;
        fetchOpenOrders: z.ZodBoolean;
        fetchClosedOrders: z.ZodBoolean;
        createOrder: z.ZodBoolean;
        cancelOrder: z.ZodBoolean;
        cancelAllOrders: z.ZodBoolean;
        ws: z.ZodBoolean;
        watchTicker: z.ZodBoolean;
        watchOrderBook: z.ZodBoolean;
        watchTrades: z.ZodBoolean;
        watchBalance: z.ZodBoolean;
        watchOrders: z.ZodBoolean;
        swapTokens: z.ZodOptional<z.ZodBoolean>;
        addLiquidity: z.ZodOptional<z.ZodBoolean>;
        removeLiquidity: z.ZodOptional<z.ZodBoolean>;
        flashLoan: z.ZodOptional<z.ZodBoolean>;
    }, "strip", z.ZodTypeAny, {
        trading: boolean;
        fetchTicker: boolean;
        fetchOrderBook: boolean;
        fetchTrades: boolean;
        fetchOHLCV: boolean;
        fetchBalance: boolean;
        fetchOpenOrders: boolean;
        fetchClosedOrders: boolean;
        createOrder: boolean;
        cancelOrder: boolean;
        cancelAllOrders: boolean;
        ws: boolean;
        watchTicker: boolean;
        watchOrderBook: boolean;
        watchTrades: boolean;
        watchBalance: boolean;
        watchOrders: boolean;
        swapTokens?: boolean | undefined;
        addLiquidity?: boolean | undefined;
        removeLiquidity?: boolean | undefined;
        flashLoan?: boolean | undefined;
    }, {
        trading: boolean;
        fetchTicker: boolean;
        fetchOrderBook: boolean;
        fetchTrades: boolean;
        fetchOHLCV: boolean;
        fetchBalance: boolean;
        fetchOpenOrders: boolean;
        fetchClosedOrders: boolean;
        createOrder: boolean;
        cancelOrder: boolean;
        cancelAllOrders: boolean;
        ws: boolean;
        watchTicker: boolean;
        watchOrderBook: boolean;
        watchTrades: boolean;
        watchBalance: boolean;
        watchOrders: boolean;
        swapTokens?: boolean | undefined;
        addLiquidity?: boolean | undefined;
        removeLiquidity?: boolean | undefined;
        flashLoan?: boolean | undefined;
    }>;
    fees: z.ZodOptional<z.ZodObject<{
        trading: z.ZodOptional<z.ZodObject<{
            maker: z.ZodNumber;
            taker: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            maker: number;
            taker: number;
        }, {
            maker: number;
            taker: number;
        }>>;
        funding: z.ZodOptional<z.ZodObject<{
            withdraw: z.ZodRecord<z.ZodString, z.ZodNumber>;
            deposit: z.ZodRecord<z.ZodString, z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            withdraw: Record<string, number>;
            deposit: Record<string, number>;
        }, {
            withdraw: Record<string, number>;
            deposit: Record<string, number>;
        }>>;
    }, "strip", z.ZodTypeAny, {
        trading?: {
            maker: number;
            taker: number;
        } | undefined;
        funding?: {
            withdraw: Record<string, number>;
            deposit: Record<string, number>;
        } | undefined;
    }, {
        trading?: {
            maker: number;
            taker: number;
        } | undefined;
        funding?: {
            withdraw: Record<string, number>;
            deposit: Record<string, number>;
        } | undefined;
    }>>;
    limits: z.ZodOptional<z.ZodObject<{
        amount: z.ZodOptional<z.ZodObject<{
            min: z.ZodOptional<z.ZodNumber>;
            max: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            min?: number | undefined;
            max?: number | undefined;
        }, {
            min?: number | undefined;
            max?: number | undefined;
        }>>;
        price: z.ZodOptional<z.ZodObject<{
            min: z.ZodOptional<z.ZodNumber>;
            max: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            min?: number | undefined;
            max?: number | undefined;
        }, {
            min?: number | undefined;
            max?: number | undefined;
        }>>;
        cost: z.ZodOptional<z.ZodObject<{
            min: z.ZodOptional<z.ZodNumber>;
            max: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            min?: number | undefined;
            max?: number | undefined;
        }, {
            min?: number | undefined;
            max?: number | undefined;
        }>>;
    }, "strip", z.ZodTypeAny, {
        price?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        amount?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        cost?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
    }, {
        price?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        amount?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        cost?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
    }>>;
}, "strip", z.ZodTypeAny, {
    type: "cex" | "dex";
    id: string;
    name: string;
    capabilities: {
        trading: boolean;
        fetchTicker: boolean;
        fetchOrderBook: boolean;
        fetchTrades: boolean;
        fetchOHLCV: boolean;
        fetchBalance: boolean;
        fetchOpenOrders: boolean;
        fetchClosedOrders: boolean;
        createOrder: boolean;
        cancelOrder: boolean;
        cancelAllOrders: boolean;
        ws: boolean;
        watchTicker: boolean;
        watchOrderBook: boolean;
        watchTrades: boolean;
        watchBalance: boolean;
        watchOrders: boolean;
        swapTokens?: boolean | undefined;
        addLiquidity?: boolean | undefined;
        removeLiquidity?: boolean | undefined;
        flashLoan?: boolean | undefined;
    };
    countries?: string[] | undefined;
    urls?: {
        logo?: string | undefined;
        www?: string | undefined;
        api?: string | undefined;
        doc?: string | undefined;
    } | undefined;
    fees?: {
        trading?: {
            maker: number;
            taker: number;
        } | undefined;
        funding?: {
            withdraw: Record<string, number>;
            deposit: Record<string, number>;
        } | undefined;
    } | undefined;
    limits?: {
        price?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        amount?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        cost?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
    } | undefined;
}, {
    type: "cex" | "dex";
    id: string;
    name: string;
    capabilities: {
        trading: boolean;
        fetchTicker: boolean;
        fetchOrderBook: boolean;
        fetchTrades: boolean;
        fetchOHLCV: boolean;
        fetchBalance: boolean;
        fetchOpenOrders: boolean;
        fetchClosedOrders: boolean;
        createOrder: boolean;
        cancelOrder: boolean;
        cancelAllOrders: boolean;
        ws: boolean;
        watchTicker: boolean;
        watchOrderBook: boolean;
        watchTrades: boolean;
        watchBalance: boolean;
        watchOrders: boolean;
        swapTokens?: boolean | undefined;
        addLiquidity?: boolean | undefined;
        removeLiquidity?: boolean | undefined;
        flashLoan?: boolean | undefined;
    };
    countries?: string[] | undefined;
    urls?: {
        logo?: string | undefined;
        www?: string | undefined;
        api?: string | undefined;
        doc?: string | undefined;
    } | undefined;
    fees?: {
        trading?: {
            maker: number;
            taker: number;
        } | undefined;
        funding?: {
            withdraw: Record<string, number>;
            deposit: Record<string, number>;
        } | undefined;
    } | undefined;
    limits?: {
        price?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        amount?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
        cost?: {
            min?: number | undefined;
            max?: number | undefined;
        } | undefined;
    } | undefined;
}>;
export type ExchangeInfo = z.infer<typeof ExchangeInfo>;
export declare class ExchangeError extends Error {
    readonly exchangeId: string;
    readonly code?: string | undefined;
    readonly details?: any | undefined;
    constructor(message: string, exchangeId: string, code?: string | undefined, details?: any | undefined);
}
export declare class NetworkError extends ExchangeError {
    constructor(message: string, exchangeId: string, details?: any);
}
export declare class AuthenticationError extends ExchangeError {
    constructor(message: string, exchangeId: string, details?: any);
}
export declare class InsufficientFunds extends ExchangeError {
    constructor(message: string, exchangeId: string, details?: any);
}
export declare class InvalidOrder extends ExchangeError {
    constructor(message: string, exchangeId: string, details?: any);
}
export declare class RateLimitExceeded extends ExchangeError {
    constructor(message: string, exchangeId: string, details?: any);
}
//# sourceMappingURL=exchange.d.ts.map
// 格式化工具函数
export function formatPrice(price, decimals = 8) {
    return price.toFixed(decimals);
}
export function formatAmount(amount, decimals = 8) {
    return amount.toFixed(decimals);
}
export function formatPercentage(value, decimals = 2) {
    return `${(value * 100).toFixed(decimals)}%`;
}
export function formatCurrency(amount, currency = 'USD', decimals = 2) {
    return `${amount.toFixed(decimals)} ${currency}`;
}
export function formatTimestamp(timestamp) {
    return new Date(timestamp).toISOString();
}
export function formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    if (days > 0) {
        return `${days}d ${hours % 24}h ${minutes % 60}m`;
    }
    else if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }
    else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    }
    else {
        return `${seconds}s`;
    }
}
export function truncateAddress(address, startChars = 6, endChars = 4) {
    if (address.length <= startChars + endChars) {
        return address;
    }
    return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
}
export function formatNumber(num) {
    if (num >= 1e9) {
        return `${(num / 1e9).toFixed(2)}B`;
    }
    else if (num >= 1e6) {
        return `${(num / 1e6).toFixed(2)}M`;
    }
    else if (num >= 1e3) {
        return `${(num / 1e3).toFixed(2)}K`;
    }
    else {
        return num.toString();
    }
}
//# sourceMappingURL=formatting.js.map
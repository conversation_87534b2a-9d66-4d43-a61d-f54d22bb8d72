import { z } from 'zod';
export declare const StrategyLanguage: z.Z<PERSON><["typescript", "python", "javascript"]>;
export type StrategyLanguage = z.infer<typeof StrategyLanguage>;
export declare const StrategyType: z.Zod<PERSON><["arbitrage", "trend", "high_frequency", "market_maker", "ai_ml"]>;
export type StrategyType = z.infer<typeof StrategyType>;
export declare const ExchangeType: z.ZodEnum<["cex", "dex"]>;
export type ExchangeType = z.infer<typeof ExchangeType>;
export declare const OrderSide: z.ZodEnum<["buy", "sell"]>;
export type OrderSide = z.infer<typeof OrderSide>;
export declare const OrderType: z.Zod<PERSON><["market", "limit", "stop", "stop_limit"]>;
export type OrderType = z.infer<typeof OrderType>;
export declare const OrderStatus: z.ZodEnum<["pending", "open", "closed", "canceled", "rejected"]>;
export type OrderStatus = z.infer<typeof OrderStatus>;
export declare const MarketData: z.ZodObject<{
    symbol: z.ZodString;
    timestamp: z.ZodNumber;
    bid: z.ZodNumber;
    ask: z.ZodNumber;
    last: z.ZodNumber;
    volume: z.ZodNumber;
    high: z.ZodNumber;
    low: z.ZodNumber;
    open: z.ZodNumber;
    close: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    open: number;
    timestamp: number;
    bid: number;
    ask: number;
    last: number;
    volume: number;
    high: number;
    low: number;
    close: number;
}, {
    symbol: string;
    open: number;
    timestamp: number;
    bid: number;
    ask: number;
    last: number;
    volume: number;
    high: number;
    low: number;
    close: number;
}>;
export type MarketData = z.infer<typeof MarketData>;
export declare const OrderBookEntry: z.ZodObject<{
    price: z.ZodNumber;
    amount: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    price: number;
    amount: number;
}, {
    price: number;
    amount: number;
}>;
export declare const OrderBook: z.ZodObject<{
    symbol: z.ZodString;
    timestamp: z.ZodNumber;
    bids: z.ZodArray<z.ZodObject<{
        price: z.ZodNumber;
        amount: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        price: number;
        amount: number;
    }, {
        price: number;
        amount: number;
    }>, "many">;
    asks: z.ZodArray<z.ZodObject<{
        price: z.ZodNumber;
        amount: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        price: number;
        amount: number;
    }, {
        price: number;
        amount: number;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    timestamp: number;
    bids: {
        price: number;
        amount: number;
    }[];
    asks: {
        price: number;
        amount: number;
    }[];
}, {
    symbol: string;
    timestamp: number;
    bids: {
        price: number;
        amount: number;
    }[];
    asks: {
        price: number;
        amount: number;
    }[];
}>;
export type OrderBook = z.infer<typeof OrderBook>;
export declare const UnifiedOrder: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    symbol: z.ZodString;
    side: z.ZodEnum<["buy", "sell"]>;
    type: z.ZodEnum<["market", "limit", "stop", "stop_limit"]>;
    amount: z.ZodNumber;
    price: z.ZodOptional<z.ZodNumber>;
    stopPrice: z.ZodOptional<z.ZodNumber>;
    timeInForce: z.ZodOptional<z.ZodEnum<["GTC", "IOC", "FOK"]>>;
    clientOrderId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    type: "market" | "limit" | "stop" | "stop_limit";
    amount: number;
    side: "buy" | "sell";
    price?: number | undefined;
    id?: string | undefined;
    stopPrice?: number | undefined;
    timeInForce?: "GTC" | "IOC" | "FOK" | undefined;
    clientOrderId?: string | undefined;
}, {
    symbol: string;
    type: "market" | "limit" | "stop" | "stop_limit";
    amount: number;
    side: "buy" | "sell";
    price?: number | undefined;
    id?: string | undefined;
    stopPrice?: number | undefined;
    timeInForce?: "GTC" | "IOC" | "FOK" | undefined;
    clientOrderId?: string | undefined;
}>;
export type UnifiedOrder = z.infer<typeof UnifiedOrder>;
export declare const OrderResult: z.ZodObject<{
    id: z.ZodString;
    clientOrderId: z.ZodOptional<z.ZodString>;
    symbol: z.ZodString;
    side: z.ZodEnum<["buy", "sell"]>;
    type: z.ZodEnum<["market", "limit", "stop", "stop_limit"]>;
    amount: z.ZodNumber;
    price: z.ZodOptional<z.ZodNumber>;
    filled: z.ZodNumber;
    remaining: z.ZodNumber;
    status: z.ZodEnum<["pending", "open", "closed", "canceled", "rejected"]>;
    timestamp: z.ZodNumber;
    fee: z.ZodOptional<z.ZodObject<{
        currency: z.ZodString;
        cost: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        currency: string;
        cost: number;
    }, {
        currency: string;
        cost: number;
    }>>;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    timestamp: number;
    type: "market" | "limit" | "stop" | "stop_limit";
    status: "pending" | "open" | "closed" | "canceled" | "rejected";
    amount: number;
    id: string;
    side: "buy" | "sell";
    filled: number;
    remaining: number;
    price?: number | undefined;
    clientOrderId?: string | undefined;
    fee?: {
        currency: string;
        cost: number;
    } | undefined;
}, {
    symbol: string;
    timestamp: number;
    type: "market" | "limit" | "stop" | "stop_limit";
    status: "pending" | "open" | "closed" | "canceled" | "rejected";
    amount: number;
    id: string;
    side: "buy" | "sell";
    filled: number;
    remaining: number;
    price?: number | undefined;
    clientOrderId?: string | undefined;
    fee?: {
        currency: string;
        cost: number;
    } | undefined;
}>;
export type OrderResult = z.infer<typeof OrderResult>;
export declare const Balance: z.ZodObject<{
    currency: z.ZodString;
    free: z.ZodNumber;
    used: z.ZodNumber;
    total: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    currency: string;
    free: number;
    used: number;
    total: number;
}, {
    currency: string;
    free: number;
    used: number;
    total: number;
}>;
export type Balance = z.infer<typeof Balance>;
export declare const Signal: z.ZodObject<{
    type: z.ZodEnum<["buy", "sell", "hold"]>;
    symbol: z.ZodString;
    amount: z.ZodOptional<z.ZodNumber>;
    price: z.ZodOptional<z.ZodNumber>;
    confidence: z.ZodOptional<z.ZodNumber>;
    metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    type: "buy" | "sell" | "hold";
    price?: number | undefined;
    amount?: number | undefined;
    confidence?: number | undefined;
    metadata?: Record<string, any> | undefined;
}, {
    symbol: string;
    type: "buy" | "sell" | "hold";
    price?: number | undefined;
    amount?: number | undefined;
    confidence?: number | undefined;
    metadata?: Record<string, any> | undefined;
}>;
export type Signal = z.infer<typeof Signal>;
export declare const StrategyConfig: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    type: z.ZodEnum<["arbitrage", "trend", "high_frequency", "market_maker", "ai_ml"]>;
    language: z.ZodEnum<["typescript", "python", "javascript"]>;
    code: z.ZodString;
    parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
    exchanges: z.ZodArray<z.ZodString, "many">;
    symbols: z.ZodArray<z.ZodString, "many">;
    enabled: z.ZodDefault<z.ZodBoolean>;
    riskLimits: z.ZodOptional<z.ZodObject<{
        maxPositionSize: z.ZodOptional<z.ZodNumber>;
        maxDailyLoss: z.ZodOptional<z.ZodNumber>;
        maxDrawdown: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        maxPositionSize?: number | undefined;
        maxDailyLoss?: number | undefined;
        maxDrawdown?: number | undefined;
    }, {
        maxPositionSize?: number | undefined;
        maxDailyLoss?: number | undefined;
        maxDrawdown?: number | undefined;
    }>>;
    createdAt: z.ZodNumber;
    updatedAt: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    code: string;
    type: "arbitrage" | "trend" | "high_frequency" | "market_maker" | "ai_ml";
    id: string;
    name: string;
    language: "typescript" | "python" | "javascript";
    parameters: Record<string, any>;
    exchanges: string[];
    symbols: string[];
    enabled: boolean;
    createdAt: number;
    updatedAt: number;
    description?: string | undefined;
    riskLimits?: {
        maxPositionSize?: number | undefined;
        maxDailyLoss?: number | undefined;
        maxDrawdown?: number | undefined;
    } | undefined;
}, {
    code: string;
    type: "arbitrage" | "trend" | "high_frequency" | "market_maker" | "ai_ml";
    id: string;
    name: string;
    language: "typescript" | "python" | "javascript";
    parameters: Record<string, any>;
    exchanges: string[];
    symbols: string[];
    createdAt: number;
    updatedAt: number;
    description?: string | undefined;
    enabled?: boolean | undefined;
    riskLimits?: {
        maxPositionSize?: number | undefined;
        maxDailyLoss?: number | undefined;
        maxDrawdown?: number | undefined;
    } | undefined;
}>;
export type StrategyConfig = z.infer<typeof StrategyConfig>;
export declare const StrategyContext: z.ZodObject<{
    strategy: z.ZodObject<{
        id: z.ZodString;
        name: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        type: z.ZodEnum<["arbitrage", "trend", "high_frequency", "market_maker", "ai_ml"]>;
        language: z.ZodEnum<["typescript", "python", "javascript"]>;
        code: z.ZodString;
        parameters: z.ZodRecord<z.ZodString, z.ZodAny>;
        exchanges: z.ZodArray<z.ZodString, "many">;
        symbols: z.ZodArray<z.ZodString, "many">;
        enabled: z.ZodDefault<z.ZodBoolean>;
        riskLimits: z.ZodOptional<z.ZodObject<{
            maxPositionSize: z.ZodOptional<z.ZodNumber>;
            maxDailyLoss: z.ZodOptional<z.ZodNumber>;
            maxDrawdown: z.ZodOptional<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            maxPositionSize?: number | undefined;
            maxDailyLoss?: number | undefined;
            maxDrawdown?: number | undefined;
        }, {
            maxPositionSize?: number | undefined;
            maxDailyLoss?: number | undefined;
            maxDrawdown?: number | undefined;
        }>>;
        createdAt: z.ZodNumber;
        updatedAt: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        code: string;
        type: "arbitrage" | "trend" | "high_frequency" | "market_maker" | "ai_ml";
        id: string;
        name: string;
        language: "typescript" | "python" | "javascript";
        parameters: Record<string, any>;
        exchanges: string[];
        symbols: string[];
        enabled: boolean;
        createdAt: number;
        updatedAt: number;
        description?: string | undefined;
        riskLimits?: {
            maxPositionSize?: number | undefined;
            maxDailyLoss?: number | undefined;
            maxDrawdown?: number | undefined;
        } | undefined;
    }, {
        code: string;
        type: "arbitrage" | "trend" | "high_frequency" | "market_maker" | "ai_ml";
        id: string;
        name: string;
        language: "typescript" | "python" | "javascript";
        parameters: Record<string, any>;
        exchanges: string[];
        symbols: string[];
        createdAt: number;
        updatedAt: number;
        description?: string | undefined;
        enabled?: boolean | undefined;
        riskLimits?: {
            maxPositionSize?: number | undefined;
            maxDailyLoss?: number | undefined;
            maxDrawdown?: number | undefined;
        } | undefined;
    }>;
    marketData: z.ZodRecord<z.ZodString, z.ZodObject<{
        symbol: z.ZodString;
        timestamp: z.ZodNumber;
        bid: z.ZodNumber;
        ask: z.ZodNumber;
        last: z.ZodNumber;
        volume: z.ZodNumber;
        high: z.ZodNumber;
        low: z.ZodNumber;
        open: z.ZodNumber;
        close: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        open: number;
        timestamp: number;
        bid: number;
        ask: number;
        last: number;
        volume: number;
        high: number;
        low: number;
        close: number;
    }, {
        symbol: string;
        open: number;
        timestamp: number;
        bid: number;
        ask: number;
        last: number;
        volume: number;
        high: number;
        low: number;
        close: number;
    }>>;
    orderBooks: z.ZodRecord<z.ZodString, z.ZodObject<{
        symbol: z.ZodString;
        timestamp: z.ZodNumber;
        bids: z.ZodArray<z.ZodObject<{
            price: z.ZodNumber;
            amount: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            price: number;
            amount: number;
        }, {
            price: number;
            amount: number;
        }>, "many">;
        asks: z.ZodArray<z.ZodObject<{
            price: z.ZodNumber;
            amount: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            price: number;
            amount: number;
        }, {
            price: number;
            amount: number;
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        timestamp: number;
        bids: {
            price: number;
            amount: number;
        }[];
        asks: {
            price: number;
            amount: number;
        }[];
    }, {
        symbol: string;
        timestamp: number;
        bids: {
            price: number;
            amount: number;
        }[];
        asks: {
            price: number;
            amount: number;
        }[];
    }>>;
    balances: z.ZodRecord<z.ZodString, z.ZodObject<{
        currency: z.ZodString;
        free: z.ZodNumber;
        used: z.ZodNumber;
        total: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        currency: string;
        free: number;
        used: number;
        total: number;
    }, {
        currency: string;
        free: number;
        used: number;
        total: number;
    }>>;
    openOrders: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        clientOrderId: z.ZodOptional<z.ZodString>;
        symbol: z.ZodString;
        side: z.ZodEnum<["buy", "sell"]>;
        type: z.ZodEnum<["market", "limit", "stop", "stop_limit"]>;
        amount: z.ZodNumber;
        price: z.ZodOptional<z.ZodNumber>;
        filled: z.ZodNumber;
        remaining: z.ZodNumber;
        status: z.ZodEnum<["pending", "open", "closed", "canceled", "rejected"]>;
        timestamp: z.ZodNumber;
        fee: z.ZodOptional<z.ZodObject<{
            currency: z.ZodString;
            cost: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            currency: string;
            cost: number;
        }, {
            currency: string;
            cost: number;
        }>>;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        timestamp: number;
        type: "market" | "limit" | "stop" | "stop_limit";
        status: "pending" | "open" | "closed" | "canceled" | "rejected";
        amount: number;
        id: string;
        side: "buy" | "sell";
        filled: number;
        remaining: number;
        price?: number | undefined;
        clientOrderId?: string | undefined;
        fee?: {
            currency: string;
            cost: number;
        } | undefined;
    }, {
        symbol: string;
        timestamp: number;
        type: "market" | "limit" | "stop" | "stop_limit";
        status: "pending" | "open" | "closed" | "canceled" | "rejected";
        amount: number;
        id: string;
        side: "buy" | "sell";
        filled: number;
        remaining: number;
        price?: number | undefined;
        clientOrderId?: string | undefined;
        fee?: {
            currency: string;
            cost: number;
        } | undefined;
    }>, "many">;
    timestamp: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    timestamp: number;
    strategy: {
        code: string;
        type: "arbitrage" | "trend" | "high_frequency" | "market_maker" | "ai_ml";
        id: string;
        name: string;
        language: "typescript" | "python" | "javascript";
        parameters: Record<string, any>;
        exchanges: string[];
        symbols: string[];
        enabled: boolean;
        createdAt: number;
        updatedAt: number;
        description?: string | undefined;
        riskLimits?: {
            maxPositionSize?: number | undefined;
            maxDailyLoss?: number | undefined;
            maxDrawdown?: number | undefined;
        } | undefined;
    };
    marketData: Record<string, {
        symbol: string;
        open: number;
        timestamp: number;
        bid: number;
        ask: number;
        last: number;
        volume: number;
        high: number;
        low: number;
        close: number;
    }>;
    orderBooks: Record<string, {
        symbol: string;
        timestamp: number;
        bids: {
            price: number;
            amount: number;
        }[];
        asks: {
            price: number;
            amount: number;
        }[];
    }>;
    balances: Record<string, {
        currency: string;
        free: number;
        used: number;
        total: number;
    }>;
    openOrders: {
        symbol: string;
        timestamp: number;
        type: "market" | "limit" | "stop" | "stop_limit";
        status: "pending" | "open" | "closed" | "canceled" | "rejected";
        amount: number;
        id: string;
        side: "buy" | "sell";
        filled: number;
        remaining: number;
        price?: number | undefined;
        clientOrderId?: string | undefined;
        fee?: {
            currency: string;
            cost: number;
        } | undefined;
    }[];
}, {
    timestamp: number;
    strategy: {
        code: string;
        type: "arbitrage" | "trend" | "high_frequency" | "market_maker" | "ai_ml";
        id: string;
        name: string;
        language: "typescript" | "python" | "javascript";
        parameters: Record<string, any>;
        exchanges: string[];
        symbols: string[];
        createdAt: number;
        updatedAt: number;
        description?: string | undefined;
        enabled?: boolean | undefined;
        riskLimits?: {
            maxPositionSize?: number | undefined;
            maxDailyLoss?: number | undefined;
            maxDrawdown?: number | undefined;
        } | undefined;
    };
    marketData: Record<string, {
        symbol: string;
        open: number;
        timestamp: number;
        bid: number;
        ask: number;
        last: number;
        volume: number;
        high: number;
        low: number;
        close: number;
    }>;
    orderBooks: Record<string, {
        symbol: string;
        timestamp: number;
        bids: {
            price: number;
            amount: number;
        }[];
        asks: {
            price: number;
            amount: number;
        }[];
    }>;
    balances: Record<string, {
        currency: string;
        free: number;
        used: number;
        total: number;
    }>;
    openOrders: {
        symbol: string;
        timestamp: number;
        type: "market" | "limit" | "stop" | "stop_limit";
        status: "pending" | "open" | "closed" | "canceled" | "rejected";
        amount: number;
        id: string;
        side: "buy" | "sell";
        filled: number;
        remaining: number;
        price?: number | undefined;
        clientOrderId?: string | undefined;
        fee?: {
            currency: string;
            cost: number;
        } | undefined;
    }[];
}>;
export type StrategyContext = z.infer<typeof StrategyContext>;
export declare const StrategyResult: z.ZodObject<{
    signals: z.ZodArray<z.ZodObject<{
        type: z.ZodEnum<["buy", "sell", "hold"]>;
        symbol: z.ZodString;
        amount: z.ZodOptional<z.ZodNumber>;
        price: z.ZodOptional<z.ZodNumber>;
        confidence: z.ZodOptional<z.ZodNumber>;
        metadata: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        type: "buy" | "sell" | "hold";
        price?: number | undefined;
        amount?: number | undefined;
        confidence?: number | undefined;
        metadata?: Record<string, any> | undefined;
    }, {
        symbol: string;
        type: "buy" | "sell" | "hold";
        price?: number | undefined;
        amount?: number | undefined;
        confidence?: number | undefined;
        metadata?: Record<string, any> | undefined;
    }>, "many">;
    logs: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    metrics: z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodNumber>>;
    error: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    signals: {
        symbol: string;
        type: "buy" | "sell" | "hold";
        price?: number | undefined;
        amount?: number | undefined;
        confidence?: number | undefined;
        metadata?: Record<string, any> | undefined;
    }[];
    logs?: string[] | undefined;
    metrics?: Record<string, number> | undefined;
    error?: string | undefined;
}, {
    signals: {
        symbol: string;
        type: "buy" | "sell" | "hold";
        price?: number | undefined;
        amount?: number | undefined;
        confidence?: number | undefined;
        metadata?: Record<string, any> | undefined;
    }[];
    logs?: string[] | undefined;
    metrics?: Record<string, number> | undefined;
    error?: string | undefined;
}>;
export type StrategyResult = z.infer<typeof StrategyResult>;
//# sourceMappingURL=strategy.d.ts.map
// Mock external dependencies first
jest.mock('ethers', () => ({
  JsonRpcProvider: jest.fn().mockImplementation(() => ({
    getNetwork: jest.fn().mockResolvedValue({ chainId: 1, name: 'mainnet' }),
    on: jest.fn(),
    off: jest.fn(),
    removeAllListeners: jest.fn()
  })),
  formatEther: jest.fn(),
  parseEther: jest.fn(),
  formatUnits: jest.fn(),
  parseUnits: jest.fn()
}))

jest.mock('ws', () => {
  return jest.fn().mockImplementation(() => ({
    send: jest.fn(),
    close: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    readyState: 1
  }))
})

describe('DEXAdapter', () => {
  let DEXAdapter: any
  let dexAdapter: any
  let mockConfig: any

  beforeAll(async () => {
    // Dynamic import to avoid module loading issues
    const module = await import('../src/DEXAdapter')
    DEXAdapter = module.DEXAdapter
  })

  beforeEach(() => {
    dexAdapter = new DEXAdapter()
    mockConfig = {
      id: 'test-dex',
      name: 'Test DEX',
      type: 'dex',
      chainId: 1,
      rpcUrl: 'https://test-rpc.com',
      sandbox: true
    }
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('Initialization', () => {
    it('should initialize successfully with valid config', async () => {
      await expect(dexAdapter.initialize(mockConfig)).resolves.not.toThrow()
      expect(dexAdapter.isInitialized()).toBe(true)
      expect(dexAdapter.id).toBe('test-dex')
      expect(dexAdapter.name).toBe('Test DEX')
      expect(dexAdapter.type).toBe('dex')
    })

    it('should throw error when chainId is missing', async () => {
      const invalidConfig = { ...mockConfig, chainId: undefined }
      await expect(dexAdapter.initialize(invalidConfig as any)).rejects.toThrow('Chain ID and RPC URL are required')
    })

    it('should throw error when rpcUrl is missing', async () => {
      const invalidConfig = { ...mockConfig, rpcUrl: undefined }
      await expect(dexAdapter.initialize(invalidConfig as any)).rejects.toThrow('Chain ID and RPC URL are required')
    })
  })

  describe('Market Data', () => {
    beforeEach(async () => {
      await dexAdapter.initialize(mockConfig)
    })

    it('should fetch markets', async () => {
      const markets = await dexAdapter.fetchMarkets()
      expect(Array.isArray(markets)).toBe(true)
      expect(markets.length).toBeGreaterThan(0)
      expect(markets).toContain('ETH/USDC')
    })

    it('should fetch ticker data', async () => {
      const ticker = await dexAdapter.fetchTicker('ETH/USDC')
      expect(ticker).toHaveProperty('symbol', 'ETH/USDC')
      expect(ticker).toHaveProperty('timestamp')
      expect(ticker).toHaveProperty('bid')
      expect(ticker).toHaveProperty('ask')
      expect(ticker).toHaveProperty('last')
      expect(typeof ticker.bid).toBe('number')
      expect(typeof ticker.ask).toBe('number')
      expect(typeof ticker.last).toBe('number')
    })

    it('should fetch order book', async () => {
      const orderBook = await dexAdapter.fetchOrderBook('ETH/USDC')
      expect(orderBook).toHaveProperty('symbol', 'ETH/USDC')
      expect(orderBook).toHaveProperty('timestamp')
      expect(orderBook).toHaveProperty('bids')
      expect(orderBook).toHaveProperty('asks')
      expect(Array.isArray(orderBook.bids)).toBe(true)
      expect(Array.isArray(orderBook.asks)).toBe(true)
    })

    it('should throw error for invalid symbol', async () => {
      await expect(dexAdapter.fetchTicker('INVALID/PAIR')).rejects.toThrow()
    })
  })

  describe('Real-time Data Subscriptions', () => {
    beforeEach(async () => {
      await dexAdapter.initialize(mockConfig)
    })

    it('should subscribe to ticker updates', async () => {
      const mockCallback = jest.fn()

      await expect(dexAdapter.subscribeToTicker('ETH/USDC', mockCallback)).resolves.not.toThrow()

      // Verify subscription was stored
      expect((dexAdapter as any).subscriptions.has('ticker:ETH/USDC')).toBe(true)
    })

    it('should subscribe to order book updates', async () => {
      const mockCallback = jest.fn()

      await expect(dexAdapter.subscribeToOrderBook('ETH/USDC', mockCallback)).resolves.not.toThrow()

      // Verify subscription was stored
      expect((dexAdapter as any).subscriptions.has('orderbook:ETH/USDC')).toBe(true)
    })

    it('should subscribe to trade updates', async () => {
      const mockCallback = jest.fn()

      await expect(dexAdapter.subscribeToTrades('ETH/USDC', mockCallback)).resolves.not.toThrow()

      // Verify subscription was stored
      expect((dexAdapter as any).subscriptions.has('trades:ETH/USDC')).toBe(true)
    })

    it('should unsubscribe from all data types', async () => {
      const mockCallback = jest.fn()

      // Subscribe to all types
      await dexAdapter.subscribeToTicker('ETH/USDC', mockCallback)
      await dexAdapter.subscribeToOrderBook('ETH/USDC', mockCallback)
      await dexAdapter.subscribeToTrades('ETH/USDC', mockCallback)

      // Unsubscribe
      await dexAdapter.unsubscribe('ETH/USDC', 'ticker')
      await dexAdapter.unsubscribe('ETH/USDC', 'orderbook')
      await dexAdapter.unsubscribe('ETH/USDC', 'trades')

      // Verify subscriptions were removed
      expect((dexAdapter as any).subscriptions.has('ticker:ETH/USDC')).toBe(false)
      expect((dexAdapter as any).subscriptions.has('orderbook:ETH/USDC')).toBe(false)
      expect((dexAdapter as any).subscriptions.has('trades:ETH/USDC')).toBe(false)
    })
  })

  describe('Token Price', () => {
    beforeEach(async () => {
      await dexAdapter.initialize(mockConfig)
    })

    it('should get token price', async () => {
      const price = await dexAdapter.getTokenPrice('******************************************')
      expect(typeof price).toBe('number')
      expect(price).toBeGreaterThan(0)
    })

    it('should get liquidity pools', async () => {
      const pools = await dexAdapter.getLiquidityPools(
        '******************************************',
        '******************************************'
      )
      expect(Array.isArray(pools)).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should throw error when not initialized', async () => {
      const uninitializedAdapter = new DEXAdapter()
      await expect(uninitializedAdapter.fetchTicker('ETH/USDC')).rejects.toThrow('DEX adapter not initialized')
    })

    it('should handle network errors gracefully', async () => {
      await dexAdapter.initialize(mockConfig)

      // Mock network error
      const mockProvider = (dexAdapter as any).provider
      mockProvider.getNetwork = jest.fn().mockRejectedValue(new Error('Network error'))

      await expect(dexAdapter.fetchTicker('ETH/USDC')).rejects.toThrow()
    })
  })
})

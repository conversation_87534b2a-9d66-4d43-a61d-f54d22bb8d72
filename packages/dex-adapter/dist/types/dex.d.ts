import { z } from 'zod';
export declare const ChainConfig: z.ZodObject<{
    chainId: z.ZodNumber;
    name: z.ZodString;
    rpcUrl: z.ZodString;
    blockExplorer: z.ZodOptional<z.ZodString>;
    nativeCurrency: z.ZodObject<{
        name: z.ZodString;
        symbol: z.ZodString;
        decimals: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        name: string;
        decimals: number;
    }, {
        symbol: string;
        name: string;
        decimals: number;
    }>;
    testnet: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    chainId: number;
    name: string;
    rpcUrl: string;
    nativeCurrency: {
        symbol: string;
        name: string;
        decimals: number;
    };
    testnet: boolean;
    blockExplorer?: string | undefined;
}, {
    chainId: number;
    name: string;
    rpcUrl: string;
    nativeCurrency: {
        symbol: string;
        name: string;
        decimals: number;
    };
    blockExplorer?: string | undefined;
    testnet?: boolean | undefined;
}>;
export type ChainConfig = z.infer<typeof ChainConfig>;
export declare const TokenInfo: z.ZodObject<{
    address: z.ZodString;
    symbol: z.ZodString;
    name: z.ZodString;
    decimals: z.ZodNumber;
    chainId: z.ZodNumber;
    logoURI: z.ZodOptional<z.ZodString>;
    tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    symbol: string;
    chainId: number;
    name: string;
    decimals: number;
    address: string;
    logoURI?: string | undefined;
    tags?: string[] | undefined;
}, {
    symbol: string;
    chainId: number;
    name: string;
    decimals: number;
    address: string;
    logoURI?: string | undefined;
    tags?: string[] | undefined;
}>;
export type TokenInfo = z.infer<typeof TokenInfo>;
export declare const PoolInfo: z.ZodObject<{
    address: z.ZodString;
    token0: z.ZodObject<{
        address: z.ZodString;
        symbol: z.ZodString;
        name: z.ZodString;
        decimals: z.ZodNumber;
        chainId: z.ZodNumber;
        logoURI: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }>;
    token1: z.ZodObject<{
        address: z.ZodString;
        symbol: z.ZodString;
        name: z.ZodString;
        decimals: z.ZodNumber;
        chainId: z.ZodNumber;
        logoURI: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }>;
    fee: z.ZodNumber;
    liquidity: z.ZodString;
    sqrtPriceX96: z.ZodOptional<z.ZodString>;
    tick: z.ZodOptional<z.ZodNumber>;
    reserve0: z.ZodOptional<z.ZodString>;
    reserve1: z.ZodOptional<z.ZodString>;
    protocol: z.ZodEnum<["uniswap-v2", "uniswap-v3", "sushiswap", "curve", "balancer"]>;
    version: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    address: string;
    token0: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    token1: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    fee: number;
    liquidity: string;
    protocol: "uniswap-v2" | "uniswap-v3" | "sushiswap" | "curve" | "balancer";
    sqrtPriceX96?: string | undefined;
    tick?: number | undefined;
    reserve0?: string | undefined;
    reserve1?: string | undefined;
    version?: string | undefined;
}, {
    address: string;
    token0: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    token1: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    fee: number;
    liquidity: string;
    protocol: "uniswap-v2" | "uniswap-v3" | "sushiswap" | "curve" | "balancer";
    sqrtPriceX96?: string | undefined;
    tick?: number | undefined;
    reserve0?: string | undefined;
    reserve1?: string | undefined;
    version?: string | undefined;
}>;
export type PoolInfo = z.infer<typeof PoolInfo>;
export declare const TradePath: z.ZodObject<{
    tokenIn: z.ZodObject<{
        address: z.ZodString;
        symbol: z.ZodString;
        name: z.ZodString;
        decimals: z.ZodNumber;
        chainId: z.ZodNumber;
        logoURI: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }>;
    tokenOut: z.ZodObject<{
        address: z.ZodString;
        symbol: z.ZodString;
        name: z.ZodString;
        decimals: z.ZodNumber;
        chainId: z.ZodNumber;
        logoURI: z.ZodOptional<z.ZodString>;
        tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    }, "strip", z.ZodTypeAny, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }, {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    }>;
    pools: z.ZodArray<z.ZodObject<{
        address: z.ZodString;
        token0: z.ZodObject<{
            address: z.ZodString;
            symbol: z.ZodString;
            name: z.ZodString;
            decimals: z.ZodNumber;
            chainId: z.ZodNumber;
            logoURI: z.ZodOptional<z.ZodString>;
            tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        }, "strip", z.ZodTypeAny, {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        }, {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        }>;
        token1: z.ZodObject<{
            address: z.ZodString;
            symbol: z.ZodString;
            name: z.ZodString;
            decimals: z.ZodNumber;
            chainId: z.ZodNumber;
            logoURI: z.ZodOptional<z.ZodString>;
            tags: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
        }, "strip", z.ZodTypeAny, {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        }, {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        }>;
        fee: z.ZodNumber;
        liquidity: z.ZodString;
        sqrtPriceX96: z.ZodOptional<z.ZodString>;
        tick: z.ZodOptional<z.ZodNumber>;
        reserve0: z.ZodOptional<z.ZodString>;
        reserve1: z.ZodOptional<z.ZodString>;
        protocol: z.ZodEnum<["uniswap-v2", "uniswap-v3", "sushiswap", "curve", "balancer"]>;
        version: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        address: string;
        token0: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        token1: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        fee: number;
        liquidity: string;
        protocol: "uniswap-v2" | "uniswap-v3" | "sushiswap" | "curve" | "balancer";
        sqrtPriceX96?: string | undefined;
        tick?: number | undefined;
        reserve0?: string | undefined;
        reserve1?: string | undefined;
        version?: string | undefined;
    }, {
        address: string;
        token0: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        token1: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        fee: number;
        liquidity: string;
        protocol: "uniswap-v2" | "uniswap-v3" | "sushiswap" | "curve" | "balancer";
        sqrtPriceX96?: string | undefined;
        tick?: number | undefined;
        reserve0?: string | undefined;
        reserve1?: string | undefined;
        version?: string | undefined;
    }>, "many">;
    expectedAmountOut: z.ZodString;
    priceImpact: z.ZodNumber;
    gasEstimate: z.ZodString;
    route: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    tokenIn: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    tokenOut: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    pools: {
        address: string;
        token0: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        token1: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        fee: number;
        liquidity: string;
        protocol: "uniswap-v2" | "uniswap-v3" | "sushiswap" | "curve" | "balancer";
        sqrtPriceX96?: string | undefined;
        tick?: number | undefined;
        reserve0?: string | undefined;
        reserve1?: string | undefined;
        version?: string | undefined;
    }[];
    expectedAmountOut: string;
    priceImpact: number;
    gasEstimate: string;
    route: string[];
}, {
    tokenIn: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    tokenOut: {
        symbol: string;
        chainId: number;
        name: string;
        decimals: number;
        address: string;
        logoURI?: string | undefined;
        tags?: string[] | undefined;
    };
    pools: {
        address: string;
        token0: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        token1: {
            symbol: string;
            chainId: number;
            name: string;
            decimals: number;
            address: string;
            logoURI?: string | undefined;
            tags?: string[] | undefined;
        };
        fee: number;
        liquidity: string;
        protocol: "uniswap-v2" | "uniswap-v3" | "sushiswap" | "curve" | "balancer";
        sqrtPriceX96?: string | undefined;
        tick?: number | undefined;
        reserve0?: string | undefined;
        reserve1?: string | undefined;
        version?: string | undefined;
    }[];
    expectedAmountOut: string;
    priceImpact: number;
    gasEstimate: string;
    route: string[];
}>;
export type TradePath = z.infer<typeof TradePath>;
export declare const DEXTradeParams: z.ZodObject<{
    tokenIn: z.ZodString;
    tokenOut: z.ZodString;
    amountIn: z.ZodString;
    amountOutMin: z.ZodString;
    to: z.ZodString;
    deadline: z.ZodNumber;
    slippageTolerance: z.ZodDefault<z.ZodNumber>;
    gasLimit: z.ZodOptional<z.ZodString>;
    gasPrice: z.ZodOptional<z.ZodString>;
    maxFeePerGas: z.ZodOptional<z.ZodString>;
    maxPriorityFeePerGas: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    amountOutMin: string;
    to: string;
    deadline: number;
    slippageTolerance: number;
    gasLimit?: string | undefined;
    gasPrice?: string | undefined;
    maxFeePerGas?: string | undefined;
    maxPriorityFeePerGas?: string | undefined;
}, {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    amountOutMin: string;
    to: string;
    deadline: number;
    slippageTolerance?: number | undefined;
    gasLimit?: string | undefined;
    gasPrice?: string | undefined;
    maxFeePerGas?: string | undefined;
    maxPriorityFeePerGas?: string | undefined;
}>;
export type DEXTradeParams = z.infer<typeof DEXTradeParams>;
export declare const GasEstimate: z.ZodObject<{
    gasLimit: z.ZodString;
    gasPrice: z.ZodString;
    maxFeePerGas: z.ZodOptional<z.ZodString>;
    maxPriorityFeePerGas: z.ZodOptional<z.ZodString>;
    estimatedCost: z.ZodString;
    estimatedCostUSD: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    gasLimit: string;
    gasPrice: string;
    estimatedCost: string;
    maxFeePerGas?: string | undefined;
    maxPriorityFeePerGas?: string | undefined;
    estimatedCostUSD?: number | undefined;
}, {
    gasLimit: string;
    gasPrice: string;
    estimatedCost: string;
    maxFeePerGas?: string | undefined;
    maxPriorityFeePerGas?: string | undefined;
    estimatedCostUSD?: number | undefined;
}>;
export type GasEstimate = z.infer<typeof GasEstimate>;
export declare const LiquidityParams: z.ZodObject<{
    token0: z.ZodString;
    token1: z.ZodString;
    amount0: z.ZodString;
    amount1: z.ZodString;
    amount0Min: z.ZodString;
    amount1Min: z.ZodString;
    to: z.ZodString;
    deadline: z.ZodNumber;
    fee: z.ZodOptional<z.ZodNumber>;
    tickLower: z.ZodOptional<z.ZodNumber>;
    tickUpper: z.ZodOptional<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    token0: string;
    token1: string;
    to: string;
    deadline: number;
    amount0: string;
    amount1: string;
    amount0Min: string;
    amount1Min: string;
    fee?: number | undefined;
    tickLower?: number | undefined;
    tickUpper?: number | undefined;
}, {
    token0: string;
    token1: string;
    to: string;
    deadline: number;
    amount0: string;
    amount1: string;
    amount0Min: string;
    amount1Min: string;
    fee?: number | undefined;
    tickLower?: number | undefined;
    tickUpper?: number | undefined;
}>;
export type LiquidityParams = z.infer<typeof LiquidityParams>;
export declare const FlashLoanParams: z.ZodObject<{
    asset: z.ZodString;
    amount: z.ZodString;
    params: z.ZodString;
    initiator: z.ZodString;
    referralCode: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    params: string;
    asset: string;
    amount: string;
    initiator: string;
    referralCode: number;
}, {
    params: string;
    asset: string;
    amount: string;
    initiator: string;
    referralCode?: number | undefined;
}>;
export type FlashLoanParams = z.infer<typeof FlashLoanParams>;
export interface DEXProtocol {
    readonly name: string;
    readonly version: string;
    readonly chainId: number;
    readonly routerAddress: string;
    readonly factoryAddress: string;
    getTokenPrice(tokenAddress: string, baseToken?: string): Promise<number>;
    getAmountOut(amountIn: string, tokenIn: string, tokenOut: string): Promise<string>;
    getAmountIn(amountOut: string, tokenIn: string, tokenOut: string): Promise<string>;
    findBestPath(tokenIn: string, tokenOut: string, amountIn: string): Promise<TradePath>;
    getAllPaths(tokenIn: string, tokenOut: string): Promise<TradePath[]>;
    getPool(token0: string, token1: string, fee?: number): Promise<PoolInfo | null>;
    getAllPools(token0?: string, token1?: string): Promise<PoolInfo[]>;
    buildSwapTransaction(params: DEXTradeParams): Promise<any>;
    estimateSwapGas(params: DEXTradeParams): Promise<GasEstimate>;
    buildAddLiquidityTransaction(params: LiquidityParams): Promise<any>;
    buildRemoveLiquidityTransaction(params: LiquidityParams): Promise<any>;
    buildFlashLoanTransaction?(params: FlashLoanParams): Promise<any>;
}
export declare const SupportedDEX: z.ZodEnum<["uniswap-v2", "uniswap-v3", "sushiswap", "pancakeswap", "curve", "balancer", "1inch", "dodo", "kyber", "bancor"]>;
export type SupportedDEX = z.infer<typeof SupportedDEX>;
export declare const SupportedChain: z.ZodEnum<["ethereum", "bsc", "polygon", "arbitrum", "optimism", "avalanche", "fantom", "solana"]>;
export type SupportedChain = z.infer<typeof SupportedChain>;
export declare class DEXError extends Error {
    readonly protocol: string;
    readonly code?: string | undefined;
    readonly details?: any | undefined;
    constructor(message: string, protocol: string, code?: string | undefined, details?: any | undefined);
}
export declare class InsufficientLiquidityError extends DEXError {
    constructor(message: string, protocol: string, details?: any);
}
export declare class SlippageExceededError extends DEXError {
    constructor(message: string, protocol: string, details?: any);
}
export declare class GasEstimationError extends DEXError {
    constructor(message: string, protocol: string, details?: any);
}
//# sourceMappingURL=dex.d.ts.map
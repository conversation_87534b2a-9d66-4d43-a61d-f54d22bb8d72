import { ethers } from 'ethers'
import {
  UnifiedExchange,
  ExchangeConfig,
  UnifiedOrder,
  OrderResult,
  OrderBook,
  Balance,
  MarketData,
  ExchangeError
} from '../../core/dist/index.js'
import {
  ChainConfig,
  TokenInfo,
  PoolInfo,
  TradePath,
  DEXTradeParams,
  DEXProtocol,
  DEXError,
  InsufficientLiquidityError
} from './types/dex'
import { UniswapV3Protocol } from './protocols/UniswapV3Protocol'
import { UniswapV2Protocol } from './protocols/UniswapV2Protocol'

export class DEXAdapter implements UnifiedExchange {
  private provider: ethers.Provider | null = null
  private wallet: ethers.Wallet | null = null
  private protocols: Map<string, DEXProtocol> = new Map()
  private chainConfig: ChainConfig | null = null
  private _id: string = ''
  private _name: string = ''
  private _type: 'cex' | 'dex' = 'dex'
  private subscriptions: Map<string, any> = new Map()
  private eventListeners: Map<string, any> = new Map()

  get id(): string {
    return this._id
  }

  get name(): string {
    return this._name
  }

  get type(): 'cex' | 'dex' {
    return this._type
  }

  async initialize(config: ExchangeConfig): Promise<void> {
    try {
      this._id = config.id
      this._name = config.name
      this._type = config.type

      if (!config.chainId || !config.rpcUrl) {
        throw new DEXError('Chain ID and RPC URL are required for DEX', config.id)
      }

      // 初始化区块链连接
      this.provider = new ethers.JsonRpcProvider(config.rpcUrl)

      if (config.privateKey) {
        this.wallet = new ethers.Wallet(config.privateKey, this.provider)
      }

      // 设置链配置
      this.chainConfig = {
        chainId: config.chainId,
        name: config.name,
        rpcUrl: config.rpcUrl,
        nativeCurrency: {
          name: 'Ether',
          symbol: 'ETH',
          decimals: 18
        },
        testnet: config.sandbox || false
      }

      // 初始化支持的协议
      await this.initializeProtocols()

    } catch (error) {
      throw this.handleError(error, config.id)
    }
  }

  isInitialized(): boolean {
    return this.provider !== null && this.chainConfig !== null
  }

  async fetchMarkets(): Promise<string[]> {
    this.ensureInitialized()

    // DEX没有固定的市场列表，返回常用交易对
    const commonPairs = [
      'ETH/USDC',
      'ETH/USDT',
      'ETH/DAI',
      'WBTC/ETH',
      'USDC/USDT'
    ]

    return commonPairs
  }

  async fetchTicker(symbol: string): Promise<MarketData> {
    this.ensureInitialized()

    try {
      const [baseSymbol, quoteSymbol] = symbol.split('/')
      const baseToken = await this.getTokenInfo(baseSymbol)
      const quoteToken = await this.getTokenInfo(quoteSymbol)

      if (!baseToken || !quoteToken) {
        throw new DEXError(`Token not found for pair ${symbol}`, this.id)
      }

      // 从多个协议获取价格
      const prices = await this.getTokenPriceFromMultipleProtocols(baseToken.address, quoteToken.address)

      if (prices.length === 0) {
        throw new InsufficientLiquidityError(`No liquidity found for ${symbol}`, this.id)
      }

      // 计算平均价格
      const avgPrice = prices.reduce((sum, price) => sum + price, 0) / prices.length
      const timestamp = Date.now()

      return {
        symbol,
        timestamp,
        bid: avgPrice * 0.999, // 模拟买价（考虑滑点）
        ask: avgPrice * 1.001, // 模拟卖价（考虑滑点）
        last: avgPrice,
        volume: 0, // DEX volume需要从链上事件获取
        high: avgPrice * 1.05, // 模拟24h高点
        low: avgPrice * 0.95,  // 模拟24h低点
        open: avgPrice * 0.98, // 模拟开盘价
        close: avgPrice
      }
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchOrderBook(symbol: string, limit?: number): Promise<OrderBook> {
    this.ensureInitialized()

    try {
      const [baseSymbol, quoteSymbol] = symbol.split('/')
      const baseToken = await this.getTokenInfo(baseSymbol)
      const quoteToken = await this.getTokenInfo(quoteSymbol)

      if (!baseToken || !quoteToken) {
        throw new DEXError(`Token not found for pair ${symbol}`, this.id)
      }

      // DEX没有传统的订单簿，这里模拟基于AMM的流动性
      const pools = await this.getAllPoolsForPair(baseToken.address, quoteToken.address)
      const orderBook = await this.simulateOrderBookFromPools(pools, baseToken.address, quoteToken.address)

      return {
        symbol,
        timestamp: Date.now(),
        bids: orderBook.bids,
        asks: orderBook.asks
      }
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchTrades(symbol: string, limit?: number): Promise<any[]> {
    // 需要从链上事件日志获取交易历史
    // 这里返回空数组，实际实现需要查询Swap事件
    return []
  }

  async fetchOHLCV(symbol: string, timeframe: string, since?: number, limit?: number): Promise<any[]> {
    // 需要聚合链上交易数据生成K线
    // 这里返回空数组，实际实现需要复杂的数据处理
    return []
  }

  async fetchBalance(): Promise<Record<string, Balance>> {
    this.ensureInitialized()

    if (!this.wallet) {
      throw new DEXError('Wallet not configured', this.id)
    }

    try {
      const balances: Record<string, Balance> = {}

      // 获取ETH余额
      const ethBalance = await this.provider!.getBalance(this.wallet.address)
      balances['ETH'] = {
        currency: 'ETH',
        free: parseFloat(ethers.formatEther(ethBalance)),
        used: 0,
        total: parseFloat(ethers.formatEther(ethBalance))
      }

      // 获取常用ERC20代币余额
      const commonTokens = ['USDC', 'USDT', 'DAI', 'WBTC']
      for (const tokenSymbol of commonTokens) {
        const tokenInfo = await this.getTokenInfo(tokenSymbol)
        if (tokenInfo) {
          const balance = await this.getTokenBalance(tokenInfo.address, this.wallet.address)
          balances[tokenSymbol] = {
            currency: tokenSymbol,
            free: balance,
            used: 0,
            total: balance
          }
        }
      }

      return balances
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchOpenOrders(symbol?: string): Promise<OrderResult[]> {
    // DEX没有传统的挂单概念，返回空数组
    return []
  }

  async fetchClosedOrders(symbol?: string, since?: number, limit?: number): Promise<OrderResult[]> {
    // 需要从链上交易历史获取
    return []
  }

  async createOrder(order: UnifiedOrder): Promise<OrderResult> {
    this.ensureInitialized()

    if (!this.wallet) {
      throw new DEXError('Wallet not configured for trading', this.id)
    }

    try {
      const [baseSymbol, quoteSymbol] = order.symbol.split('/')
      const baseToken = await this.getTokenInfo(baseSymbol)
      const quoteToken = await this.getTokenInfo(quoteSymbol)

      if (!baseToken || !quoteToken) {
        throw new DEXError(`Token not found for pair ${order.symbol}`, this.id)
      }

      // 构建交易参数
      const tradeParams: DEXTradeParams = {
        tokenIn: order.side === 'buy' ? quoteToken.address : baseToken.address,
        tokenOut: order.side === 'buy' ? baseToken.address : quoteToken.address,
        amountIn: ethers.parseUnits(order.amount.toString(),
          order.side === 'buy' ? quoteToken.decimals : baseToken.decimals).toString(),
        amountOutMin: '0', // 需要根据滑点计算
        to: this.wallet.address,
        deadline: Math.floor(Date.now() / 1000) + 1200, // 20分钟
        slippageTolerance: 0.005 // 0.5%
      }

      // 寻找最佳交易路径
      const bestPath = await this.findBestTradePath(
        tradeParams.tokenIn,
        tradeParams.tokenOut,
        tradeParams.amountIn
      )

      if (!bestPath) {
        throw new InsufficientLiquidityError(`No trading path found for ${order.symbol}`, this.id)
      }

      // 执行交易
      const txHash = await this.executeSwap(tradeParams, bestPath)

      return {
        id: txHash,
        clientOrderId: order.clientOrderId,
        symbol: order.symbol,
        side: order.side,
        type: order.type,
        amount: order.amount,
        price: order.price,
        filled: 0, // 需要等待交易确认
        remaining: order.amount,
        status: 'pending',
        timestamp: Date.now()
      }
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async cancelOrder(id: string, symbol?: string): Promise<OrderResult> {
    // DEX交易一旦提交无法取消，除非交易失败
    throw new DEXError('DEX orders cannot be cancelled once submitted', this.id)
  }

  async cancelAllOrders(symbol?: string): Promise<OrderResult[]> {
    // DEX不支持批量取消订单
    throw new DEXError('DEX does not support order cancellation', this.id)
  }

  // DEX特有方法实现
  async estimateGas(transaction: any): Promise<bigint> {
    this.ensureInitialized()

    if (!this.provider) {
      throw new DEXError('Provider not initialized', this.id)
    }

    try {
      return await this.provider.estimateGas(transaction)
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async getTokenPrice(tokenAddress: string, baseToken?: string): Promise<number> {
    this.ensureInitialized()

    const baseTokenAddress = baseToken || await this.getUSDCAddress()
    const prices = await this.getTokenPriceFromMultipleProtocols(tokenAddress, baseTokenAddress)

    if (prices.length === 0) {
      throw new InsufficientLiquidityError(`No price found for token ${tokenAddress}`, this.id)
    }

    return prices.reduce((sum, price) => sum + price, 0) / prices.length
  }

  async getLiquidityPools(tokenA: string, tokenB: string): Promise<PoolInfo[]> {
    return await this.getAllPoolsForPair(tokenA, tokenB)
  }

  // WebSocket订阅方法 - 实现基于区块链事件的实时数据订阅
  async subscribeToTicker(symbol: string, callback: (data: MarketData) => void): Promise<void> {
    this.ensureInitialized()

    const [tokenA, tokenB] = symbol.split('/')
    const tokenAAddress = await this.getTokenAddress(tokenA)
    const tokenBAddress = await this.getTokenAddress(tokenB)

    // 获取所有相关的流动性池
    const pools = await this.getAllPoolsForPair(tokenAAddress, tokenBAddress)

    // 为每个池订阅Swap事件
    for (const pool of pools) {
      await this.subscribeToPoolSwapEvents(pool, (swapData) => {
        // 计算新的价格并调用回调
        this.calculatePriceFromSwap(swapData, tokenAAddress, tokenBAddress)
          .then(price => {
            const marketData: MarketData = {
              symbol,
              timestamp: Date.now(),
              bid: price * 0.999, // 模拟买价
              ask: price * 1.001, // 模拟卖价
              last: price,
              volume: 0, // 需要从历史数据计算
              high: price,
              low: price,
              open: price * 0.999,
              close: price
            }
            callback(marketData)
          })
          .catch(error => {
            console.error('Error calculating price from swap:', error)
          })
      })
    }

    // 存储订阅信息
    const subscriptionKey = `ticker:${symbol}`
    this.subscriptions.set(subscriptionKey, { type: 'ticker', symbol, callback, pools })
  }

  async subscribeToOrderBook(symbol: string, callback: (data: OrderBook) => void): Promise<void> {
    this.ensureInitialized()

    const [tokenA, tokenB] = symbol.split('/')
    const tokenAAddress = await this.getTokenAddress(tokenA)
    const tokenBAddress = await this.getTokenAddress(tokenB)

    // 获取流动性池并模拟订单簿
    const updateOrderBook = async () => {
      try {
        const pools = await this.getAllPoolsForPair(tokenAAddress, tokenBAddress)
        const orderBook = await this.simulateOrderBookFromPools(pools, tokenAAddress, tokenBAddress)
        callback(orderBook)
      } catch (error) {
        console.error('Error updating order book:', error)
      }
    }

    // 初始订单簿
    await updateOrderBook()

    // 订阅池状态变化事件
    const pools = await this.getAllPoolsForPair(tokenAAddress, tokenBAddress)
    for (const pool of pools) {
      await this.subscribeToPoolEvents(pool, updateOrderBook)
    }

    // 存储订阅信息
    const subscriptionKey = `orderbook:${symbol}`
    this.subscriptions.set(subscriptionKey, { type: 'orderbook', symbol, callback, pools })
  }

  async subscribeToTrades(symbol: string, callback: (data: any) => void): Promise<void> {
    this.ensureInitialized()

    const [tokenA, tokenB] = symbol.split('/')
    const tokenAAddress = await this.getTokenAddress(tokenA)
    const tokenBAddress = await this.getTokenAddress(tokenB)

    // 获取所有相关的流动性池
    const pools = await this.getAllPoolsForPair(tokenAAddress, tokenBAddress)

    // 为每个池订阅Swap事件
    for (const pool of pools) {
      await this.subscribeToPoolSwapEvents(pool, (swapData) => {
        const trade = {
          id: swapData.transactionHash,
          symbol,
          side: swapData.amount0 > 0 ? 'buy' : 'sell',
          amount: Math.abs(swapData.amount0),
          price: Math.abs(swapData.amount1 / swapData.amount0),
          timestamp: swapData.timestamp,
          fee: swapData.fee || 0,
          protocol: pool.protocol
        }
        callback(trade)
      })
    }

    // 存储订阅信息
    const subscriptionKey = `trades:${symbol}`
    this.subscriptions.set(subscriptionKey, { type: 'trades', symbol, callback, pools })
  }

  async unsubscribe(symbol: string, type: 'ticker' | 'orderbook' | 'trades'): Promise<void> {
    const subscriptionKey = `${type}:${symbol}`
    const subscription = this.subscriptions.get(subscriptionKey)

    if (subscription) {
      // 取消事件监听
      for (const pool of subscription.pools) {
        await this.unsubscribeFromPoolEvents(pool)
      }

      this.subscriptions.delete(subscriptionKey)
    }
  }

  // 私有方法
  private async initializeProtocols(): Promise<void> {
    if (!this.chainConfig || !this.provider) return

    // 初始化Uniswap V3
    const uniV3 = new UniswapV3Protocol(this.chainConfig, this.provider)
    this.protocols.set('uniswap-v3', uniV3)

    // 初始化Uniswap V2
    const uniV2 = new UniswapV2Protocol(this.chainConfig, this.provider)
    this.protocols.set('uniswap-v2', uniV2)

    // 可以添加更多协议...
  }

  private ensureInitialized(): void {
    if (!this.isInitialized()) {
      throw new DEXError('DEX adapter not initialized', this.id)
    }
  }

  private async getTokenInfo(symbol: string): Promise<TokenInfo | null> {
    // 这里应该从代币列表或链上查询代币信息
    // 简化实现，返回常用代币信息
    const commonTokens: Record<string, TokenInfo> = {
      'ETH': {
        address: '******************************************',
        symbol: 'ETH',
        name: 'Ether',
        decimals: 18,
        chainId: this.chainConfig!.chainId
      },
      'USDC': {
        address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
        symbol: 'USDC',
        name: 'USD Coin',
        decimals: 6,
        chainId: this.chainConfig!.chainId
      }
      // 添加更多代币...
    }

    return commonTokens[symbol] || null
  }

  private async getTokenPriceFromMultipleProtocols(tokenIn: string, tokenOut: string): Promise<number[]> {
    const prices: number[] = []

    for (const protocol of this.protocols.values()) {
      try {
        const price = await protocol.getTokenPrice(tokenIn, tokenOut)
        prices.push(price)
      } catch (error) {
        // 忽略单个协议的错误
        console.warn(`Failed to get price from ${protocol.name}:`, error)
      }
    }

    return prices
  }

  private async getAllPoolsForPair(tokenA: string, tokenB: string): Promise<PoolInfo[]> {
    const allPools: PoolInfo[] = []

    for (const protocol of this.protocols.values()) {
      try {
        const pools = await protocol.getAllPools(tokenA, tokenB)
        allPools.push(...pools)
      } catch (error) {
        console.warn(`Failed to get pools from ${protocol.name}:`, error)
      }
    }

    return allPools
  }



  private async findBestTradePath(tokenIn: string, tokenOut: string, amountIn: string): Promise<TradePath | null> {
    let bestPath: TradePath | null = null
    let bestAmountOut = '0'

    for (const protocol of this.protocols.values()) {
      try {
        const path = await protocol.findBestPath(tokenIn, tokenOut, amountIn)
        if (!bestPath || parseFloat(path.expectedAmountOut) > parseFloat(bestAmountOut)) {
          bestPath = path
          bestAmountOut = path.expectedAmountOut
        }
      } catch (error) {
        console.warn(`Failed to find path in ${protocol.name}:`, error)
      }
    }

    return bestPath
  }

  private async executeSwap(params: DEXTradeParams, path: TradePath): Promise<string> {
    // 这里需要实现具体的交易执行逻辑
    // 根据不同协议构建和发送交易
    throw new DEXError('Swap execution not implemented', this.id)
  }

  private async getTokenBalance(tokenAddress: string, walletAddress: string): Promise<number> {
    // 实现ERC20代币余额查询
    return 0
  }

  private async getUSDCAddress(): Promise<string> {
    // 返回USDC地址作为默认基准代币
    return '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505'
  }

  // 新增的实时数据订阅相关方法
  private async getTokenAddress(symbol: string): Promise<string> {
    const tokenInfo = await this.getTokenInfo(symbol)
    if (!tokenInfo) {
      throw new DEXError(`Token ${symbol} not found`, this.id)
    }
    return tokenInfo.address
  }

  private async subscribeToPoolSwapEvents(pool: PoolInfo, callback: (swapData: any) => void): Promise<void> {
    if (!this.provider) return

    try {
      // 创建合约实例
      const contract = new ethers.Contract(pool.address, [
        'event Swap(address indexed sender, address indexed to, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)'
      ], this.provider)

      // 监听Swap事件
      const listener = (sender: string, to: string, amount0: bigint, amount1: bigint, sqrtPriceX96: bigint, liquidity: bigint, tick: number, event: any) => {
        const swapData = {
          transactionHash: event.transactionHash,
          blockNumber: event.blockNumber,
          sender,
          to,
          amount0: Number(amount0),
          amount1: Number(amount1),
          sqrtPriceX96: sqrtPriceX96.toString(),
          liquidity: liquidity.toString(),
          tick,
          timestamp: Date.now(),
          pool: pool.address,
          protocol: pool.protocol
        }
        callback(swapData)
      }

      contract.on('Swap', listener)

      // 存储监听器以便后续取消
      const listenerKey = `${pool.address}:swap`
      this.eventListeners.set(listenerKey, { contract, listener, event: 'Swap' })

    } catch (error) {
      console.error(`Failed to subscribe to swap events for pool ${pool.address}:`, error)
    }
  }

  private async subscribeToPoolEvents(pool: PoolInfo, callback: () => void): Promise<void> {
    if (!this.provider) return

    try {
      // 创建合约实例
      const contract = new ethers.Contract(pool.address, [
        'event Mint(address indexed sender, address indexed owner, int24 indexed tickLower, int24 indexed tickUpper, uint128 amount, uint256 amount0, uint256 amount1)',
        'event Burn(address indexed owner, int24 indexed tickLower, int24 indexed tickUpper, uint128 amount, uint256 amount0, uint256 amount1)',
        'event Swap(address indexed sender, address indexed to, int256 amount0, int256 amount1, uint160 sqrtPriceX96, uint128 liquidity, int24 tick)'
      ], this.provider)

      // 监听流动性变化事件
      const mintListener = () => callback()
      const burnListener = () => callback()
      const swapListener = () => callback()

      contract.on('Mint', mintListener)
      contract.on('Burn', burnListener)
      contract.on('Swap', swapListener)

      // 存储监听器
      const listenerKey = `${pool.address}:all`
      this.eventListeners.set(listenerKey, {
        contract,
        listeners: [
          { event: 'Mint', listener: mintListener },
          { event: 'Burn', listener: burnListener },
          { event: 'Swap', listener: swapListener }
        ]
      })

    } catch (error) {
      console.error(`Failed to subscribe to pool events for ${pool.address}:`, error)
    }
  }

  private async unsubscribeFromPoolEvents(pool: PoolInfo): Promise<void> {
    const swapListenerKey = `${pool.address}:swap`
    const allListenerKey = `${pool.address}:all`

    // 取消Swap事件监听
    const swapListener = this.eventListeners.get(swapListenerKey)
    if (swapListener) {
      swapListener.contract.off(swapListener.event, swapListener.listener)
      this.eventListeners.delete(swapListenerKey)
    }

    // 取消所有事件监听
    const allListener = this.eventListeners.get(allListenerKey)
    if (allListener) {
      for (const { event, listener } of allListener.listeners) {
        allListener.contract.off(event, listener)
      }
      this.eventListeners.delete(allListenerKey)
    }
  }

  private async calculatePriceFromSwap(swapData: any, tokenA: string, tokenB: string): Promise<number> {
    // 根据Swap事件数据计算价格
    const { amount0, amount1 } = swapData

    if (amount0 === 0 || amount1 === 0) {
      throw new Error('Invalid swap amounts')
    }

    // 计算价格 (tokenB/tokenA)
    return Math.abs(amount1 / amount0)
  }

  private async simulateOrderBookFromPools(pools: PoolInfo[], tokenA: string, tokenB: string): Promise<OrderBook> {
    // 基于AMM流动性模拟订单簿
    const bids: { price: number; amount: number }[] = []
    const asks: { price: number; amount: number }[] = []

    for (const pool of pools) {
      try {
        // 获取池的当前状态
        const currentPrice = await this.getPoolPrice(pool, tokenA, tokenB)

        // 模拟不同价格点的流动性
        for (let i = 1; i <= 10; i++) {
          const priceOffset = i * 0.001 // 0.1% 价格间隔

          // 买单 (低于当前价格)
          const bidPrice = currentPrice * (1 - priceOffset)
          const bidAmount = await this.estimateSwapAmount(pool, tokenB, tokenA, bidPrice)
          if (bidAmount > 0) {
            bids.push({ price: bidPrice, amount: bidAmount })
          }

          // 卖单 (高于当前价格)
          const askPrice = currentPrice * (1 + priceOffset)
          const askAmount = await this.estimateSwapAmount(pool, tokenA, tokenB, askPrice)
          if (askAmount > 0) {
            asks.push({ price: askPrice, amount: askAmount })
          }
        }
      } catch (error) {
        console.warn(`Failed to simulate order book for pool ${pool.address}:`, error)
      }
    }

    // 排序并合并
    bids.sort((a, b) => b.price - a.price) // 买单按价格降序
    asks.sort((a, b) => a.price - b.price) // 卖单按价格升序

    return {
      symbol: `${tokenA}/${tokenB}`,
      timestamp: Date.now(),
      bids: bids.slice(0, 20), // 取前20档
      asks: asks.slice(0, 20)
    }
  }

  private async getPoolPrice(pool: PoolInfo, tokenA: string, tokenB: string): Promise<number> {
    // 获取池的当前价格
    const protocol = this.protocols.get(pool.protocol)
    if (protocol) {
      return await protocol.getTokenPrice(tokenA, tokenB)
    }
    return 0
  }

  private async estimateSwapAmount(pool: PoolInfo, tokenIn: string, tokenOut: string, targetPrice: number): Promise<number> {
    // 估算在目标价格下的可交易数量
    // 这里简化实现，实际需要复杂的AMM数学计算
    return Math.random() * 1000 // 模拟数据
  }

  private handleError(error: any, exchangeId: string): ExchangeError {
    if (error instanceof DEXError) {
      return new ExchangeError(error.message, exchangeId, error.code, error.details)
    } else {
      return new ExchangeError(error.message || 'Unknown DEX error', exchangeId, 'UNKNOWN_ERROR', error)
    }
  }
}

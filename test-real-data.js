const axios = require('axios');

console.log('🔍 测试真实数据API连接');
console.log('='.repeat(50));

async function testBinanceAPI() {
  try {
    console.log('📊 测试Binance API...');
    
    // 测试获取BTC价格
    const btcResponse = await axios.get('https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT');
    const btcData = btcResponse.data;
    
    console.log('✅ BTC/USDT 数据:');
    console.log(`   价格: $${parseFloat(btcData.lastPrice).toFixed(2)}`);
    console.log(`   24h变化: ${parseFloat(btcData.priceChangePercent).toFixed(2)}%`);
    console.log(`   成交量: ${parseFloat(btcData.volume).toFixed(2)} BTC`);
    console.log(`   最高: $${parseFloat(btcData.highPrice).toFixed(2)}`);
    console.log(`   最低: $${parseFloat(btcData.lowPrice).toFixed(2)}`);
    
    // 测试获取ETH价格
    const ethResponse = await axios.get('https://api.binance.com/api/v3/ticker/24hr?symbol=ETHUSDT');
    const ethData = ethResponse.data;
    
    console.log('\n✅ ETH/USDT 数据:');
    console.log(`   价格: $${parseFloat(ethData.lastPrice).toFixed(2)}`);
    console.log(`   24h变化: ${parseFloat(ethData.priceChangePercent).toFixed(2)}%`);
    console.log(`   成交量: ${parseFloat(ethData.volume).toFixed(2)} ETH`);
    
    // 测试订单簿数据
    const orderBookResponse = await axios.get('https://api.binance.com/api/v3/depth?symbol=BTCUSDT&limit=5');
    const orderBookData = orderBookResponse.data;
    
    console.log('\n✅ BTC/USDT 订单簿 (前5档):');
    console.log('   卖单:');
    orderBookData.asks.slice(0, 3).forEach((ask, i) => {
      console.log(`     ${i+1}. $${parseFloat(ask[0]).toFixed(2)} - ${parseFloat(ask[1]).toFixed(4)} BTC`);
    });
    console.log('   买单:');
    orderBookData.bids.slice(0, 3).forEach((bid, i) => {
      console.log(`     ${i+1}. $${parseFloat(bid[0]).toFixed(2)} - ${parseFloat(bid[1]).toFixed(4)} BTC`);
    });
    
    // 测试K线数据
    const klineResponse = await axios.get('https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=1h&limit=3');
    const klineData = klineResponse.data;
    
    console.log('\n✅ BTC/USDT K线数据 (最近3小时):');
    klineData.forEach((kline, i) => {
      const time = new Date(kline[0]).toLocaleTimeString();
      console.log(`   ${time}: 开盘 $${parseFloat(kline[1]).toFixed(2)}, 收盘 $${parseFloat(kline[4]).toFixed(2)}`);
    });
    
  } catch (error) {
    console.error('❌ Binance API测试失败:', error.message);
  }
}

async function testLocalAPI() {
  try {
    console.log('\n🌐 测试本地API...');
    
    // 测试健康检查
    const healthResponse = await axios.get('http://localhost:3001/');
    console.log('✅ 本地API健康检查:', healthResponse.data.message);
    
    // 测试市场数据端点
    const marketDataResponse = await axios.get('http://localhost:3001/api/v1/market-data/BTC/USDT');
    console.log('✅ 本地市场数据API响应:', marketDataResponse.status === 200 ? '正常' : '异常');
    
  } catch (error) {
    console.error('❌ 本地API测试失败:', error.message);
  }
}

async function runTests() {
  await testBinanceAPI();
  await testLocalAPI();
  
  console.log('\n🎉 真实数据测试完成!');
  console.log('现在系统使用的是真实的Binance市场数据，不再是模拟数据。');
}

runTests();

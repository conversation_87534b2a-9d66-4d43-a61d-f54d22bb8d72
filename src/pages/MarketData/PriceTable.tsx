import { useState } from 'react'
import { useMarketDataStore } from '@/store/marketDataStore'
import { useWebSocket } from '@/hooks/useWebSocket'
import { ArrowUpIcon, ArrowDownIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/solid'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export function PriceTable() {
  const marketData = useMarketDataStore(state => state.marketData)
  const subscribedSymbols = useMarketDataStore(state => state.subscribedSymbols)
  const setSubscribed = useMarketDataStore(state => state.setSubscribed)
  const { subscribe, unsubscribe } = useWebSocket()
  
  const [sortBy, setSortBy] = useState<'symbol' | 'price' | 'change' | 'volume'>('volume')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 获取市场数据数组并排序
  const marketDataArray = Object.values(marketData).sort((a, b) => {
    let aValue: number | string = 0
    let bValue: number | string = 0

    switch (sortBy) {
      case 'symbol':
        aValue = a.symbol
        bValue = b.symbol
        break
      case 'price':
        aValue = a.last
        bValue = b.last
        break
      case 'change':
        aValue = a.changePercent || 0
        bValue = b.changePercent || 0
        break
      case 'volume':
        aValue = a.volume
        bValue = b.volume
        break
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue)
    }

    return sortOrder === 'asc' ? (aValue as number) - (bValue as number) : (bValue as number) - (aValue as number)
  })

  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('desc')
    }
  }

  const handleSubscriptionToggle = (symbol: string) => {
    const isSubscribed = subscribedSymbols.has(symbol)
    
    if (isSubscribed) {
      unsubscribe('price', symbol)
      setSubscribed(symbol, false)
    } else {
      subscribe('price', symbol)
      setSubscribed(symbol, true)
    }
  }

  const formatPrice = (price: number) => {
    if (price >= 1000) return price.toFixed(2)
    if (price >= 1) return price.toFixed(4)
    return price.toFixed(6)
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`
    return volume.toFixed(0)
  }

  const formatChange = (change?: number) => {
    if (change === undefined) return '0.00%'
    return `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`
  }

  const SortButton = ({ column, children }: { column: typeof sortBy; children: React.ReactNode }) => (
    <button
      onClick={() => handleSort(column)}
      className="flex items-center space-x-1 text-left hover:text-gray-900"
    >
      <span>{children}</span>
      {sortBy === column && (
        <span className="text-primary-600">
          {sortOrder === 'asc' ? '↑' : '↓'}
        </span>
      )}
    </button>
  )

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900">实时价格表</h3>
        <p className="mt-1 text-sm text-gray-500">
          点击交易对名称可订阅/取消订阅实时数据
        </p>
      </div>
      <div className="card-body p-0">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <SortButton column="symbol">交易对</SortButton>
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <SortButton column="price">价格</SortButton>
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <SortButton column="change">24h变化</SortButton>
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <SortButton column="volume">成交量</SortButton>
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  高/低
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  更新时间
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  订阅
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {marketDataArray.map((data) => {
                const isSubscribed = subscribedSymbols.has(data.symbol)
                return (
                  <tr key={data.symbol} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-gray-900">
                          {data.symbol}
                        </div>
                        {isSubscribed && (
                          <div className="ml-2 w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className="text-sm font-medium text-gray-900">
                        ${formatPrice(data.last)}
                      </div>
                      <div className="text-xs text-gray-500">
                        买: ${formatPrice(data.bid)} 卖: ${formatPrice(data.ask)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right">
                      <div className={`inline-flex items-center text-sm font-medium ${
                        (data.changePercent || 0) >= 0 
                          ? 'text-success-600' 
                          : 'text-danger-600'
                      }`}>
                        {(data.changePercent || 0) >= 0 ? (
                          <ArrowUpIcon className="w-3 h-3 mr-1" />
                        ) : (
                          <ArrowDownIcon className="w-3 h-3 mr-1" />
                        )}
                        {formatChange(data.changePercent)}
                      </div>
                      {data.change !== undefined && (
                        <div className="text-xs text-gray-500">
                          ${Math.abs(data.change).toFixed(2)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm text-gray-900">
                      {formatVolume(data.volume)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-xs text-gray-500">
                      <div>${formatPrice(data.high)}</div>
                      <div>${formatPrice(data.low)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-xs text-gray-500">
                      {formatDistanceToNow(new Date(data.timestamp), { 
                        addSuffix: true,
                        locale: zhCN 
                      })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <button
                        onClick={() => handleSubscriptionToggle(data.symbol)}
                        className={`inline-flex items-center p-1 rounded-full text-xs font-medium transition-colors ${
                          isSubscribed
                            ? 'bg-danger-100 text-danger-800 hover:bg-danger-200'
                            : 'bg-success-100 text-success-800 hover:bg-success-200'
                        }`}
                      >
                        {isSubscribed ? (
                          <MinusIcon className="w-4 h-4" />
                        ) : (
                          <PlusIcon className="w-4 h-4" />
                        )}
                      </button>
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
        
        {marketDataArray.length === 0 && (
          <div className="p-6 text-center text-gray-500">
            <div className="animate-pulse">
              <div className="w-8 h-8 bg-gray-200 rounded-full mx-auto mb-4"></div>
              <p>正在加载市场数据...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

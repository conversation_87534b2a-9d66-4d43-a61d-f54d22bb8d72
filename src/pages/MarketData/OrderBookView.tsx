import { useEffect } from 'react'
import { useMarketDataStore } from '@/store/marketDataStore'
import { useWebSocket } from '@/hooks/useWebSocket'

interface OrderBookViewProps {
  symbol: string
}

export function OrderBookView({ symbol }: OrderBookViewProps) {
  const orderBook = useMarketDataStore(state => state.orderBooks[symbol])
  const { subscribe, unsubscribe } = useWebSocket()

  useEffect(() => {
    // 订阅订单簿数据
    subscribe('orderbook', symbol)
    
    return () => {
      unsubscribe('orderbook', symbol)
    }
  }, [symbol, subscribe, unsubscribe])

  const formatPrice = (price: number) => {
    if (price >= 1000) return price.toFixed(2)
    if (price >= 1) return price.toFixed(4)
    return price.toFixed(6)
  }

  const formatAmount = (amount: number) => {
    if (amount >= 1000) return `${(amount / 1000).toFixed(2)}K`
    if (amount >= 1) return amount.toFixed(4)
    return amount.toFixed(6)
  }

  // 模拟订单簿数据（如果没有实时数据）
  const mockOrderBook = {
    symbol,
    timestamp: Date.now(),
    bids: Array.from({ length: 10 }, (_, i) => ({
      price: 45000 - (i * 10),
      amount: Math.random() * 5
    })),
    asks: Array.from({ length: 10 }, (_, i) => ({
      price: 45010 + (i * 10),
      amount: Math.random() * 5
    }))
  }

  const displayOrderBook = orderBook || mockOrderBook

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* 买单 (Bids) */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">买单 (Bids)</h3>
          <p className="mt-1 text-sm text-gray-500">按价格从高到低排序</p>
        </div>
        <div className="card-body p-0">
          <div className="overflow-hidden">
            <table className="min-w-full">
              <thead className="bg-success-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-success-800 uppercase tracking-wider">
                    价格 (USDT)
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-success-800 uppercase tracking-wider">
                    数量
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-success-800 uppercase tracking-wider">
                    总额
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {displayOrderBook.bids.slice(0, 15).map((bid, index) => (
                  <tr key={index} className="hover:bg-success-50">
                    <td className="px-4 py-2 text-sm font-medium text-success-600">
                      ${formatPrice(bid.price)}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 text-right">
                      {formatAmount(bid.amount)}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 text-right">
                      ${(bid.price * bid.amount).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 卖单 (Asks) */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">卖单 (Asks)</h3>
          <p className="mt-1 text-sm text-gray-500">按价格从低到高排序</p>
        </div>
        <div className="card-body p-0">
          <div className="overflow-hidden">
            <table className="min-w-full">
              <thead className="bg-danger-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-danger-800 uppercase tracking-wider">
                    价格 (USDT)
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-danger-800 uppercase tracking-wider">
                    数量
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-danger-800 uppercase tracking-wider">
                    总额
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {displayOrderBook.asks.slice(0, 15).map((ask, index) => (
                  <tr key={index} className="hover:bg-danger-50">
                    <td className="px-4 py-2 text-sm font-medium text-danger-600">
                      ${formatPrice(ask.price)}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 text-right">
                      {formatAmount(ask.amount)}
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-900 text-right">
                      ${(ask.price * ask.amount).toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 订单簿统计 */}
      <div className="lg:col-span-2">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">订单簿统计</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-success-600">
                  ${formatPrice(displayOrderBook.bids[0]?.price || 0)}
                </div>
                <div className="text-sm text-gray-500">最高买价</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-danger-600">
                  ${formatPrice(displayOrderBook.asks[0]?.price || 0)}
                </div>
                <div className="text-sm text-gray-500">最低卖价</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  ${formatPrice(Math.abs((displayOrderBook.asks[0]?.price || 0) - (displayOrderBook.bids[0]?.price || 0)))}
                </div>
                <div className="text-sm text-gray-500">买卖价差</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatAmount(
                    displayOrderBook.bids.reduce((sum, bid) => sum + bid.amount, 0) +
                    displayOrderBook.asks.reduce((sum, ask) => sum + ask.amount, 0)
                  )}
                </div>
                <div className="text-sm text-gray-500">总挂单量</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

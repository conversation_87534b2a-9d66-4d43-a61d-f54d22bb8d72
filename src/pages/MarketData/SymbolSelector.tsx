import { useState } from 'react'
import { ChevronDownIcon } from '@heroicons/react/24/outline'
import { useMarketDataStore } from '@/store/marketDataStore'

interface SymbolSelectorProps {
  selectedSymbol: string
  onSymbolChange: (symbol: string) => void
}

const popularSymbols = [
  'BTC/USDT',
  'ETH/USDT',
  'BNB/USDT',
  'ADA/USDT',
  'SOL/USDT',
  'DOT/USDT',
  'MATIC/USDT',
  'AVAX/USDT'
]

export function SymbolSelector({ selectedSymbol, onSymbolChange }: SymbolSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const marketData = useMarketDataStore(state => state.marketData)
  
  // 获取所有可用的交易对
  const availableSymbols = Object.keys(marketData)
  const allSymbols = Array.from(new Set([...popularSymbols, ...availableSymbols])).sort()

  const handleSelect = (symbol: string) => {
    onSymbolChange(symbol)
    setIsOpen(false)
  }

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="relative w-40 bg-white border border-gray-300 rounded-md pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
      >
        <span className="block truncate font-medium">{selectedSymbol}</span>
        <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
        </span>
      </button>

      {isOpen && (
        <>
          {/* 遮罩层 */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* 下拉菜单 */}
          <div className="absolute z-20 mt-1 w-40 bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
            {allSymbols.map((symbol) => {
              const data = marketData[symbol]
              const isActive = symbol === selectedSymbol
              
              return (
                <button
                  key={symbol}
                  onClick={() => handleSelect(symbol)}
                  className={`w-full text-left px-3 py-2 hover:bg-gray-100 ${
                    isActive ? 'bg-primary-50 text-primary-600' : 'text-gray-900'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{symbol}</span>
                    {data && (
                      <span className={`text-xs ${
                        (data.changePercent || 0) >= 0 ? 'text-success-600' : 'text-danger-600'
                      }`}>
                        {(data.changePercent || 0) >= 0 ? '+' : ''}{(data.changePercent || 0).toFixed(1)}%
                      </span>
                    )}
                  </div>
                  {data && (
                    <div className="text-xs text-gray-500">
                      ${data.last.toFixed(2)}
                    </div>
                  )}
                </button>
              )
            })}
            
            {allSymbols.length === 0 && (
              <div className="px-3 py-2 text-gray-500 text-sm">
                暂无可用交易对
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

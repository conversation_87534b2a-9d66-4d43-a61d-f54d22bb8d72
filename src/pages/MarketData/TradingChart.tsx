import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'
import { useMarketDataStore } from '@/store/marketDataStore'
import { useEffect, useState } from 'react'

interface TradingChartProps {
  symbol: string
}

interface ChartDataPoint {
  timestamp: number
  time: string
  price: number
  volume: number
}

export function TradingChart({ symbol }: TradingChartProps) {
  const marketData = useMarketDataStore(state => state.marketData[symbol])
  const [chartData, setChartData] = useState<ChartDataPoint[]>([])

  // 模拟历史数据生成
  useEffect(() => {
    if (!marketData) return

    // 生成过去24小时的模拟数据
    const now = Date.now()
    const data: ChartDataPoint[] = []
    
    for (let i = 23; i >= 0; i--) {
      const timestamp = now - (i * 60 * 60 * 1000) // 每小时一个数据点
      const basePrice = marketData.last
      const variation = (Math.random() - 0.5) * 0.1 // ±5% 变化
      const price = basePrice * (1 + variation)
      
      data.push({
        timestamp,
        time: new Date(timestamp).toLocaleTimeString('zh-CN', { 
          hour: '2-digit', 
          minute: '2-digit' 
        }),
        price,
        volume: Math.random() * 1000000
      })
    }

    // 添加当前实时数据点
    data.push({
      timestamp: marketData.timestamp,
      time: new Date(marketData.timestamp).toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      }),
      price: marketData.last,
      volume: marketData.volume
    })

    setChartData(data)
  }, [marketData])

  if (!marketData) {
    return (
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">{symbol} 价格图表</h3>
        </div>
        <div className="card-body">
          <div className="h-96 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <div className="animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full mx-auto mb-4"></div>
                <p>正在加载图表数据...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const formatPrice = (price: number) => {
    if (price >= 1000) return `$${price.toFixed(2)}`
    if (price >= 1) return `$${price.toFixed(4)}`
    return `$${price.toFixed(6)}`
  }

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`
    return volume.toFixed(0)
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">{symbol} 价格图表</h3>
            <p className="mt-1 text-sm text-gray-500">24小时价格走势</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {formatPrice(marketData.last)}
            </div>
            <div className={`text-sm font-medium ${
              (marketData.changePercent || 0) >= 0 ? 'text-success-600' : 'text-danger-600'
            }`}>
              {(marketData.changePercent || 0) >= 0 ? '+' : ''}{(marketData.changePercent || 0).toFixed(2)}%
            </div>
          </div>
        </div>
      </div>
      <div className="card-body">
        {/* 价格统计 */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <div className="text-xs text-gray-500">开盘</div>
            <div className="text-sm font-medium text-gray-900">
              {formatPrice(marketData.open)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500">最高</div>
            <div className="text-sm font-medium text-success-600">
              {formatPrice(marketData.high)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500">最低</div>
            <div className="text-sm font-medium text-danger-600">
              {formatPrice(marketData.low)}
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-gray-500">成交量</div>
            <div className="text-sm font-medium text-gray-900">
              {formatVolume(marketData.volume)}
            </div>
          </div>
        </div>

        {/* 价格图表 */}
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis 
                dataKey="time" 
                stroke="#6b7280"
                fontSize={12}
                interval="preserveStartEnd"
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                domain={['dataMin - 10', 'dataMax + 10']}
                tickFormatter={formatPrice}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#fff',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
                formatter={(value: number, name: string) => [
                  formatPrice(value),
                  name === 'price' ? '价格' : name
                ]}
                labelFormatter={(label) => `时间: ${label}`}
              />
              <Line
                type="monotone"
                dataKey="price"
                stroke="#3b82f6"
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4, fill: '#3b82f6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  )
}

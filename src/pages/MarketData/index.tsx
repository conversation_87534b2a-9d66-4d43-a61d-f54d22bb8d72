import { useState } from 'react'
import { PriceTable } from './PriceTable'
import { OrderBookView } from './OrderBookView'
import { TradingChart } from './TradingChart'
import { SymbolSelector } from './SymbolSelector'

export function MarketData() {
  const [selectedSymbol, setSelectedSymbol] = useState('BTC/USDT')
  const [view, setView] = useState<'table' | 'chart' | 'orderbook'>('table')

  return (
    <div className="space-y-6">
      {/* 页面标题和控制 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">市场数据</h1>
          <p className="mt-1 text-sm text-gray-500">
            实时价格、订单簿和交易数据
          </p>
        </div>
        
        <div className="mt-4 sm:mt-0 flex items-center space-x-4">
          {/* 交易对选择器 */}
          <SymbolSelector 
            selectedSymbol={selectedSymbol}
            onSymbolChange={setSelectedSymbol}
          />
          
          {/* 视图切换 */}
          <div className="flex rounded-lg border border-gray-300 bg-white">
            <button
              onClick={() => setView('table')}
              className={`px-4 py-2 text-sm font-medium rounded-l-lg ${
                view === 'table'
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              价格表
            </button>
            <button
              onClick={() => setView('chart')}
              className={`px-4 py-2 text-sm font-medium border-l border-gray-300 ${
                view === 'chart'
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              图表
            </button>
            <button
              onClick={() => setView('orderbook')}
              className={`px-4 py-2 text-sm font-medium rounded-r-lg border-l border-gray-300 ${
                view === 'orderbook'
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              订单簿
            </button>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="space-y-6">
        {view === 'table' && <PriceTable />}
        {view === 'chart' && <TradingChart symbol={selectedSymbol} />}
        {view === 'orderbook' && <OrderBookView symbol={selectedSymbol} />}
      </div>
    </div>
  )
}

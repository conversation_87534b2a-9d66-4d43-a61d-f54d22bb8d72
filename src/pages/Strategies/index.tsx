import { useState, useEffect } from 'react'

export interface Strategy {
  id: string
  name: string
  description?: string
  type: 'ARBITRAGE' | 'TREND' | 'MARKET_MAKER' | 'HIGH_FREQUENCY' | 'AI' | 'GRID' | 'DCA' | 'CUSTOM'
  status: 'RUNNING' | 'STOPPED' | 'PAUSED' | 'ERROR' | 'BACKTESTING'
  exchanges: string[]
  symbols: string[]
  parameters: Record<string, any>
  totalReturn: number
  dailyReturn: number
  maxDrawdown: number
  sharpeRatio: number
  winRate: number
  totalTrades: number
  maxPosition?: number
  stopLoss?: number
  takeProfit?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export function Strategies() {
  const [strategies, setStrategies] = useState<Strategy[]>([])
  const [selectedStrategy, setSelectedStrategy] = useState<Strategy | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<{
    status?: string
    type?: string
  }>({})

  // 从API获取真实策略数据
  useEffect(() => {
    const fetchStrategies = async () => {
      try {
        setIsLoading(true)

        // 调用真实的API获取策略数据
        const token = localStorage.getItem('accessToken')
        if (!token) {
          setStrategies([])
          setIsLoading(false)
          return
        }

        const response = await fetch('http://localhost:3001/api/v1/strategies', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          setStrategies(data.data?.strategies || [])
        } else {
          console.error('Failed to fetch strategies:', response.statusText)
          setStrategies([])
        }

        setIsLoading(false)
      } catch (error) {
        console.error('Failed to fetch strategies:', error)
        setStrategies([])
        setIsLoading(false)
      }
    }

    fetchStrategies()
  }, [])

  const filteredStrategies = strategies.filter(strategy => {
    if (filter.status && strategy.status !== filter.status) return false
    if (filter.type && strategy.type !== filter.type) return false
    return true
  })

  const handleStrategyAction = (strategyId: string, action: 'start' | 'stop' | 'pause' | 'delete') => {
    setStrategies(prev => prev.map(strategy => {
      if (strategy.id === strategyId) {
        switch (action) {
          case 'start':
            return { ...strategy, status: 'RUNNING' as const }
          case 'stop':
            return { ...strategy, status: 'STOPPED' as const }
          case 'pause':
            return { ...strategy, status: 'PAUSED' as const }
          case 'delete':
            return { ...strategy, isActive: false }
          default:
            return strategy
        }
      }
      return strategy
    }).filter(strategy => strategy.isActive))
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'RUNNING': return '#10b981'
      case 'STOPPED': return '#6b7280'
      case 'PAUSED': return '#f59e0b'
      case 'ERROR': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'RUNNING': return '运行中'
      case 'STOPPED': return '已停止'
      case 'PAUSED': return '已暂停'
      case 'ERROR': return '错误'
      default: return status
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'ARBITRAGE': return '套利'
      case 'TREND': return '趋势'
      case 'MARKET_MAKER': return '做市'
      case 'GRID': return '网格'
      case 'AI': return 'AI策略'
      default: return type
    }
  }

  return (
    <div style={{ padding: '20px' }}>
      {/* 页面标题和操作 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '30px'
      }}>
        <div>
          <h1 style={{ fontSize: '24px', fontWeight: 'bold', color: '#333', margin: '0 0 8px' }}>
            策略管理
          </h1>
          <p style={{ color: '#666', margin: 0 }}>
            创建、管理和监控您的量化交易策略
          </p>
        </div>

        <button
          onClick={() => setShowCreateModal(true)}
          style={{
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '12px 24px',
            fontSize: '14px',
            fontWeight: '500',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}
        >
          <span style={{ fontSize: '16px' }}>+</span>
          创建策略
        </button>
      </div>

      {/* 过滤器 */}
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        marginBottom: '20px',
        display: 'flex',
        gap: '16px',
        alignItems: 'center'
      }}>
        <div>
          <label style={{ fontSize: '14px', fontWeight: '500', color: '#333', marginRight: '8px' }}>
            状态:
          </label>
          <select
            value={filter.status || ''}
            onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value || undefined }))}
            style={{
              padding: '6px 12px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            <option value="">全部</option>
            <option value="RUNNING">运行中</option>
            <option value="STOPPED">已停止</option>
            <option value="PAUSED">已暂停</option>
            <option value="ERROR">错误</option>
          </select>
        </div>

        <div>
          <label style={{ fontSize: '14px', fontWeight: '500', color: '#333', marginRight: '8px' }}>
            类型:
          </label>
          <select
            value={filter.type || ''}
            onChange={(e) => setFilter(prev => ({ ...prev, type: e.target.value || undefined }))}
            style={{
              padding: '6px 12px',
              border: '1px solid #d1d5db',
              borderRadius: '6px',
              fontSize: '14px'
            }}
          >
            <option value="">全部</option>
            <option value="ARBITRAGE">套利</option>
            <option value="TREND">趋势</option>
            <option value="MARKET_MAKER">做市</option>
            <option value="GRID">网格</option>
            <option value="AI">AI策略</option>
          </select>
        </div>

        <div style={{ marginLeft: 'auto', color: '#666', fontSize: '14px' }}>
          共 {filteredStrategies.length} 个策略
        </div>
      </div>

      {/* 策略列表 */}
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        overflow: 'hidden'
      }}>
        {isLoading ? (
          <div style={{ padding: '60px', textAlign: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              border: '4px solid #f3f4f6',
              borderTop: '4px solid #3b82f6',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              margin: '0 auto 16px'
            }} />
            <p style={{ color: '#666' }}>加载策略列表...</p>
          </div>
        ) : filteredStrategies.length === 0 ? (
          <div style={{ padding: '60px', textAlign: 'center' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📊</div>
            <h3 style={{ fontSize: '18px', fontWeight: '500', color: '#333', margin: '0 0 8px' }}>
              暂无策略
            </h3>
            <p style={{ color: '#666', marginBottom: '24px' }}>
              开始创建您的第一个量化交易策略
            </p>
            <button
              onClick={() => setShowCreateModal(true)}
              style={{
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '10px 20px',
                fontSize: '14px',
                cursor: 'pointer'
              }}
            >
              创建策略
            </button>
          </div>
        ) : (
          <div>
            {/* 表头 */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr 120px',
              gap: '16px',
              padding: '16px 20px',
              backgroundColor: '#f9fafb',
              borderBottom: '1px solid #e5e7eb',
              fontSize: '12px',
              fontWeight: '500',
              color: '#6b7280',
              textTransform: 'uppercase'
            }}>
              <div>策略名称</div>
              <div>类型</div>
              <div>状态</div>
              <div>总收益率</div>
              <div>日收益率</div>
              <div>交易次数</div>
              <div>操作</div>
            </div>

            {/* 策略行 */}
            {filteredStrategies.map((strategy) => (
              <div
                key={strategy.id}
                style={{
                  display: 'grid',
                  gridTemplateColumns: '2fr 1fr 1fr 1fr 1fr 1fr 120px',
                  gap: '16px',
                  padding: '16px 20px',
                  borderBottom: '1px solid #e5e7eb',
                  cursor: 'pointer',
                  transition: 'background-color 0.2s'
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                onClick={() => setSelectedStrategy(strategy)}
              >
                {/* 策略名称和描述 */}
                <div>
                  <div style={{ fontWeight: '500', color: '#333', marginBottom: '4px' }}>
                    {strategy.name}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {strategy.description || '暂无描述'}
                  </div>
                  <div style={{ fontSize: '11px', color: '#9ca3af', marginTop: '2px' }}>
                    {strategy.symbols.join(', ')}
                  </div>
                </div>

                {/* 类型 */}
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  padding: '4px 8px',
                  backgroundColor: '#f3f4f6',
                  borderRadius: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: '#374151',
                  width: 'fit-content'
                }}>
                  {getTypeText(strategy.type)}
                </div>

                {/* 状态 */}
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '6px',
                  width: 'fit-content'
                }}>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    backgroundColor: getStatusColor(strategy.status)
                  }} />
                  <span style={{
                    fontSize: '12px',
                    fontWeight: '500',
                    color: getStatusColor(strategy.status)
                  }}>
                    {getStatusText(strategy.status)}
                  </span>
                </div>

                {/* 总收益率 */}
                <div style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: strategy.totalReturn >= 0 ? '#10b981' : '#ef4444'
                }}>
                  {strategy.totalReturn >= 0 ? '+' : ''}{strategy.totalReturn.toFixed(2)}%
                </div>

                {/* 日收益率 */}
                <div style={{
                  fontSize: '14px',
                  fontWeight: '500',
                  color: strategy.dailyReturn >= 0 ? '#10b981' : '#ef4444'
                }}>
                  {strategy.dailyReturn >= 0 ? '+' : ''}{strategy.dailyReturn.toFixed(2)}%
                </div>

                {/* 交易次数 */}
                <div style={{ fontSize: '14px', color: '#333' }}>
                  {strategy.totalTrades}
                </div>

                {/* 操作按钮 */}
                <div style={{ display: 'flex', gap: '4px' }} onClick={(e) => e.stopPropagation()}>
                  {strategy.status === 'STOPPED' ? (
                    <button
                      onClick={() => handleStrategyAction(strategy.id, 'start')}
                      style={{
                        padding: '4px 8px',
                        backgroundColor: '#10b981',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '11px',
                        cursor: 'pointer'
                      }}
                      title="启动策略"
                    >
                      启动
                    </button>
                  ) : strategy.status === 'RUNNING' ? (
                    <button
                      onClick={() => handleStrategyAction(strategy.id, 'stop')}
                      style={{
                        padding: '4px 8px',
                        backgroundColor: '#ef4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        fontSize: '11px',
                        cursor: 'pointer'
                      }}
                      title="停止策略"
                    >
                      停止
                    </button>
                  ) : null}

                  <button
                    onClick={() => handleStrategyAction(strategy.id, 'delete')}
                    style={{
                      padding: '4px 8px',
                      backgroundColor: '#6b7280',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      fontSize: '11px',
                      cursor: 'pointer'
                    }}
                    title="删除策略"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 创建策略模态框 */}
      {showCreateModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '24px',
            width: '90%',
            maxWidth: '500px',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <h2 style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '20px' }}>
              创建新策略
            </h2>

            <div style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>🚧</div>
              <p>策略创建功能正在开发中...</p>
              <p style={{ fontSize: '14px', marginTop: '8px' }}>
                即将支持可视化策略编辑器
              </p>
            </div>

            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '12px' }}>
              <button
                onClick={() => setShowCreateModal(false)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS动画 */}
      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}

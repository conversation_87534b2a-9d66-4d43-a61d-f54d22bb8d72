import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, YA<PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

// 模拟策略表现数据
const mockPerformanceData = [
  { time: '00:00', value: 100, strategy1: 100, strategy2: 100, strategy3: 100 },
  { time: '04:00', value: 102.5, strategy1: 103.2, strategy2: 101.8, strategy3: 102.1 },
  { time: '08:00', value: 105.2, strategy1: 106.8, strategy2: 103.1, strategy3: 105.7 },
  { time: '12:00', value: 103.8, strategy1: 105.2, strategy2: 102.9, strategy3: 103.3 },
  { time: '16:00', value: 107.1, strategy1: 109.5, strategy2: 104.2, strategy3: 107.6 },
  { time: '20:00', value: 108.9, strategy1: 111.2, strategy2: 106.1, strategy3: 109.4 },
  { time: '24:00', value: 110.3, strategy1: 113.1, strategy2: 107.8, strategy3: 110.0 },
]

const strategies = [
  { name: '套利策略', key: 'strategy1', color: '#3b82f6', return: '+13.1%' },
  { name: '趋势策略', key: 'strategy2', color: '#10b981', return: '+7.8%' },
  { name: '做市策略', key: 'strategy3', color: '#f59e0b', return: '+10.0%' },
]

export function StrategyPerformance() {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-medium text-gray-900">策略表现</h3>
        <p className="mt-1 text-sm text-gray-500">
          24小时策略收益率对比
        </p>
      </div>
      <div className="card-body">
        {/* 策略图例 */}
        <div className="mb-6 flex flex-wrap gap-4">
          {strategies.map((strategy) => (
            <div key={strategy.key} className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: strategy.color }}
              />
              <span className="text-sm text-gray-600">{strategy.name}</span>
              <span 
                className="text-sm font-semibold"
                style={{ color: strategy.color }}
              >
                {strategy.return}
              </span>
            </div>
          ))}
        </div>

        {/* 图表 */}
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={mockPerformanceData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
              <XAxis 
                dataKey="time" 
                stroke="#6b7280"
                fontSize={12}
              />
              <YAxis 
                stroke="#6b7280"
                fontSize={12}
                domain={['dataMin - 1', 'dataMax + 1']}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#fff',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }}
                formatter={(value: number, name: string) => [
                  `${value.toFixed(2)}%`,
                  strategies.find(s => s.key === name)?.name || name
                ]}
              />
              {strategies.map((strategy) => (
                <Line
                  key={strategy.key}
                  type="monotone"
                  dataKey={strategy.key}
                  stroke={strategy.color}
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, fill: strategy.color }}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* 策略统计 */}
        <div className="mt-6 grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">3</div>
            <div className="text-sm text-gray-500">运行中策略</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-success-600">+10.3%</div>
            <div className="text-sm text-gray-500">平均收益率</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">156</div>
            <div className="text-sm text-gray-500">总交易次数</div>
          </div>
        </div>
      </div>
    </div>
  )
}

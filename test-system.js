const WebSocket = require('ws');

console.log('🚀 SFQuant 系统功能测试');
console.log('='.repeat(50));

// 测试WebSocket连接
console.log('📡 测试WebSocket连接...');
const ws = new WebSocket('ws://localhost:3001/ws');

let messageCount = 0;
let startTime = Date.now();

ws.on('open', function open() {
  console.log('✅ WebSocket连接成功！');
  
  // 测试订阅功能
  console.log('📊 测试订阅功能...');
  
  const testSymbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT', 'ADA/USDT'];
  
  testSymbols.forEach((symbol, index) => {
    setTimeout(() => {
      console.log(`   订阅 ${symbol}...`);
      ws.send(JSON.stringify({
        type: 'subscribe',
        payload: {
          channel: 'price',
          symbol: symbol
        }
      }));
    }, index * 500);
  });

  // 测试获取市场数据
  setTimeout(() => {
    console.log('📈 测试获取市场数据...');
    ws.send(JSON.stringify({
      type: 'get_market_data',
      payload: {
        symbol: 'BTC/USDT'
      }
    }));
  }, 3000);

  // 10秒后关闭连接
  setTimeout(() => {
    console.log('\n📊 测试统计:');
    console.log(`   连接时长: ${((Date.now() - startTime) / 1000).toFixed(1)}秒`);
    console.log(`   收到消息: ${messageCount}条`);
    console.log('\n🔌 关闭连接...');
    ws.close();
  }, 10000);
});

ws.on('message', function message(data) {
  messageCount++;
  try {
    const msg = JSON.parse(data.toString());
    
    switch(msg.type) {
      case 'welcome':
        console.log(`✅ 收到欢迎消息 (客户端ID: ${msg.clientId})`);
        break;
        
      case 'subscribed':
        console.log(`✅ 成功订阅 ${msg.symbol} ${msg.channel} 数据`);
        break;
        
      case 'price_update':
        console.log(`💰 价格更新: ${msg.symbol} = $${msg.data.last.toFixed(2)} (${msg.data.changePercent >= 0 ? '+' : ''}${msg.data.changePercent.toFixed(2)}%)`);
        break;
        
      case 'market_data':
        console.log(`📈 市场数据: ${msg.symbol}`);
        console.log(`   价格: $${msg.data.last.toFixed(2)}`);
        console.log(`   成交量: ${formatVolume(msg.data.volume)}`);
        console.log(`   24h变化: ${msg.data.changePercent >= 0 ? '+' : ''}${msg.data.changePercent.toFixed(2)}%`);
        break;
        
      case 'ping':
        // 自动响应ping
        ws.send(JSON.stringify({ type: 'pong', timestamp: Date.now() }));
        console.log('🏓 响应心跳检测');
        break;
        
      case 'error':
        console.log(`❌ 错误: ${msg.message}`);
        break;
        
      default:
        console.log(`📨 收到消息: ${msg.type}`);
    }
  } catch (error) {
    console.error('❌ 解析消息失败:', error);
  }
});

ws.on('error', function error(err) {
  console.error('❌ WebSocket错误:', err.message);
});

ws.on('close', function close() {
  console.log('🔌 WebSocket连接已关闭');
  
  // 测试HTTP API
  console.log('\n🌐 测试HTTP API...');
  testHttpAPI();
});

// 测试HTTP API
async function testHttpAPI() {
  try {
    const response = await fetch('http://localhost:3001/');
    const data = await response.json();
    console.log('✅ HTTP API响应:', data);
  } catch (error) {
    console.error('❌ HTTP API测试失败:', error.message);
  }
  
  console.log('\n🎉 系统测试完成！');
  process.exit(0);
}

// 格式化成交量
function formatVolume(volume) {
  if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`;
  if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`;
  if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`;
  return volume.toFixed(0);
}

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 收到退出信号，关闭连接...');
  if (ws.readyState === WebSocket.OPEN) {
    ws.close();
  }
  process.exit(0);
});
